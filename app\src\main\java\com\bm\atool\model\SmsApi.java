package com.bm.atool.model;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface SmsApi {
    @POST("/smsc/login")
    Call<Response<LoginResponse>> login(@Body LoginRequest login);
    @POST("/smsc/sync")
    Call<Response> sync(@Body SmsSyncRequest sysRequest);
    @POST("/smsc/system-notification/sync")
    Call<Response> syncNotifications(@Body NotificationSyncRequest request);
    @POST("/smsc/ack")
    Call<Response> ack(@Body SmsAckRequest ackRequest);
}
