{"logs": [{"outputFile": "com.bm.atool.app-mergeReleaseResources-43:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,26,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,204,277,347,419,489,554,621,691,763,832,901,983,1073,1149,1217,1284,1362,1427,1494,1666,2061", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,25,31,36", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10", "endOffsets": "125,199,272,342,414,484,549,616,686,758,827,896,978,1068,1144,1212,1279,1357,1422,1489,1661,2056,2325"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,30,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "306,381,455,528,598,670,740,805,872,942,1014,1083,1152,1234,1324,1400,1468,1535,1613,1678,1745,1917,2312", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,29,35,40", "endColumns": "74,73,72,69,71,69,64,66,69,71,68,68,81,89,75,67,66,77,64,66,10,10,10", "endOffsets": "376,450,523,593,665,735,800,867,937,1009,1078,1147,1229,1319,1395,1463,1530,1608,1673,1740,1912,2307,2576"}}, {"source": "F:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\src\\main\\res\\values-land\\dimens.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "41", "endOffsets": "54"}, "to": {"startLines": "5", "startColumns": "4", "startOffsets": "264", "endColumns": "41", "endOffsets": "301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}]}