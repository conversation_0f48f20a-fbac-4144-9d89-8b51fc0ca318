package com.bm.atool.ui.views;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.core.content.ContextCompat;

import com.bm.atool.R;

public class BlackUnderlineEditText extends AppCompatEditText {

    private int defaultColor = Color.BLACK;
    private int focusedColor;

    public BlackUnderlineEditText(Context context) {
        super(context);
        init(context);
    }

    public BlackUnderlineEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public BlackUnderlineEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        focusedColor = ContextCompat.getColor(context, R.color.green);
        
        // 设置下划线颜色状态列表
        ColorStateList colorStateList = new ColorStateList(
                new int[][] {
                        new int[] {android.R.attr.state_focused}, // 聚焦状态
                        new int[] {} // 默认状态
                },
                new int[] {
                        focusedColor,   // 聚焦颜色-绿色
                        defaultColor    // 默认颜色-黑色
                }
        );
        
        // 适配不同的Android版本
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            setBackgroundTintList(colorStateList);
        } else {
            Drawable drawable = getBackground();
            if (drawable != null) {
                drawable.setColorFilter(defaultColor, PorterDuff.Mode.SRC_ATOP);
            }
        }
        
        // 设置焦点变化监听器
        setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                Drawable drawable = getBackground();
                if (drawable != null) {
                    if (hasFocus) {
                        drawable.setColorFilter(focusedColor, PorterDuff.Mode.SRC_ATOP);
                    } else {
                        drawable.setColorFilter(defaultColor, PorterDuff.Mode.SRC_ATOP);
                    }
                    invalidate();
                }
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        // 确保初始状态下显示黑色下划线
        Drawable drawable = getBackground();
        if (drawable != null && !isFocused()) {
            drawable.setColorFilter(defaultColor, PorterDuff.Mode.SRC_ATOP);
        }
    }
} 