package com.bm.atool.service;


import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.VpnService;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.ParcelFileDescriptor;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.bm.atool.Env;
import com.bm.atool.events.EventSource;
import com.bm.atool.receivers.AccessibilityReceiver;
import com.bm.atool.receivers.WatchDogStatusReceiver;
import com.bm.atool.service.singlepixel.ScreenReceiverUtil;
import com.bm.atool.utils.PhoneUtils;
import com.bm.atool.SmsSender;
import com.bm.atool.Sys;
import com.bm.atool.model.PingModel;
import com.bm.atool.model.PongModel;
import com.bm.atool.model.SendSmsRequest;
import com.bm.atool.ui.FloatingWindow;
import com.google.gson.Gson;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;
import io.socket.client.IO;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;
import java.net.URI;

public final class SocketService extends VpnService {
    private BatteryManager batteryManager;
    private static int counter = 0;
    private String token;
    private static final String TAG = SocketService.class.getSimpleName();
    TimerTask timerTask;
    private  FloatingWindow floatingWindow = null;


    private ParcelFileDescriptor vpnInterface;
//    private SmsSendingReceiver smsSendingReceiver = new SmsSendingReceiver();

    // SocketIO
    private IO.Options options = new IO.Options();
    private Socket socket = null;
    private Timer timer = null;

    private StartWatchReceiver startWatchReceiver = new StartWatchReceiver();
    private AccessibilityReceiver accessibilityReceiver = new AccessibilityReceiver();
    private BaseServiceConnection mConnection = new BaseServiceConnection() {

        @Override
        public void onDisconnected(ComponentName name) {
            if (needStartWorkService()) {
                Sys.startServiceMayBind(SocketService.this, WatchDogService.class, mConnection);
            }
        }

    };

    private EventSource.IEventListener<Boolean> watchDogEventListener = new EventSource.IEventListener<Boolean>() {
        @Override
        public void onEvent(Boolean enabled) {
            if(!enabled){
                Sys.safelyUnbindService(SocketService.this,mConnection);
                stopSelf();
            }
        }
    };
//
//    private final BroadcastReceiver accessibilityReceiver = new BroadcastReceiver() {
//        @Override // android.content.BroadcastReceiver
//        public void onReceive(@Nullable Context context, @Nullable Intent intent) {
//            if(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED.equals(intent.getAction())){
//                Sys.updateAccessibilityEnabled(intent.getBooleanExtra(NotificationCompat.CATEGORY_STATUS, false));
//            }
////            try {
////                final Message message = Message.obtain(null, SocketService.SOCKET_CMD_UPDATE_FLOATINGWINDOW, null);
////                messenger.send(message);
////            } catch (RemoteException e) {
////                e.printStackTrace();
////            }
////            if(PermissionUtils.isAccessibilityEnabled){
////
////                try{
////                    Log.e(TAG, "restart floating window...");
////                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
////                        context.getMainExecutor().execute(new Runnable() {
////                            @Override
////                            public void run() {
////                                Log.e(TAG, "executing floating window...");
////                                floatingWindow.close();
////                                floatingWindow.show();
////                            }
////                        });
////                    }
////                }
////                catch (Exception ex){
////                    ex.printStackTrace();
////                }
////            }
//        }
//    };

    public SocketService() {
        Log.e(TAG, "SocketService ctor");
        options.path = "/smsc/ws";
        options.reconnection = true;
    }

    private PingModel makePing(){
        PingModel ping = new PingModel();
        try{
            ping.power = this.batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
        }
        catch (Exception ex){
            ping.power = -1;
        }
        ping.brand = Build.BRAND;
        ping.model = Build.MODEL;
        ping.phones = PhoneUtils.getPhones(this);
        return ping;
    }

    public static final int SOCKET_CMD_START = 1;
    public static final int SOCKET_CMD_UPDATE_FLOATINGWINDOW = 2;
    private class IncomingHandler extends Handler {
        @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
        @Override
        public void handleMessage(Message message) {
            switch (message.what) {
                case SOCKET_CMD_START:{
                    Log.d(TAG, "on message: SOCKET_CMD_START");
                    startSocket();
                }
                case SOCKET_CMD_UPDATE_FLOATINGWINDOW:{
//                    if(PermissionUtils.isAccessibilityEnabled){
                        try{
                            Log.e(TAG, "executing floating window...");
                            floatingWindow.close();
                            floatingWindow.show();
                        }
                        catch (Exception ex){
                            ex.printStackTrace();
                        }
//                    }
                }
                default:
                    return;  // message not understood, ignore
            }
        }
    }

    final private Messenger messenger = new Messenger(new IncomingHandler());

    @Override
    public IBinder onBind(Intent intent) {
        return messenger.getBinder();
    }

    @Override
    public void onCreate() {
        Log.e(TAG, "SocketService created: " + String.valueOf(this.hashCode()));
        super.onCreate();
        this.batteryManager = (BatteryManager) this.getSystemService(Context.BATTERY_SERVICE);

        floatingWindow = new FloatingWindow(this);


        Sys.registerReceiver(this, this.accessibilityReceiver,new IntentFilter(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED));
        Sys.registerReceiver(this,this.startWatchReceiver,new IntentFilter(Sys.ACTION_DAEMON_ENABLED));
        Sys.watchDogEventSource.addEventListener(watchDogEventListener);
        if (!needStartWorkService()) {
            stopSelf();
        }else {
            Log.d("wsh-daemon", "AbsWorkService  onCreate 启动。。。。");
            createScreenListener();
            getPackageManager().setComponentEnabledSetting(new ComponentName(getPackageName(), WatchDogService.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        }

    }
    @Override
    public void onTaskRemoved(Intent rootIntent) {
        restartServiceIfNeed();
    }
    private long currentInterval = 0l;
    private void updatePing(long delay){
        if(currentInterval == delay && Objects.nonNull(timer)){
            return;
        }
        if(!Objects.isNull(timer)){
            timer.cancel();
            timer = null;
        }
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                if(Objects.isNull(socket) || !socket.connected()){
                    return;
                }
                Log.d(TAG, "current delay:" + String.valueOf(currentInterval));
                socket.emit("p",new Gson().toJson(makePing()));
            }
        };
        timer = new Timer(true);
        timer.scheduleAtFixedRate(task,1000L,delay);
        currentInterval = delay;
    }
    private  void startSocket(){
        this.closeSocket();
//        if(Objects.isNull(this.token) || this.token.length() < 1){
//            return;
//        }
        options.query = "token=" + URLEncoder.encode(Sys.getToken());
        socket = IO.socket(URI.create(Env.API_HOME), options);

        socket.on(Socket.EVENT_CONNECT, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "socket connnected");
                updatePing(10000L);
            }
        });
        socket.on(Socket.EVENT_CONNECT_ERROR, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "socket connnect error:" + args[0]);
            }
        });
        socket.on(Socket.EVENT_DISCONNECT, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "socket disconnected:" + args[0]);
            }
        });

        socket.on("p", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "on pong:" + args[0]);
                PongModel pongModel = new Gson().fromJson((String)args[0], PongModel.class);
                if(pongModel.ttl > 1){
                    Log.d(TAG, "change ttl:" + String.valueOf(pongModel.ttl));
                    updatePing(pongModel.ttl);

                    Intent intent = new Intent(Sys.ACTION_SOCKET_PONG);
                    intent.putExtra("ttl", pongModel.ttl);
                    sendBroadcast(intent);
                }
            }
        });
        socket.on("s", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.e(TAG, "on send text:" + args[0].toString());
                if(Objects.isNull(args) || args.length < 1 || !(args[0] instanceof String)){
                    return;
                }
                String str = (String)args[0];
                SendSmsRequest sendSmsRequest = new Gson().fromJson(str, SendSmsRequest.class );
                SmsSender.sendSMS(sendSmsRequest);
//                JsonObject jsonObject = (JsonObject)args[0];
//                String from = jsonObject.get("from").getAsString();
//                String to = jsonObject.get("to").getAsString();
//                String content = jsonObject.get("content").getAsString();
//                SmsSender.sendSMS(from,to,content);
            }
        });

        socket.connect();
    }
    private void closeSocket(){
        if(!Objects.isNull(this.socket)){
            try {
                this.socket.disconnect();
            }
            catch (Exception ex){
                Log.d(TAG, "closeSocket error: " + ex.getMessage());
            }
            this.socket = null;
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "onStartCommand");
        super.onStartCommand(intent, flags, startId);

        Sys.startServiceMayBind(SocketService.this, WatchDogService.class, mConnection);
        floatingWindow.show();

        Log.e(TAG, "start work");
        Builder builder = new Builder();

        // Configure the VPN interface
        builder.setSession("MyVpnService")
                .addAddress("*********", 24)
                .addDnsServer("*******")     // DNS server
                .addDnsServer("*******")     // Secondary DNS server
                .setMtu(1500);               // Maximum Transmission Unit

        // Establish the VPN interface
        vpnInterface = builder.establish();
        Log.e(TAG,"VPN service started");
        this.startSocket();

        return START_NOT_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "socket service destroy:"  + String.valueOf(this.hashCode()));
        if(Objects.nonNull(floatingWindow)){
            floatingWindow.close();
        }

        Sys.safelyUnbindService(SocketService.this,mConnection);
        try{
            Sys.watchDogEventSource.remvoeEventListener(watchDogEventListener);
//            ForegroundNotificationUtils.deleteForegroundNotification(this);
//        stopRegisterReceiver();
            stopScreenListener();
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
        unregisterReceiver(this.accessibilityReceiver);
        unregisterReceiver(this.startWatchReceiver);

        try {
            if(Objects.nonNull(vpnInterface)){
                vpnInterface.close();
                vpnInterface = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        restartServiceIfNeed();
//        this.unregisterReceiver(smsSendingReceiver);
//        unregisterReceiver(this.accessibilityReceiver);
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
//            NotificationManager mManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
//            mManager.cancel(NOTICE_ID);
//        }
//
//        Log.d(TAG, "DaemonService---->onDestroy，前台service被杀死");
//        // 重启自己
//        Intent intent = new Intent(getApplicationContext(), SocketService.class);
//        startService(intent);
    }

    public Boolean needStartWorkService() {
        Boolean needStart = Objects.nonNull(Sys.getToken()) && Sys.getToken().length() > 1 && Sys.watchDogEnabled;
        Log.e(TAG, "needStart:" + String.valueOf(needStart));
        return needStart;
    }
    private void restartServiceIfNeed(){
        if(needStartWorkService()){
            Sys.startServiceSafely(SocketService.this,WatchDogService.class);
        }

    }
    private ScreenReceiverUtil mScreenUtils;

    private void createScreenListener(){
        //   注册锁屏广播监听器
        mScreenUtils = new ScreenReceiverUtil(this);
        mScreenUtils.startScreenReceiverListener();
    }

    private void stopScreenListener(){
        // 取消注册
        if (mScreenUtils != null){
            mScreenUtils.stopScreenReceiverListener();
            mScreenUtils = null;
        }
    }

//    @Override
//    public void startWork() {
//    }

//    @Override
//    public void stopWork() {
//        Log.e(TAG, "stop work");
//        this.closeSocket();
//    }
//
//    @Override
//    public Boolean isWorkRunning() {
//        return Objects.nonNull(socket) ;
//    }
//
//    @NonNull
//    @Override
//    public IBinder onBindService(Intent intent, Void alwaysNull) {
//        return messenger.getBinder();
//    }
//
//    @Override
//    public void onServiceKilled() {
//        Log.e(TAG, "killed");
//        this.closeSocket();
//    }
}