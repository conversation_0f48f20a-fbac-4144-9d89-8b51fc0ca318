package com.bm.atool.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * JavaScript执行请求模型
 */
public class JavaScriptRequest implements Serializable {
    @SerializedName("id")
    public String id;
    
    @SerializedName("script")
    public String script;
    
    @SerializedName("timeout")
    public int timeout = 30; // 默认超时时间30秒
    
    @SerializedName("type")
    public String type = "execute"; // execute, load, function
    
    @SerializedName("functionName")
    public String functionName; // 当type为function时使用
    
    @SerializedName("args")
    public Object[] args; // 函数参数
    
    @SerializedName("scriptName")
    public String scriptName; // 脚本名称（用于加载已保存的脚本）
    
    @SerializedName("scriptVersion")
    public int scriptVersion = -1; // 脚本版本（-1表示最新版本）
}
