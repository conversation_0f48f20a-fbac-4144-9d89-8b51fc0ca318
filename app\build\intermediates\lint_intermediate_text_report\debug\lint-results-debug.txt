F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:87: Warning: Attribute android:inputType should not be used with <TextView>: Change element type to <EditText> ? [TextViewEdits]
                android:inputType="text"
                ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TextViewEdits":
   Using a <TextView> to input text is generally an error, you should be using
   <EditText> instead.  EditText is a subclass of TextView, and some of the
   editing support is provided by TextView, so it's possible to set some
   input-related properties on a TextView. However, using a TextView along
   with input attributes is usually a cut & paste error. To input text you
   should be using <EditText>.

   This check also checks subclasses of TextView, such as Button and CheckBox,
   since these have the same issue: they should not be used with editable
   attributes.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java:141: Warning: Consider using apply() instead; commit writes its data to persistent storage immediately, whereas apply will handle it in the background [ApplySharedPref]
        editor.commit();
               ~~~~~~~~

   Explanation for issues of type "ApplySharedPref":
   Consider using apply() instead of commit on shared preferences. Whereas
   commit blocks and writes its data to persistent storage immediately, apply
   will handle it in the background.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\js\AndroidBridge.java:49: Warning: Implicitly using the default locale is a common source of bugs: Use toUpperCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (level.toUpperCase()) {
                      ~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\DebugFragment.java:139: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String resultText = String.format(
                                    ^
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:123: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        boolean isEnabled = enabledServices.toLowerCase().contains(serviceId.toLowerCase());
                                            ~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:123: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        boolean isEnabled = enabledServices.toLowerCase().contains(serviceId.toLowerCase());
                                                                             ~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\js\ScriptScheduler.java:268: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("Task[%s] %s - %s (执行次数: %d, 成功: %d, 失败: %d)", 
                   ^
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java:867: Warning: Implicitly using the default locale is a common source of bugs: Use toUpperCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                Log.d(TAG, "获取到网络国家代码: " + networkCountryIso.toUpperCase());
                                                             ~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java:868: Warning: Implicitly using the default locale is a common source of bugs: Use toUpperCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                return networkCountryIso.toUpperCase();
                                         ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.US) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:50: Warning: Field requires API level 28 (current min is 24): android.Manifest.permission#FOREGROUND_SERVICE [InlinedApi]
        permissions.add(createPermissionModel(context,"Foreground Service", Manifest.permission.FOREGROUND_SERVICE, false));
                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:51: Warning: Field requires API level 33 (current min is 24): android.Manifest.permission#POST_NOTIFICATIONS [InlinedApi]
        permissions.add(createPermissionModel(context,"Notifications", Manifest.permission.POST_NOTIFICATIONS, false));
                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:59: Warning: Field requires API level 26 (current min is 24): android.Manifest.permission#READ_PHONE_NUMBERS [InlinedApi]
        permissions.add(createPermissionModel(context,"Read Phone Numbers", Manifest.permission.READ_PHONE_NUMBERS, false));
                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java:241: Warning: Field requires API level 33 (current min is 26): android.content.Context#RECEIVER_EXPORTED [InlinedApi]
            return context.registerReceiver(receiver, filter,RECEIVER_EXPORTED);
                                                             ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InlinedApi":
   This check scans through all the Android API field references in the
   application and flags certain constants, such as static final integers and
   Strings, which were introduced in later versions. These will actually be
   copied into the class files rather than being referenced, which means that
   the value is available even when running on older devices. In some cases
   that's fine, and in other cases it can result in a runtime crash or
   incorrect behavior. It depends on the context, so consider the code
   carefully and decide whether it's safe and can be suppressed or whether the
   code needs to be guarded.

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96: Warning: Declaring a broadcastreceiver for android.net.conn.CONNECTIVITY_CHANGE is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use WorkManager. [BatteryLife]
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110: Warning: Declaring a broadcastreceiver for android.net.conn.CONNECTIVITY_CHANGE is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use WorkManager. [BatteryLife]
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:120: Warning: Declaring a broadcastreceiver for android.net.conn.CONNECTIVITY_CHANGE is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use WorkManager. [BatteryLife]
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE"/>
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\impl\DefaultWhiteListProvider.java:56: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
                Intent dozeIntent = new Intent(ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\LoginActivity.java:100: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
                        && !p.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\MainActivity.java:350: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
        else if(permission.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)){
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:66: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
        permissions.add(createPermissionModel(context,"Battery Optimizations", Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS, false));
                                                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:78: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
        } else if(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS.equals(permission)){
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:199: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:241: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
                } else if (permission.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)) {
                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PermissionsAdapter.java:51: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
            (permissionModel.fullName != null && Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS.equals(permissionModel.fullName))) {
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\SettingsFragment.java:75: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
                } else if (permission.fullName != null && permission.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)) {
                                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java:68: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
            if(p.fullName != null && p.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "BatteryLife":
   This issue flags code that either
   * negatively affects battery life, or
   * uses APIs that have recently changed behavior to prevent background tasks
   from consuming memory and battery excessively.

   Generally, you should be using WorkManager instead.

   For more details on how to update your code, please see
   https://developer.android.com/topic/performance/background-optimization


F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\FloatingWindow.java:88: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
            floatRootView = LayoutInflater.from(this.context).inflate(R.layout.activity_float_item, null);
                                                                                                    ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteListIntentWrapper.java:74: Warning: Consider adding a <queries> declaration to your manifest when calling this method; see https://g.co/dev/packagevisibility for details [QueryPermissionsNeeded]
        List<ResolveInfo> list = pm.queryIntentActivities(mIntent, PackageManager.MATCH_DEFAULT_ONLY);
                                    ~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "QueryPermissionsNeeded":
   Apps that target Android 11 cannot query or interact with other installed
   apps by default. If you need to query or interact with other installed
   apps, you may need to add a <queries> declaration in your manifest.

   As a corollary, the methods PackageManager#getInstalledPackages and
   PackageManager#getInstalledApplications will no longer return information
   about all installed apps. To query specific apps or types of apps, you can
   use methods like PackageManager#getPackageInfo or
   PackageManager#queryIntentActivities.

   https://g.co/dev/packagevisibility

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:59: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\androidTest\java\com\bm\atool\StressTest.java:104: Error: pongReceiver is missing RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED flag for unprotected broadcasts registered for ACTION_SOCKET_PONG [UnspecifiedRegisterReceiverFlag]
        context.registerReceiver(pongReceiver, filter);
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java:244: Warning: receiver is missing RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED flag for unprotected broadcasts registered for an IntentFilter that cannot be inspected by lint [UnspecifiedRegisterReceiverFlag]
            return context.registerReceiver(receiver, filter);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnspecifiedRegisterReceiverFlag":
   In Android U, all receivers registering for non-system broadcasts are
   required to include a flag indicating the receiver's exported state. Apps
   registering for non-system broadcasts should use the
   ContextCompat#registerReceiver APIs with flags set to either
   RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED.

   If you are not expecting broadcasts from other apps on the device, register
   your receiver with RECEIVER_NOT_EXPORTED to protect your receiver on all
   platform releases.

   https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_main.xml:34: Warning: Avoid using "px" as units; use "dp" instead [PxUsage]
        app:tabIndicatorHeight="4px"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "PxUsage":
   For performance reasons and to keep the code simpler, the Android system
   uses pixels as the standard unit for expressing dimension or coordinate
   values. That means that the dimensions of a view are always expressed in
   the code using pixels, but always based on the current screen density. For
   instance, if myView.getWidth() returns 10, the view is 10 pixels wide on
   the current screen, but on a device with a higher density screen, the value
   returned might be 15. If you use pixel values in your application code to
   work with bitmaps that are not pre-scaled for the current screen density,
   you might need to scale the pixel values that you use in your code to match
   the un-scaled bitmap source.

   https://developer.android.com/guide/practices/screens_support.html#screen-independence

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml:25: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
    android:tint="@color/check_mark_color"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PhoneUtils.java:43: Warning: Using getSubscriberId to get device identifiers is not recommended [HardwareIds]
            if (telephonyManager.getSubscriberId() != null) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java:165: Warning: Using getString to get device identifiers is not recommended [HardwareIds]
        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\model\SubscriptionInfoModel.java:126: Warning: Using getSubscriberId to get device identifiers is not recommended [HardwareIds]
            subscriptionInfoModel.subscriptionId = String.valueOf(telephonyManager.getSubscriberId());
                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\model\SubscriptionInfoModel.java:142: Warning: Using getSimSerialNumber to get device identifiers is not recommended [HardwareIds]
            subscriptionInfoModel.iccId = telephonyManager.getSimSerialNumber();
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\model\SubscriptionInfoModel.java:157: Warning: Using getLine1Number to get device identifiers is not recommended [HardwareIds]
            subscriptionInfoModel.phoneNumber = telephonyManager.getLine1Number();
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardwareIds":
   Using these device identifiers is not recommended other than for high value
   fraud prevention and advanced telephony use-cases. For advertising
   use-cases, use AdvertisingIdClient$Info#getId and for analytics, use
   InstanceId#getId.

   https://developer.android.com/training/articles/user-data-ids.html

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\receivers\SimChangedReceiver.java:13: Warning: This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver's onReceive method does not appear to call getAction to ensure that the received Intent's action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored. [UnsafeProtectedBroadcastReceiver]
    public void onReceive(Context context, Intent intent) {
                ~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\receivers\SmsReceiver.java:31: Warning: This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver's onReceive method does not appear to call getAction to ensure that the received Intent's action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored. [UnsafeProtectedBroadcastReceiver]
    public void onReceive(Context context,  Intent intent) {
                ~~~~~~~~~

   Explanation for issues of type "UnsafeProtectedBroadcastReceiver":
   `BroadcastReceiver`s that declare an intent-filter for a
   protected-broadcast action string must check that the received intent's
   action string matches the expected value, otherwise it is possible for
   malicious actors to spoof intents.

   https://goo.gle/UnsafeProtectedBroadcastReceiver

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140: Warning: Exported service does not require permission [ExportedService]
        <service
         ~~~~~~~

   Explanation for issues of type "ExportedService":
   Exported services (services which either set exported=true or contain an
   intent-filter and do not specify exported=false) should define a permission
   that an entity must have in order to launch the service or bind to it.
   Without this, any application can use this service.

   https://goo.gle/ExportedService

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15: Warning: Did you mean android.permission.WRITE_OBB? [SystemPermissionTypo]
    <uses-permission android:name="android.permission.WRITE_SMS" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SystemPermissionTypo":
   This check looks for required permissions that look like well-known system
   permissions or permissions from the Android SDK, but aren't, and may be
   typos.

   Please double check the permission value you have supplied.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\MainActivity.java:235: Warning: Should not set both PARTIAL_WAKE_LOCK and ACQUIRE_CAUSES_WAKEUP. If you do not want the screen to turn on, get rid of ACQUIRE_CAUSES_WAKEUP [Wakelock]
                wakeLock = powerManager.newWakeLock(
                                        ~~~~~~~~~~~

   Explanation for issues of type "Wakelock":
   Failing to release a wakelock properly can keep the Android device in a
   high power mode, which reduces battery life. There are several causes of
   this, such as releasing the wake lock in onDestroy() instead of in
   onPause(), failing to call release() in all possible code paths after an
   acquire(), and so on.

   NOTE: If you are using the lock just to keep the screen on, you should
   strongly consider using FLAG_KEEP_SCREEN_ON instead. This window flag will
   be correctly managed by the platform as the user moves between applications
   and doesn't require a special permission. See
   https://developer.android.com/reference/android/view/WindowManager.LayoutPa
   rams.html#FLAG_KEEP_SCREEN_ON.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\MainActivity.java:238: Warning: Provide a timeout when requesting a wakelock with PowerManager.Wakelock.acquire(long timeout). This will ensure the OS will cleanup any wakelocks that last longer than you intend, and will save your user's battery. [WakelockTimeout]
            wakeLock.acquire();
            ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "WakelockTimeout":
   Wakelocks have two acquire methods: one with a timeout, and one without.
   You should generally always use the one with a timeout. A typical timeout
   is 10 minutes. If the task takes longer than it is critical that it happens
   (i.e. can't use JobScheduler) then maybe they should consider a foreground
   service instead (which is a stronger run guarantee and lets the user know
   something long/important is happening).

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml:14: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
        android:layout_centerVertical="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml:23: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
        android:layout_centerVertical="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml:34: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
        android:layout_centerVertical="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml:42: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
        android:layout_centerVertical="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:33: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
            android:layout_centerVertical="true"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:40: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
            android:layout_centerVertical="true"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:47: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
            android:layout_centerVertical="true"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:66: Warning: Invalid layout param in a LinearLayout: layout_centerVertical [ObsoleteLayoutParam]
            android:layout_centerVertical="true"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteLayoutParam":
   The given layout_param is not defined for the given layout, meaning it has
   no effect. This usually happens when you change the parent layout or move
   view code around without updating the layout params. This will cause
   useless attribute processing at runtime, and is misleading for others
   reading the layout so the parameter should be removed.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\App.java:101: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            String methodName = Build.VERSION.SDK_INT >= 18 ? "currentProcessName" : "currentPackageName";
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\views\BlackUnderlineEditText.java:53: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\impl\DefaultWhiteListProvider.java:52: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\JobSchedulerService.java:16: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:134: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:146: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:175: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java:198: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PhoneUtils.java:36: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 22 ) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PhoneUtils.java:52: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\singlepixel\SinglePixelActivity.java:85: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\SmsSender.java:110: Warning: Unnecessary; SDK_INT is never < 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\UssdProcessor.java:82: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\UssdProcessor.java:96: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\UssdProcessor.java:134: Warning: Unnecessary; SDK_INT is never < 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchDogService.java:78: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchDogService.java:83: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchDogService.java:187: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\receivers\WatchDogStatusReceiver.java:44: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            String methodName = Build.VERSION.SDK_INT >= 18 ? "currentProcessName" : "currentPackageName";
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchedService.java:59: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                String methodName = Build.VERSION.SDK_INT >= 18 ? "currentProcessName" : "currentPackageName";
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteList.java:95: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteList.java:142: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteList.java:173: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-v24: Warning: This folder configuration (v24) is unnecessary; minSdkVersion is 24. Merge all the resources in this folder into drawable. [ObsoleteSdkInt]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-night\themes.xml:6: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        <item name="android:statusBarColor" tools:targetApi="l">#000000</item> <!-- Black -->
                                            ~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\themes.xml:6: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        <item name="android:statusBarColor" tools:targetApi="l">#000000</item> <!-- Black -->
                                            ~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-v23: Warning: This folder configuration (v23) is unnecessary; minSdkVersion is 24. Merge all the resources in this folder into values. [ObsoleteSdkInt]

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\singlepixel\ScreenManager.java:18: Warning: Do not place Android context classes in static fields (static reference to ScreenManager which has field mContext pointing to Context); this is a memory leak [StaticFieldLeak]
    private static ScreenManager sInstance;
            ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketServiceConnection.java:19: Warning: Do not place Android context classes in static fields (static reference to SocketServiceConnection which has field host pointing to Context); this is a memory leak [StaticFieldLeak]
    private static SocketServiceConnection instance;
            ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java:47: Warning: Do not place Android context classes in static fields (static reference to FloatingWindow which has field floatRootView pointing to View); this is a memory leak [StaticFieldLeak]
    private static FloatingWindow floatingWindow = null;
            ~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java:269: Warning: This Handler class should be static or leaks might occur (com.bm.atool.service.SocketService.IncomingHandler) [HandlerLeak]
    private class IncomingHandler extends Handler {
                  ~~~~~~~~~~~~~~~

   Explanation for issues of type "HandlerLeak":
   Since this Handler is declared as an inner class, it may prevent the outer
   class from being garbage collected. If the Handler is using a Looper or
   MessageQueue for a thread other than the main thread, then there is no
   issue. If the Handler is using the Looper or MessageQueue of the main
   thread, you need to fix your Handler declaration, as follows: Declare the
   Handler as a static class; In the outer class, instantiate a WeakReference
   to the outer class and pass this object to your Handler when you
   instantiate the Handler; Make all references to members of the outer class
   using the WeakReference object.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_main.xml:16: Warning: Use a layout_height of 0dp instead of 1dp for better performance [InefficientWeight]
        android:layout_height = "1dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InefficientWeight":
   When only a single widget in a LinearLayout defines a weight, it is more
   efficient to assign a width/height of 0dp to it since it will absorb all
   the remaining space anyway. With a declared width/height of 0dp it does not
   have to measure its own size first.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:5: Warning: Possible overdraw: Root element paints background #ffffff with a theme that also paints a background (inferred theme is @style/Theme.AndroidTool) [Overdraw]
    android:background="#ffffff"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:5: Warning: Possible overdraw: Root element paints background @color/app_background_color with a theme that also paints a background (inferred theme is @style/Theme.AndroidTool) [Overdraw]
    android:background="@color/app_background_color"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:8: Warning: Possible overdraw: Root element paints background @color/app_background_color with a theme that also paints a background (inferred theme is @style/Theme.AndroidTool) [Overdraw]
    android:background="@color/app_background_color"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml:7: Warning: Possible overdraw: Root element paints background @color/app_background_color with a theme that also paints a background (inferred theme is @style/Theme.AndroidTool) [Overdraw]
    android:background="@color/app_background_color"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\arrays.xml:3: Warning: The resource R.array.reply_entries appears to be unused [UnusedResources]
    <string-array name="reply_entries">
                  ~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\arrays.xml:8: Warning: The resource R.array.reply_values appears to be unused [UnusedResources]
    <string-array name="reply_values">
                  ~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\dimens.xml:2: Warning: The resource R.dimen.fab_margin appears to be unused [UnusedResources]
    <dimen name="fab_margin">16dp</dimen>
           ~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-anydpi\ic_action_home.xml:1: Warning: The resource R.drawable.ic_action_home appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:3: Warning: The resource R.string.action_settings appears to be unused [UnusedResources]
    <string name="action_settings">Settings</string>
            ~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.first_fragment_label appears to be unused [UnusedResources]
    <string name="first_fragment_label">First Fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.second_fragment_label appears to be unused [UnusedResources]
    <string name="second_fragment_label">Second Fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.next appears to be unused [UnusedResources]
    <string name="next">Next</string>
            ~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.previous appears to be unused [UnusedResources]
    <string name="previous">Previous</string>
            ~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.lorem_ipsum appears to be unused [UnusedResources]
    <string name="lorem_ipsum">
            ~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:46: Warning: The resource R.string.title_activity_settings appears to be unused [UnusedResources]
    <string name="title_activity_settings">SettingsActivity</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:49: Warning: The resource R.string.messages_header appears to be unused [UnusedResources]
    <string name="messages_header">Messages</string>
            ~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:50: Warning: The resource R.string.sync_header appears to be unused [UnusedResources]
    <string name="sync_header">Sync</string>
            ~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:53: Warning: The resource R.string.signature_title appears to be unused [UnusedResources]
    <string name="signature_title">Your signature</string>
            ~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:54: Warning: The resource R.string.reply_title appears to be unused [UnusedResources]
    <string name="reply_title">Default reply action</string>
            ~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:57: Warning: The resource R.string.sync_title appears to be unused [UnusedResources]
    <string name="sync_title">Sync email periodically</string>
            ~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:58: Warning: The resource R.string.attachment_title appears to be unused [UnusedResources]
    <string name="attachment_title">Download incoming attachments</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:59: Warning: The resource R.string.attachment_summary_on appears to be unused [UnusedResources]
    <string name="attachment_summary_on">Automatically download attachments for incoming emails
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml:61: Warning: The resource R.string.attachment_summary_off appears to be unused [UnusedResources]
    <string name="attachment_summary_off">Only download attachments when manually requested</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-night\themes.xml:3: Warning: The resource R.style.Base_Theme_Snettool appears to be unused [UnusedResources]
    <style name="Base.Theme.Snettool" parent="Theme.Material3.DayNight.NoActionBar">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-v23\themes.xml:3: Warning: The resource R.style.Theme_Snettool appears to be unused [UnusedResources]
    <style name="Theme.Snettool" parent="Base.Theme.Snettool">
           ~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:10: Warning: This LinearLayout layout or its FrameLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "UselessParent":
   A layout with children that has no siblings, is not a scrollview or a root
   layout, and does not have a background, can be removed and have its
   children moved directly into the parent for a flatter and more efficient
   layout hierarchy.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:12: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:android="http://schemas.android.com/apk/res/android"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:13: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:app="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:14: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:tools="http://schemas.android.com/tools"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:52: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:android="http://schemas.android.com/apk/res/android"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:53: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:app="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:54: Warning: This namespace declaration is redundant [RedundantNamespace]
        xmlns:tools="http://schemas.android.com/tools"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantNamespace":
   In Android XML documents, only specify the namespace on the root/document
   element. Namespace declarations elsewhere in the document are typically
   accidental leftovers from copy/pasting XML from other files or
   documentation.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:12: Warning: Unused namespace declaration xmlns:android; already declared on the root element [UnusedNamespace]
        xmlns:android="http://schemas.android.com/apk/res/android"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:13: Warning: Unused namespace declaration xmlns:app; already declared on the root element [UnusedNamespace]
        xmlns:app="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:14: Warning: Unused namespace declaration xmlns:tools; already declared on the root element [UnusedNamespace]
        xmlns:tools="http://schemas.android.com/tools"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:52: Warning: Unused namespace declaration xmlns:android; already declared on the root element [UnusedNamespace]
        xmlns:android="http://schemas.android.com/apk/res/android"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:53: Warning: Unused namespace declaration xmlns:app; already declared on the root element [UnusedNamespace]
        xmlns:app="http://schemas.android.com/apk/res-auto"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:54: Warning: Unused namespace declaration xmlns:tools; already declared on the root element [UnusedNamespace]
        xmlns:tools="http://schemas.android.com/tools"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedNamespace":
   Unused namespace declarations take up space and require processing that is
   not necessary

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxhdpi\ic_action_home.png: Warning: The following images appear both as density independent .xml files and as bitmap files: srcmainresdrawable-anydpiic_action_home.xml, srcmainresdrawable-hdpiic_action_home.png [IconXmlAndPng]

   Explanation for issues of type "IconXmlAndPng":
   If a drawable resource appears as an .xml file in the drawable/ folder,
   it's usually not intentional for it to also appear as a bitmap using the
   same name; generally you expect the drawable XML file to define states and
   each state has a corresponding drawable bitmap.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-hdpi\ic_action_ok.png: Warning: Action Bar icons should use a single gray color (#333333 for light themes (with 60%/30% opacity for enabled/disabled), and #FFFFFF with opacity 80%/30% for dark themes [IconColors]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-mdpi\ic_action_ok.png: Warning: Action Bar icons should use a single gray color (#333333 for light themes (with 60%/30% opacity for enabled/disabled), and #FFFFFF with opacity 80%/30% for dark themes [IconColors]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xhdpi\ic_action_ok.png: Warning: Action Bar icons should use a single gray color (#333333 for light themes (with 60%/30% opacity for enabled/disabled), and #FFFFFF with opacity 80%/30% for dark themes [IconColors]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxhdpi\ic_action_ok.png: Warning: Action Bar icons should use a single gray color (#333333 for light themes (with 60%/30% opacity for enabled/disabled), and #FFFFFF with opacity 80%/30% for dark themes [IconColors]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxxhdpi\ic_action_ok.png: Warning: Action Bar icons should use a single gray color (#333333 for light themes (with 60%/30% opacity for enabled/disabled), and #FFFFFF with opacity 80%/30% for dark themes [IconColors]

   Explanation for issues of type "IconColors":
   Notification icons and Action Bar icons should only white and shades of
   gray. See the Android Design Guide for more details. Note that the way Lint
   decides whether an icon is an action bar icon or a notification icon is
   based on the filename prefix: ic_menu_ for action bar icons, ic_stat_ for
   notification icons etc. These correspond to the naming conventions
   documented in
   https://d.android.com/r/studio-ui/designer/material/iconography

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\debug.png: Warning: Found bitmap drawable res/drawable/debug.png in densityless folder [IconLocation]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\debug_select.png: Warning: Found bitmap drawable res/drawable/debug_select.png in densityless folder [IconLocation]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\home.png: Warning: Found bitmap drawable res/drawable/home.png in densityless folder [IconLocation]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\home_select.png: Warning: Found bitmap drawable res/drawable/home_select.png in densityless folder [IconLocation]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\setting.png: Warning: Found bitmap drawable res/drawable/setting.png in densityless folder [IconLocation]
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\setting_select.png: Warning: Found bitmap drawable res/drawable/setting_select.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:57: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:67: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:95: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:106: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:118: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml:54: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml:65: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:27: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:27: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:48: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:36: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\ItemViewTouchListener.java:30: Warning: ItemViewTouchListener#onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
    public boolean onTouch(View v, MotionEvent motionEvent) {
                   ~~~~~~~

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_float_item.xml:7: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:25: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:46: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml:17: Warning: Missing contentDescription attribute on image [ContentDescription]
<ImageView
 ~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:19: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\DebugFragment.java:157: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvJSResult.setText("No result yet...");
                           ~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PhoneAdapter.java:56: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.txtSlot.setText("PHONE");
                               ~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PhoneAdapter.java:58: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.txtSubscriptionId.setText("ID:" + currentItem.subscriptionId);
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PhoneAdapter.java:58: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.txtSubscriptionId.setText("ID:" + currentItem.subscriptionId);
                                         ~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:24: Warning: Hardcoded string "User Name", should use @string resource [HardcodedText]
        android:text="User Name"
        ~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:36: Warning: Hardcoded string "Login ID", should use @string resource [HardcodedText]
        android:hint="Login ID"
        ~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:45: Warning: Hardcoded string "Password", should use @string resource [HardcodedText]
        android:text="Password"
        ~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:57: Warning: Hardcoded string "Password", should use @string resource [HardcodedText]
        android:hint="Password"
        ~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:68: Warning: Hardcoded string "Login", should use @string resource [HardcodedText]
        android:text="Login"
        ~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:17: Warning: Hardcoded string "Debug & JavaScript Test", should use @string resource [HardcodedText]
            android:text="Debug &amp; JavaScript Test"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:30: Warning: Hardcoded string "JavaScript Test", should use @string resource [HardcodedText]
            android:text="JavaScript Test"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:43: Warning: Hardcoded string "Enter JavaScript code here...", should use @string resource [HardcodedText]
            android:hint="Enter JavaScript code here..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:64: Warning: Hardcoded string "Execute", should use @string resource [HardcodedText]
                android:text="Execute"
                ~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:74: Warning: Hardcoded string "Clear", should use @string resource [HardcodedText]
                android:text="Clear"
                ~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:83: Warning: Hardcoded string "Quick Tests", should use @string resource [HardcodedText]
            android:text="Quick Tests"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:102: Warning: Hardcoded string "Device Info", should use @string resource [HardcodedText]
                android:text="Device Info"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:113: Warning: Hardcoded string "Phone Numbers", should use @string resource [HardcodedText]
                android:text="Phone Numbers"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:125: Warning: Hardcoded string "Battery", should use @string resource [HardcodedText]
                android:text="Battery"
                ~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:135: Warning: Hardcoded string "Execution Result", should use @string resource [HardcodedText]
            android:text="Execution Result"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:151: Warning: Hardcoded string "No result yet...", should use @string resource [HardcodedText]
            android:text="No result yet..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:158: Warning: Hardcoded string "System Control", should use @string resource [HardcodedText]
            android:text="System Control"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml:173: Warning: Hardcoded string "Stop All Services", should use @string resource [HardcodedText]
            android:text="Stop All Services"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:40: Warning: Hardcoded string "username", should use @string resource [HardcodedText]
                android:text="username"
                ~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:88: Warning: Hardcoded string "SMS:", should use @string resource [HardcodedText]
                android:text="SMS:"
                ~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml:25: Warning: Hardcoded string "Permissions", should use @string resource [HardcodedText]
                android:text="Permissions"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml:63: Warning: Hardcoded string "Logout", should use @string resource [HardcodedText]
                    android:text="Logout" />
                    ~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml:74: Warning: Hardcoded string "Exit", should use @string resource [HardcodedText]
                    android:text="Exit" />
                    ~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml:32: Warning: Hardcoded string "请输入手机号码", should use @string resource [HardcodedText]
        android:hint="请输入手机号码"
        ~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml:13: Warning: Hardcoded string "Android 6.0", should use @string resource [HardcodedText]
    android:text="Android 6.0"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml:42: Warning: Hardcoded string "Grant", should use @string resource [HardcodedText]
        android:text="Grant" />
        ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:32: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
            android:paddingLeft="6dp"
            ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlSymmetry":
   If you specify padding or margin on the left side of a layout, you should
   probably also specify padding on the right side (and vice versa) for
   right-to-left layout symmetry.

F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\FloatingWindow.java:68: Warning: Use "Gravity.END" instead of "Gravity.RIGHT" to ensure correct behavior in right-to-left locales [RtlHardcoded]
        layoutParam.gravity = Gravity.RIGHT;
                                      ~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\singlepixel\SinglePixelActivity.java:37: Warning: Use "Gravity.START" instead of "Gravity.LEFT" to ensure correct behavior in right-to-left locales [RtlHardcoded]
        mWindow.setGravity(Gravity.LEFT | Gravity.TOP);
                                   ~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:35: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="5dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginLeft="5dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml:56: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="5dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginLeft="5dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:20: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
            android:gravity="left|center_vertical"
                             ~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml:30: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="0dp" to better support right-to-left layouts [RtlHardcoded]
                android:layout_marginLeft="0dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml:21: Warning: Consider replacing android:layout_alignParentRight with android:layout_alignParentEnd="true" to better support right-to-left layouts [RtlHardcoded]
    android:layout_alignParentRight="true"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml:33: Warning: Redundant attribute layout_alignParentRight; already defining layout_alignParentEnd with targetSdkVersion 34 [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:24: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="0dp" to better support right-to-left layouts [RtlHardcoded]
            android:layout_marginLeft="0dp"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:32: Warning: Consider replacing android:paddingLeft with android:paddingStart="6dp" to better support right-to-left layouts [RtlHardcoded]
            android:paddingLeft="6dp"
            ~~~~~~~~~~~~~~~~~~~
F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml:65: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="48dp" to better support right-to-left layouts [RtlHardcoded]
            android:layout_marginLeft="48dp"
            ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

2 errors, 193 warnings
