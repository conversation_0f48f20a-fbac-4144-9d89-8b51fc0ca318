# JavaScript引擎修复验证脚本 (PowerShell版本)
# 用于测试修复后的JavaScript引擎是否正常工作

Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "JavaScript引擎修复验证脚本" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

$success = $true

# 检查项目文件结构
Write-Host "1. 检查项目文件结构..." -ForegroundColor Yellow

if (-not (Test-Path "app\src\main\java\com\bm\atool\js\AndroidBridge.java")) {
    Write-Host "❌ AndroidBridge.java 文件不存在" -ForegroundColor Red
    $success = $false
} else {
    Write-Host "✅ AndroidBridge.java 文件存在" -ForegroundColor Green
}

if (-not (Test-Path "app\src\main\java\com\bm\atool\js\JavaScriptEngine.java")) {
    Write-Host "❌ JavaScriptEngine.java 文件不存在" -ForegroundColor Red
    $success = $false
} else {
    Write-Host "✅ JavaScriptEngine.java 文件存在" -ForegroundColor Green
}

# 检查AndroidBridge是否实现了接口
Write-Host "2. 检查AndroidBridge接口实现..." -ForegroundColor Yellow

$androidBridgeContent = Get-Content "app\src\main\java\com\bm\atool\js\AndroidBridge.java" -Raw

if ($androidBridgeContent -match "interface AndroidBridgeInterface") {
    Write-Host "✅ AndroidBridgeInterface 接口已定义" -ForegroundColor Green
} else {
    Write-Host "❌ AndroidBridgeInterface 接口未找到" -ForegroundColor Red
    $success = $false
}

if ($androidBridgeContent -match "implements AndroidBridgeInterface") {
    Write-Host "✅ AndroidBridge 已实现接口" -ForegroundColor Green
} else {
    Write-Host "❌ AndroidBridge 未实现接口" -ForegroundColor Red
    $success = $false
}

# 检查JavaScriptEngine是否使用接口绑定
Write-Host "3. 检查JavaScriptEngine接口绑定..." -ForegroundColor Yellow

$jsEngineContent = Get-Content "app\src\main\java\com\bm\atool\js\JavaScriptEngine.java" -Raw

if ($jsEngineContent -match "AndroidBridgeInterface\.class") {
    Write-Host "✅ JavaScriptEngine 使用接口绑定" -ForegroundColor Green
} else {
    Write-Host "❌ JavaScriptEngine 未使用接口绑定" -ForegroundColor Red
    $success = $false
}

# 检查测试文件是否存在
Write-Host "4. 检查测试文件..." -ForegroundColor Yellow

if (Test-Path "app\src\test\java\com\bm\atool\js\JavaScriptEngineTest.java") {
    Write-Host "✅ 单元测试文件已创建" -ForegroundColor Green
} else {
    Write-Host "⚠️  单元测试文件不存在" -ForegroundColor Yellow
}

if (Test-Path "app\src\androidTest\java\com\bm\atool\js\JavaScriptEngineIntegrationTest.java") {
    Write-Host "✅ 集成测试文件已创建" -ForegroundColor Green
} else {
    Write-Host "⚠️  集成测试文件不存在" -ForegroundColor Yellow
}

if (Test-Path "app\src\main\java\com\bm\atool\js\JavaScriptEngineTestHelper.java") {
    Write-Host "✅ 测试辅助类已创建" -ForegroundColor Green
} else {
    Write-Host "⚠️  测试辅助类不存在" -ForegroundColor Yellow
}

# 编译检查
Write-Host "5. 执行编译检查..." -ForegroundColor Yellow

if (Test-Path "gradlew.bat") {
    Write-Host "正在执行Gradle编译检查..." -ForegroundColor Cyan
    $compileResult = & .\gradlew.bat compileDebugJavaWithJavac --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 编译检查通过" -ForegroundColor Green
    } else {
        Write-Host "❌ 编译检查失败" -ForegroundColor Red
        $success = $false
    }
} else {
    Write-Host "⚠️  Gradle wrapper 不可用，跳过编译检查" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "修复验证完成" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

if ($success) {
    Write-Host "🎉 所有检查通过！修复成功！" -ForegroundColor Green
} else {
    Write-Host "❌ 部分检查失败，请检查上述错误" -ForegroundColor Red
}

Write-Host ""
Write-Host "修复摘要：" -ForegroundColor Cyan
Write-Host "1. ✅ 创建了 AndroidBridgeInterface 接口" -ForegroundColor Green
Write-Host "2. ✅ AndroidBridge 实现了接口" -ForegroundColor Green
Write-Host "3. ✅ JavaScriptEngine 使用接口绑定" -ForegroundColor Green
Write-Host "4. ✅ 添加了完整的测试套件" -ForegroundColor Green
Write-Host "5. ✅ 修改了 DebugFragment 使用测试辅助类" -ForegroundColor Green
Write-Host ""
Write-Host "问题修复说明：" -ForegroundColor Cyan
Write-Host "原始错误: 'Only interfaces can be bound. Received: class com.bm.atool.js.AndroidBridge'" -ForegroundColor Red
Write-Host "修复方案: 创建接口并让AndroidBridge实现该接口，然后在QuickJS绑定中使用接口" -ForegroundColor Green
Write-Host ""
Write-Host "测试建议：" -ForegroundColor Cyan
Write-Host "1. 运行单元测试: .\gradlew test" -ForegroundColor White
Write-Host "2. 运行集成测试: .\gradlew connectedAndroidTest" -ForegroundColor White
Write-Host "3. 在应用中打开Debug页面，查看JavaScript引擎初始化状态" -ForegroundColor White
Write-Host "4. 尝试执行JavaScript代码验证功能" -ForegroundColor White
Write-Host ""
Write-Host "如果问题仍然存在，请检查：" -ForegroundColor Yellow
Write-Host "- QuickJS库版本是否兼容" -ForegroundColor White
Write-Host "- Android API级别是否支持" -ForegroundColor White
Write-Host "- 设备是否支持QuickJS" -ForegroundColor White
