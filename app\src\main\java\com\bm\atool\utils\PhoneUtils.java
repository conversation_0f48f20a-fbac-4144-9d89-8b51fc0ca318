package com.bm.atool.utils;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.telecom.PhoneAccount;
import android.telecom.PhoneAccountHandle;
import android.telecom.TelecomManager;
import android.telephony.SignalStrength;
import android.telephony.SubscriptionInfo;
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.util.Log;

import com.bm.atool.model.SubscriptionInfoModel;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import kotlin.TypeCastException;

public class PhoneUtils {
    private final static String TAG = PhoneUtils.class.getSimpleName();
    @SuppressLint("MissingPermission")
    public static ArrayList<SubscriptionInfoModel> getPhones(Context context) {
        ArrayList<SubscriptionInfoModel> arrayList = new ArrayList<>();
        if (!PermissionUtils.isPermissionOk(context, "android.permission.READ_PHONE_STATE")) {
            return arrayList;
        }
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
//        Log.e(TAG,((TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE)).getLine1Number());
        if (Build.VERSION.SDK_INT >= 22 ) {
            SubscriptionManager from = SubscriptionManager.from(context.getApplicationContext());
            for (SubscriptionInfo subscriptionInfo : from.getActiveSubscriptionInfoList()) {
                TelephonyManager telephonyManager1 = telephonyManager.createForSubscriptionId(subscriptionInfo.getSubscriptionId());
                arrayList.add(SubscriptionInfoModel.from(subscriptionInfo,telephonyManager1));
            }
        } else {
            if (telephonyManager.getSubscriberId() != null) {
                arrayList.add(SubscriptionInfoModel.from(telephonyManager));
            }
        }
        return arrayList;
    }

    @SuppressLint("MissingPermission")
    public static PhoneAccountHandle getPhoneAccountHandleFromSubscriptionId(Context context, int subscriptionId) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
            if (telecomManager != null) {
                List<PhoneAccountHandle> phoneAccountHandleList = telecomManager.getCallCapablePhoneAccounts();
                if (phoneAccountHandleList != null) {
                    for (PhoneAccountHandle phoneAccountHandle : phoneAccountHandleList) {
                        if (phoneAccountHandle != null) {
                            try {
                                PhoneAccount phoneAccount = telecomManager.getPhoneAccount(phoneAccountHandle);
                                if (phoneAccount != null) {
                                    // 尝试使用反射获取subscriptionId
                                    try {
                                        Method getSubIdMethod = phoneAccount.getClass().getDeclaredMethod("getSubscriptionId");
                                        if (getSubIdMethod != null) {
                                            getSubIdMethod.setAccessible(true);
                                            int phoneAccountSubId = (int) getSubIdMethod.invoke(phoneAccount);
                                            if (phoneAccountSubId == subscriptionId) {
                                                return phoneAccountHandle;
                                            }
                                        }
                                    } catch (Exception ignored) {
                                        // 反射失败，继续使用下一种方法
                                    }
                                    
                                    // 通过账号标识判断
                                    Uri accountUri = phoneAccount.getAddress();
                                    if (accountUri != null && accountUri.getSchemeSpecificPart() != null) {
                                        String accountId = phoneAccountHandle.getId();
                                        if (accountId != null && (accountId.endsWith(String.valueOf(subscriptionId)) 
                                                || accountId.contains(":" + subscriptionId))) {
                                            return phoneAccountHandle;
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "获取PhoneAccount时出错: " + e.getMessage());
                            }
                            
                            // 尝试从ID匹配
                            String phoneAccountId = phoneAccountHandle.getId();
                            if (phoneAccountId != null && 
                                (phoneAccountId.endsWith(String.valueOf(subscriptionId)) || 
                                 phoneAccountId.contains(":" + subscriptionId))) {
                                return phoneAccountHandle;
                            }
                        }
                    }
                }
            }
        }
        return null;
    }
//    @SuppressLint("Range")
//    fun getSimMultiInfo(): MutableMap<Int, SimInfo> {
//        val infoList = HashMap<Int, SimInfo>()
//        try {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
//                Log.d(TAG, "1.版本超过5.1，调用系统方法")
//                val mSubscriptionManager = XUtil.getContext().getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
//                ActivityCompat.checkSelfPermission(
//                        XUtil.getContext(), permission.READ_PHONE_STATE
//                )
//                val activeSubscriptionInfoList: List<SubscriptionInfo>? = mSubscriptionManager.activeSubscriptionInfoList
//                if (!activeSubscriptionInfoList.isNullOrEmpty()) {
//                    //1.1.1 有使用的卡，就遍历所有卡
//                    for (subscriptionInfo in activeSubscriptionInfoList) {
//                        val simInfo = SimInfo()
//                        simInfo.mCarrierName = subscriptionInfo.carrierName?.toString()
//                        simInfo.mIccId = subscriptionInfo.iccId?.toString()
//                        simInfo.mSimSlotIndex = subscriptionInfo.simSlotIndex
//                        simInfo.mNumber = subscriptionInfo.number?.toString()
//                        simInfo.mCountryIso = subscriptionInfo.countryIso?.toString()
//                        simInfo.mSubscriptionId = subscriptionInfo.subscriptionId
//                        Log.d(TAG, simInfo.toString())
//                        infoList[simInfo.mSimSlotIndex] = simInfo
//                    }
//                }
//            } else {
//                Log.d(TAG, "2.版本低于5.1的系统，首先调用数据库，看能不能访问到")
//                val uri = Uri.parse("content://telephony/siminfo") //访问raw_contacts表
//                val resolver: ContentResolver = XUtil.getContext().contentResolver
//                val cursor = resolver.query(
//                        uri, arrayOf(
//                                "_id", "icc_id", "sim_id", "display_name", "carrier_name", "name_source", "color", "number", "display_number_format", "data_roaming", "mcc", "mnc"
//                        ), null, null, null
//                )
//                if (cursor != null && cursor.moveToFirst()) {
//                    do {
//                        val simInfo = SimInfo()
//                        simInfo.mCarrierName = cursor.getString(cursor.getColumnIndex("carrier_name"))
//                        simInfo.mIccId = cursor.getString(cursor.getColumnIndex("icc_id"))
//                        simInfo.mSimSlotIndex = cursor.getInt(cursor.getColumnIndex("sim_id"))
//                        simInfo.mNumber = cursor.getString(cursor.getColumnIndex("number"))
//                        simInfo.mCountryIso = cursor.getString(cursor.getColumnIndex("mcc"))
//                        //val id = cursor.getString(cursor.getColumnIndex("_id"))
//                        Log.d(TAG, simInfo.toString())
//                        infoList[simInfo.mSimSlotIndex] = simInfo
//                    } while (cursor.moveToNext())
//                    cursor.close()
//                }
//            }
//        } catch (e: java.lang.Exception) {
//            e.printStackTrace()
//            Log.e(TAG, "getSimMultiInfo:", e)
//        }
//        //仍然获取不到/只获取到一个->取出备注
//            /*if (infoList.isEmpty() || infoList.size == 1) {
//                Log.d(TAG, "3.直接取出备注框的数据作为信息")
//                //为空，两个卡都没有获取到信息
//                if (infoList.isEmpty()) {
//                    //卡1备注信息不为空
//                    val etExtraSim1 = SettingUtils.extraSim1
//                    if (!TextUtils.isEmpty(etExtraSim1)) {
//                        val simInfo1 = SimInfo()
//                        //卡1
//                        simInfo1.mSimSlotIndex = 0
//                        simInfo1.mNumber = etExtraSim1
//                        simInfo1.mSubscriptionId = SettingUtils.subidSim1
//                        //把卡放入
//                        infoList[simInfo1.mSimSlotIndex] = simInfo1
//                    }
//                    //卡2备注信息不为空
//                    val etExtraSim2 = SettingUtils.extraSim2
//                    if (!TextUtils.isEmpty(etExtraSim2)) {
//                        val simInfo2 = SimInfo()
//                        simInfo2.mSimSlotIndex = 1
//                        simInfo2.mNumber = etExtraSim2
//                        simInfo2.mSubscriptionId = SettingUtils.subidSim2
//                        //把卡放入
//                        infoList[simInfo2.mSimSlotIndex] = simInfo2
//                    }
//
//                    //有一张卡,判断是卡几
//                } else {
//                    var infoListIndex = -1
//                    for (obj in infoList) {
//                        infoListIndex = obj.key
//                    }
//                    //获取到卡1，且卡2备注信息不为空
//                    if (infoListIndex == 0 && !TextUtils.isEmpty(SettingUtils.extraSim2)) {
//                        //获取到卡1信息，卡2备注不为空，创建卡2实体
//                        val simInfo2 = SimInfo()
//                        simInfo2.mSimSlotIndex = 1
//                        simInfo2.mNumber = SettingUtils.extraSim2
//                        simInfo2.mSubscriptionId = SettingUtils.subidSim1
//                        infoList[simInfo2.mSimSlotIndex] = simInfo2
//                    } else if (infoListIndex == 1 && !TextUtils.isEmpty(SettingUtils.extraSim1)) {
//                        //获取到卡1信息，卡1备注不为空，创建卡1实体
//                        val simInfo1 = SimInfo()
//                        simInfo1.mSimSlotIndex = 0
//                        simInfo1.mNumber = SettingUtils.extraSim1
//                        simInfo1.mSubscriptionId = SettingUtils.subidSim1
//                        infoList[simInfo1.mSimSlotIndex] = simInfo1
//                    }
//                }
//            }*/
//        Log.i(TAG, infoList.toString())
//        return infoList
//    }
}
