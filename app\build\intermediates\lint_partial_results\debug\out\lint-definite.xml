<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.0" type="incidents">

    <incident
        id="SystemPermissionTypo"
        severity="warning"
        message="Did you mean `android.permission.WRITE_OBB`?">
        <fix-replace
            description="Replace with android.permission.WRITE_OBB"
            oldString="android.permission.WRITE_SMS"
            replacement="android.permission.WRITE_OBB"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="15"
            column="36"
            startOffset="676"
            endLine="15"
            endColumn="64"
            endOffset="704"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="59"
            column="13"
            startOffset="3286"
            endLine="59"
            endColumn="45"
            endOffset="3318"/>
    </incident>

    <incident
        id="ExportedService"
        severity="warning"
        message="Exported service does not require permission">
        <fix-alternatives>
            <fix-attribute
                description="Set permission"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="permission"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set exported=&quot;false&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="exported"
                value="false"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="140"
            column="10"
            startOffset="6893"
            endLine="140"
            endColumn="17"
            endOffset="6900"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v24`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `drawable`.">
        <fix-data file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24" folderName="drawable" requiresApi="24"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_float_item.xml"
            line="7"
            column="6"
            startOffset="259"
            endLine="7"
            endColumn="15"
            endOffset="268"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;User Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="24"
            column="9"
            startOffset="840"
            endLine="24"
            endColumn="33"
            endOffset="864"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            dot="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="27"
            column="6"
            startOffset="944"
            endLine="27"
            endColumn="14"
            endOffset="952"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="27"
            column="6"
            startOffset="944"
            endLine="27"
            endColumn="14"
            endOffset="952"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Login ID&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="36"
            column="9"
            startOffset="1276"
            endLine="36"
            endColumn="32"
            endOffset="1299"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Password&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="45"
            column="9"
            startOffset="1568"
            endLine="45"
            endColumn="32"
            endOffset="1591"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="48"
            column="6"
            startOffset="1671"
            endLine="48"
            endColumn="14"
            endOffset="1679"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Password&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="57"
            column="9"
            startOffset="2004"
            endLine="57"
            endColumn="32"
            endOffset="2027"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Login&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="68"
            column="9"
            startOffset="2375"
            endLine="68"
            endColumn="29"
            endOffset="2395"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_height` of `0dp` instead of `1dp` for better performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="16"
            column="9"
            startOffset="634"
            endLine="16"
            endColumn="38"
            endOffset="663"/>
    </incident>

    <incident
        id="PxUsage"
        severity="warning"
        message="Avoid using &quot;`px`&quot; as units; use &quot;`dp`&quot; instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="34"
            column="9"
            startOffset="1335"
            endLine="34"
            endColumn="37"
            endOffset="1363"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Debug &amp; JavaScript Test&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="17"
            column="13"
            startOffset="605"
            endLine="17"
            endColumn="55"
            endOffset="647"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;JavaScript Test&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="30"
            column="13"
            startOffset="1123"
            endLine="30"
            endColumn="43"
            endOffset="1153"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="36"
            column="10"
            startOffset="1343"
            endLine="36"
            endColumn="18"
            endOffset="1351"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter JavaScript code here...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="43"
            column="13"
            startOffset="1671"
            endLine="43"
            endColumn="57"
            endOffset="1715"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="1981"
                    endOffset="3047"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="2204"
                    endOffset="2604"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="2620"
                    endOffset="3020"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="57"
            column="14"
            startOffset="2205"
            endLine="57"
            endColumn="20"
            endOffset="2211"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Execute&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="64"
            column="17"
            startOffset="2532"
            endLine="64"
            endColumn="39"
            endOffset="2554"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="1981"
                    endOffset="3047"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="2204"
                    endOffset="2604"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="2620"
                    endOffset="3020"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="67"
            column="14"
            startOffset="2621"
            endLine="67"
            endColumn="20"
            endOffset="2627"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Clear&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="74"
            column="17"
            startOffset="2948"
            endLine="74"
            endColumn="37"
            endOffset="2968"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Quick Tests&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="83"
            column="13"
            startOffset="3206"
            endLine="83"
            endColumn="39"
            endOffset="3232"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="3421"
                    endOffset="5092"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="3643"
                    endOffset="4092"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="4108"
                    endOffset="4605"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="4621"
                    endOffset="5065"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="95"
            column="14"
            startOffset="3644"
            endLine="95"
            endColumn="20"
            endOffset="3650"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Device Info&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="102"
            column="17"
            startOffset="3975"
            endLine="102"
            endColumn="43"
            endOffset="4001"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="3421"
                    endOffset="5092"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="3643"
                    endOffset="4092"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="4108"
                    endOffset="4605"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="4621"
                    endOffset="5065"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="106"
            column="14"
            startOffset="4109"
            endLine="106"
            endColumn="20"
            endOffset="4115"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Phone Numbers&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="113"
            column="17"
            startOffset="4436"
            endLine="113"
            endColumn="45"
            endOffset="4464"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="3421"
                    endOffset="5092"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="3643"
                    endOffset="4092"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="4108"
                    endOffset="4605"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
                    startOffset="4621"
                    endOffset="5065"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="118"
            column="14"
            startOffset="4622"
            endLine="118"
            endColumn="20"
            endOffset="4628"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Battery&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="125"
            column="17"
            startOffset="4950"
            endLine="125"
            endColumn="39"
            endOffset="4972"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Execution Result&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="135"
            column="13"
            startOffset="5251"
            endLine="135"
            endColumn="44"
            endOffset="5282"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No result yet...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="151"
            column="13"
            startOffset="5897"
            endLine="151"
            endColumn="44"
            endOffset="5928"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;System Control&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="158"
            column="13"
            startOffset="6137"
            endLine="158"
            endColumn="42"
            endOffset="6166"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Stop All Services&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="173"
            column="13"
            startOffset="6736"
            endLine="173"
            endColumn="45"
            endOffset="6768"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `FrameLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="10"
            column="6"
            startOffset="408"
            endLine="10"
            endColumn="18"
            endOffset="420"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="25"
            column="14"
            startOffset="957"
            endLine="25"
            endColumn="23"
            endOffset="966"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;username&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="40"
            column="17"
            startOffset="1619"
            endLine="40"
            endColumn="40"
            endOffset="1642"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="46"
            column="14"
            startOffset="1891"
            endLine="46"
            endColumn="23"
            endOffset="1900"/>
    </incident>

    <incident
        id="TextViewEdits"
        severity="warning"
        message="Attribute `android:inputType` should not be used with `&lt;TextView>`: Change element type to `&lt;EditText>` ?">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="87"
            column="17"
            startOffset="3534"
            endLine="87"
            endColumn="41"
            endOffset="3558"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SMS:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="88"
            column="17"
            startOffset="3576"
            endLine="88"
            endColumn="36"
            endOffset="3595"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Permissions&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
            line="25"
            column="17"
            startOffset="885"
            endLine="25"
            endColumn="43"
            endOffset="911"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
                    startOffset="1913"
                    endOffset="3206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
                    startOffset="2157"
                    endOffset="2659"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
                    startOffset="2679"
                    endOffset="3177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
            line="54"
            column="18"
            startOffset="2158"
            endLine="54"
            endColumn="24"
            endOffset="2164"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Logout&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
            line="63"
            column="21"
            startOffset="2635"
            endLine="63"
            endColumn="42"
            endOffset="2656"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
                    startOffset="1913"
                    endOffset="3206"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
                    startOffset="2157"
                    endOffset="2659"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
                    startOffset="2679"
                    endOffset="3177"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
            line="65"
            column="18"
            startOffset="2680"
            endLine="65"
            endColumn="24"
            endOffset="2686"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Exit&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
            line="74"
            column="21"
            startOffset="3155"
            endLine="74"
            endColumn="40"
            endOffset="3174"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/phone_row.xml"
            line="14"
            column="9"
            startOffset="511"
            endLine="14"
            endColumn="45"
            endOffset="547"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/phone_row.xml"
            line="23"
            column="9"
            startOffset="827"
            endLine="23"
            endColumn="45"
            endOffset="863"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入手机号码&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/phone_row.xml"
            line="32"
            column="9"
            startOffset="1183"
            endLine="32"
            endColumn="31"
            endOffset="1205"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/phone_row.xml"
            line="34"
            column="9"
            startOffset="1250"
            endLine="34"
            endColumn="45"
            endOffset="1286"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/phone_row.xml"
            line="42"
            column="9"
            startOffset="1561"
            endLine="42"
            endColumn="45"
            endOffset="1597"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Android 6.0&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/setting_row.xml"
            line="13"
            column="5"
            startOffset="438"
            endLine="13"
            endColumn="31"
            endOffset="464"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/setting_row.xml"
            line="17"
            column="2"
            startOffset="559"
            endLine="17"
            endColumn="11"
            endOffset="568"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/check_mark_color&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/check_mark_color"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/setting_row.xml"
            line="25"
            column="5"
            startOffset="842"
            endLine="25"
            endColumn="43"
            endOffset="880"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Grant&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/setting_row.xml"
            line="42"
            column="9"
            startOffset="1502"
            endLine="42"
            endColumn="29"
            endOffset="1522"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:android"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="12"
            column="9"
            startOffset="403"
            endLine="12"
            endColumn="67"
            endOffset="461"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:app"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="13"
            column="9"
            startOffset="471"
            endLine="13"
            endColumn="60"
            endOffset="522"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:tools"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="14"
            column="9"
            startOffset="532"
            endLine="14"
            endColumn="55"
            endOffset="578"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:android"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="52"
            column="9"
            startOffset="2060"
            endLine="52"
            endColumn="67"
            endOffset="2118"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:app"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="53"
            column="9"
            startOffset="2128"
            endLine="53"
            endColumn="60"
            endOffset="2179"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:tools"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="54"
            column="9"
            startOffset="2189"
            endLine="54"
            endColumn="55"
            endOffset="2235"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:android; already declared on the root element">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="12"
            column="9"
            startOffset="403"
            endLine="12"
            endColumn="67"
            endOffset="461"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:app; already declared on the root element">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="13"
            column="9"
            startOffset="471"
            endLine="13"
            endColumn="60"
            endOffset="522"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:tools; already declared on the root element">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="14"
            column="9"
            startOffset="532"
            endLine="14"
            endColumn="55"
            endOffset="578"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="19"
            column="10"
            startOffset="754"
            endLine="19"
            endColumn="19"
            endOffset="763"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="33"
            column="13"
            startOffset="1341"
            endLine="33"
            endColumn="49"
            endOffset="1377"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="40"
            column="13"
            startOffset="1627"
            endLine="40"
            endColumn="49"
            endOffset="1663"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="47"
            column="13"
            startOffset="1915"
            endLine="47"
            endColumn="49"
            endOffset="1951"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:android; already declared on the root element">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="52"
            column="9"
            startOffset="2060"
            endLine="52"
            endColumn="67"
            endOffset="2118"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:app; already declared on the root element">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="53"
            column="9"
            startOffset="2128"
            endLine="53"
            endColumn="60"
            endOffset="2179"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:tools; already declared on the root element">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="54"
            column="9"
            startOffset="2189"
            endLine="54"
            endColumn="55"
            endOffset="2235"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="66"
            column="13"
            startOffset="2651"
            endLine="66"
            endColumn="49"
            endOffset="2687"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v23`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `values`.">
        <fix-data file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23" folderName="values" requiresApi="24"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/App.java"
            line="101"
            column="33"
            startOffset="3406"
            endLine="101"
            endColumn="60"
            endOffset="3433"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/js/AndroidBridge.java"
            line="49"
            column="23"
            startOffset="1205"
            endLine="49"
            endColumn="34"
            endOffset="1216"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/js/ScriptScheduler.java"
            line="268"
            column="20"
            startOffset="8305"
            endLine="269"
            endColumn="85"
            endOffset="8451"/>
    </incident>

    <incident
        id="WakelockTimeout"
        severity="warning"
        message="Provide a timeout when requesting a wakelock with `PowerManager.Wakelock.acquire(long timeout)`. This will ensure the OS will cleanup any wakelocks that last longer than you intend, and will save your user&apos;s battery.">
        <fix-replace
            description="Set timeout to 10 minutes"
            oldPattern="acquire\s*\(()\s*\)"
            replacement="10*60*1000L /*10 minutes*/"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/MainActivity.java"
            line="238"
            column="13"
            startOffset="9674"
            endLine="238"
            endColumn="31"
            endOffset="9692"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getSubscriberId` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/model/SubscriptionInfoModel.java"
            line="126"
            column="67"
            startOffset="6284"
            endLine="126"
            endColumn="101"
            endOffset="6318"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getSimSerialNumber` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/model/SubscriptionInfoModel.java"
            line="142"
            column="43"
            startOffset="7141"
            endLine="142"
            endColumn="80"
            endOffset="7178"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getLine1Number` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/model/SubscriptionInfoModel.java"
            line="157"
            column="49"
            startOffset="7886"
            endLine="157"
            endColumn="82"
            endOffset="7919"/>
    </incident>

    <incident
        id="UnsafeProtectedBroadcastReceiver"
        severity="warning"
        message="This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver&apos;s onReceive method does not appear to call getAction to ensure that the received Intent&apos;s action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/receivers/SimChangedReceiver.java"
            line="13"
            column="17"
            startOffset="318"
            endLine="13"
            endColumn="26"
            endOffset="327"/>
    </incident>

    <incident
        id="UnsafeProtectedBroadcastReceiver"
        severity="warning"
        message="This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver&apos;s onReceive method does not appear to call getAction to ensure that the received Intent&apos;s action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/receivers/SmsReceiver.java"
            line="31"
            column="17"
            startOffset="940"
            endLine="31"
            endColumn="26"
            endOffset="949"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/receivers/WatchDogStatusReceiver.java"
            line="44"
            column="33"
            startOffset="1885"
            endLine="44"
            endColumn="60"
            endOffset="1912"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `ScreenManager` which has field `mContext` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/singlepixel/ScreenManager.java"
            line="18"
            column="13"
            startOffset="432"
            endLine="18"
            endColumn="19"
            endOffset="438"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/singlepixel/SinglePixelActivity.java"
            line="85"
            column="13"
            startOffset="2447"
            endLine="85"
            endColumn="70"
            endOffset="2504"/>
    </incident>

    <incident
        id="HandlerLeak"
        severity="warning"
        message="This `Handler` class should be static or leaks might occur (com.bm.atool.service.SocketService.IncomingHandler)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
            line="269"
            column="19"
            startOffset="9924"
            endLine="269"
            endColumn="34"
            endOffset="9939"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
            line="165"
            column="28"
            startOffset="5578"
            endLine="165"
            endColumn="103"
            endOffset="5653"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
            line="867"
            column="62"
            startOffset="35496"
            endLine="867"
            endColumn="73"
            endOffset="35507"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
            line="868"
            column="42"
            startOffset="35553"
            endLine="868"
            endColumn="53"
            endOffset="35564"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `SocketServiceConnection` which has field `host` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketServiceConnection.java"
            line="19"
            column="13"
            startOffset="477"
            endLine="19"
            endColumn="19"
            endOffset="483"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/WatchDogService.java"
            line="78"
            column="17"
            startOffset="2323"
            endLine="78"
            endColumn="70"
            endOffset="2376"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/WatchDogService.java"
            line="83"
            column="21"
            startOffset="2728"
            endLine="83"
            endColumn="67"
            endOffset="2774"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/WatchDogService.java"
            line="187"
            column="13"
            startOffset="6627"
            endLine="187"
            endColumn="66"
            endOffset="6680"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/WatchedService.java"
            line="59"
            column="37"
            startOffset="2394"
            endLine="59"
            endColumn="64"
            endOffset="2421"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 24">
        <fix-data conditional="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/SmsSender.java"
            line="110"
            column="13"
            startOffset="4704"
            endLine="110"
            endColumn="69"
            endOffset="4760"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `FloatingWindow` which has field `floatRootView` pointing to `View`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
            line="47"
            column="13"
            startOffset="1731"
            endLine="47"
            endColumn="19"
            endOffset="1737"/>
    </incident>

    <incident
        id="ApplySharedPref"
        severity="warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background">
        <fix-replace
            description="Replace commit() with apply()"
            oldPattern="(commit)\s*\("
            replacement="apply"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
            line="141"
            column="16"
            startOffset="5463"
            endLine="141"
            endColumn="24"
            endOffset="5471"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/adapters/PhoneAdapter.java"
            line="56"
            column="32"
            startOffset="1831"
            endLine="56"
            endColumn="39"
            endOffset="1838"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/adapters/PhoneAdapter.java"
            line="58"
            column="42"
            startOffset="1942"
            endLine="58"
            endColumn="76"
            endOffset="1976"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/adapters/PhoneAdapter.java"
            line="58"
            column="42"
            startOffset="1942"
            endLine="58"
            endColumn="47"
            endOffset="1947"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/DebugFragment.java"
            line="139"
            column="37"
            startOffset="4852"
            endLine="144"
            endColumn="18"
            endOffset="5055"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/DebugFragment.java"
            line="157"
            column="28"
            startOffset="5444"
            endLine="157"
            endColumn="46"
            endOffset="5462"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`ItemViewTouchListener#onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/ItemViewTouchListener.java"
            line="30"
            column="20"
            startOffset="742"
            endLine="30"
            endColumn="27"
            endOffset="749"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/views/BlackUnderlineEditText.java"
            line="53"
            column="13"
            startOffset="1549"
            endLine="53"
            endColumn="66"
            endOffset="1602"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/UssdProcessor.java"
            line="82"
            column="25"
            startOffset="2893"
            endLine="82"
            endColumn="71"
            endOffset="2939"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/UssdProcessor.java"
            line="96"
            column="21"
            startOffset="3670"
            endLine="96"
            endColumn="67"
            endOffset="3716"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 24">
        <fix-data conditional="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/UssdProcessor.java"
            line="134"
            column="13"
            startOffset="5268"
            endLine="134"
            endColumn="69"
            endOffset="5324"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="123"
            column="45"
            startOffset="6083"
            endLine="123"
            endColumn="56"
            endOffset="6094"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="123"
            column="78"
            startOffset="6116"
            endLine="123"
            endColumn="89"
            endOffset="6127"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="134"
            column="13"
            startOffset="6478"
            endLine="134"
            endColumn="59"
            endOffset="6524"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="146"
            column="13"
            startOffset="6863"
            endLine="146"
            endColumn="59"
            endOffset="6909"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="175"
            column="13"
            startOffset="8120"
            endLine="175"
            endColumn="59"
            endOffset="8166"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="198"
            column="17"
            startOffset="9423"
            endLine="198"
            endColumn="63"
            endOffset="9469"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PhoneUtils.java"
            line="36"
            column="13"
            startOffset="1365"
            endLine="36"
            endColumn="40"
            endOffset="1392"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getSubscriberId` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PhoneUtils.java"
            line="43"
            column="17"
            startOffset="1863"
            endLine="43"
            endColumn="51"
            endOffset="1897"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PhoneUtils.java"
            line="52"
            column="13"
            startOffset="2210"
            endLine="52"
            endColumn="59"
            endOffset="2256"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/xuexiang/keeplive/whitelist/impl/DefaultWhiteListProvider.java"
            line="52"
            column="13"
            startOffset="1634"
            endLine="52"
            endColumn="59"
            endOffset="1680"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/xuexiang/keeplive/whitelist/WhiteList.java"
            line="95"
            column="25"
            startOffset="2938"
            endLine="95"
            endColumn="71"
            endOffset="2984"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/xuexiang/keeplive/whitelist/WhiteList.java"
            line="142"
            column="21"
            startOffset="4507"
            endLine="142"
            endColumn="67"
            endOffset="4553"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/xuexiang/keeplive/whitelist/WhiteList.java"
            line="173"
            column="21"
            startOffset="5731"
            endLine="173"
            endColumn="67"
            endOffset="5777"/>
    </incident>

    <incident
        id="Wakelock"
        severity="warning"
        message="Should not set both `PARTIAL_WAKE_LOCK` and `ACQUIRE_CAUSES_WAKEUP`. If you do not want the screen to turn on, get rid of `ACQUIRE_CAUSES_WAKEUP`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/MainActivity.java"
            line="235"
            column="41"
            startOffset="9523"
            endLine="235"
            endColumn="52"
            endOffset="9534"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/debug.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/debug.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/debug_select.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/debug_select.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/home.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/home_select.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home_select.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/setting.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/setting.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/setting_select.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/setting_select.png"/>
    </incident>

    <incident
        id="IconColors"
        severity="warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_action_ok.png"/>
    </incident>

    <incident
        id="IconColors"
        severity="warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_action_ok.png"/>
    </incident>

    <incident
        id="IconColors"
        severity="warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_action_ok.png"/>
    </incident>

    <incident
        id="IconColors"
        severity="warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_action_ok.png"/>
    </incident>

    <incident
        id="IconColors"
        severity="warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_action_ok.png"/>
    </incident>

    <incident
        id="IconXmlAndPng"
        severity="warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable-anydpi\ic_action_home.xml, src\main\res\drawable-hdpi\ic_action_home.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_action_home.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_action_home.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_action_home.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_action_home.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_action_home.xml"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#ffffff` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="5"
            column="5"
            startOffset="221"
            endLine="5"
            endColumn="33"
            endOffset="249"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/app_background_color` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml"
            line="5"
            column="5"
            startOffset="199"
            endLine="5"
            endColumn="53"
            endOffset="247"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/app_background_color` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="8"
            column="5"
            startOffset="314"
            endLine="8"
            endColumn="53"
            endOffset="362"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/app_background_color` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml"
            line="7"
            column="5"
            startOffset="257"
            endLine="7"
            endColumn="53"
            endOffset="305"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/FloatingWindow.java"
            line="88"
            column="101"
            startOffset="3981"
            endLine="88"
            endColumn="105"
            endOffset="3985"/>
    </incident>

</incidents>
