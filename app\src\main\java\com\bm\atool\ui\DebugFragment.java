package com.bm.atool.ui;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.R;
import com.bm.atool.Sys;
import com.bm.atool.js.JavaScriptEngine;
import com.bm.atool.js.JavaScriptEngineTestHelper;
import com.bm.atool.service.SocketService;
import com.bm.atool.service.WatchDogService;

import java.util.List;

public class DebugFragment extends BaseFragment{
    private static final String TAG = "DebugFragment";

    // UI组件
    private Button btnStopSocket;
    private EditText etJavaScript;
    private TextView tvJSResult;
    private Button btnExecuteJS;
    private Button btnClearJS;
    private Button btnTestDeviceInfo;
    private Button btnTestPhones;
    private Button btnTestBattery;
    private Button btnTestStartCase;

    // JavaScript引擎
    private JavaScriptEngine jsEngine;
    private Handler mainHandler;

    public DebugFragment(){
        setTitle("DEBUG");
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Initialize view
        View view = inflater.inflate(R.layout.fragment_debug, container, false);

        mainHandler = new Handler(Looper.getMainLooper());
        initializeViews(view);
        setupClickListeners();
        initializeJavaScriptEngine();

        return view;
    }

    private void initializeViews(View view) {
        btnStopSocket = view.findViewById(R.id.btnStopSocket);
        etJavaScript = view.findViewById(R.id.etJavaScript);
        tvJSResult = view.findViewById(R.id.tvJSResult);
        btnExecuteJS = view.findViewById(R.id.btnExecuteJS);
        btnClearJS = view.findViewById(R.id.btnClearJS);
        btnTestDeviceInfo = view.findViewById(R.id.btnTestDeviceInfo);
        btnTestPhones = view.findViewById(R.id.btnTestPhones);
        btnTestBattery = view.findViewById(R.id.btnTestBattery);
        btnTestStartCase = view.findViewById(R.id.btnTestStartCase);
    }

    private void setupClickListeners() {
        btnStopSocket.setOnClickListener(v -> {
            Log.d(TAG, "Stopping all services from DebugFragment");
            Sys.stop();
            Log.d(TAG, "Sys.stop() called.");
        });

        btnExecuteJS.setOnClickListener(v -> executeJavaScript());
        btnClearJS.setOnClickListener(v -> clearJavaScript());

        btnTestDeviceInfo.setOnClickListener(v -> loadTestScript("Android.getDeviceInfo()"));
        btnTestPhones.setOnClickListener(v -> loadTestScript("Android.getPhoneNumbers()"));
        btnTestBattery.setOnClickListener(v -> loadTestScript("Android.getBatteryLevel()"));
        btnTestStartCase.setOnClickListener(v -> loadTestScript("Android.startTest('test_case_001')"));
    }

    private void initializeJavaScriptEngine() {
        // 运行完整的JavaScript引擎测试
        new Thread(() -> {
            // 运行完整的JavaScript引擎测试
            JavaScriptEngineTestHelper.TestResult result =
                JavaScriptEngineTestHelper.runAllTests(getContext());

            mainHandler.post(() -> {
                if (result.success) {
                    Log.i(TAG, "JavaScript引擎测试成功: " + result.message);
                    updateResult("✅ " + result.message, true);

                    // 如果测试成功，创建引擎实例
                    try {
                        jsEngine = new JavaScriptEngine(getContext(), null);
                        updateResult("✅ JavaScript引擎已就绪", true);
                    } catch (Exception e) {
                        Log.e(TAG, "创建JavaScript引擎实例失败", e);
                        updateResult("❌ 创建引擎实例失败: " + e.getMessage(), false);
                    }
                } else {
                    Log.e(TAG, "JavaScript引擎测试失败: " + result.message);
                    updateResult("❌ " + result.message, false);
                }
            });
        }).start();
    }

    private void executeJavaScript() {
        String script = etJavaScript.getText().toString().trim();
        if (TextUtils.isEmpty(script)) {
            Toast.makeText(getContext(), "请输入JavaScript代码", Toast.LENGTH_SHORT).show();
            return;
        }

        if (jsEngine == null || !jsEngine.isAvailable()) {
            updateResult("JavaScript引擎不可用", false);
            return;
        }

        updateResult("执行中...", true);

        // 在后台线程执行JavaScript
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            JavaScriptEngine.ScriptResult scriptResult = jsEngine.executeScript(script, "debug_script.js");
            long executionTime = System.currentTimeMillis() - startTime;

            final String resultText;
            final boolean success = scriptResult.success;

            if (success) {
                resultText = String.format(
                        "执行时间: %dms\n成功: 是\n结果: %s",
                        executionTime,
                        scriptResult.result != null ? scriptResult.result.toString() : "null"
                );
            } else {
                resultText = String.format(
                        "执行时间: %dms\n成功: 否\n错误: %s",
                        executionTime,
                        scriptResult.error
                );
            }

            mainHandler.post(() -> updateResult(resultText, success));
        }).start();
    }

    private void clearJavaScript() {
        etJavaScript.setText("");
        tvJSResult.setText("No result yet...");
    }

    private void loadTestScript(String script) {
        etJavaScript.setText(script);
    }

    private void updateResult(String result, boolean success) {
        if (tvJSResult != null) {
            tvJSResult.setText(result);
            tvJSResult.setTextColor(getResources().getColor(
                success ? R.color.text_success : R.color.text_error, null));
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (jsEngine != null) {
            jsEngine.close();
            jsEngine = null;
        }
    }

    @Override
    public int getIconResourceId() {
        return R.drawable.tab_icon_debug;
    }
}
