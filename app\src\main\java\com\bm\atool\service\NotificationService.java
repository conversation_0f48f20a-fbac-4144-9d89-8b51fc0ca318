package com.bm.atool.service;

import android.app.Notification;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.service.notification.NotificationListenerService;
import android.service.notification.StatusBarNotification;
import android.util.Log;
import com.bm.atool.model.ApiFacotry;
import com.bm.atool.model.NotificationItem;
import com.bm.atool.model.NotificationSyncRequest;
import com.bm.atool.model.Response;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import retrofit2.Call;
import retrofit2.Callback;

public class NotificationService extends NotificationListenerService {

    private static final String TAG = "NotificationService";

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "NotificationService 已启动");
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "NotificationService 已销毁");
        super.onDestroy();
    }

    @Override
    public void onListenerConnected() {
        super.onListenerConnected();
        Log.d(TAG, "NotificationService onListenerConnected: 服务已连接");
    }

    @Override
    public void onNotificationPosted(StatusBarNotification sbn) {
        Log.d(TAG, "收到通知: " + (sbn == null ? "null" : sbn.toString()));
        if (sbn == null) {
            Log.d(TAG, "sbn为null，返回");
            return;
        }

        String packageName = sbn.getPackageName();
        Log.d(TAG, "通知包名: " + packageName);
        if (Objects.equals(packageName, getApplication().getPackageName())) {
            Log.d(TAG, "忽略自身app的通知");
            return;
        }

        String appName = "";
        try {
            appName = getPackageManager().getApplicationLabel(
                    getPackageManager().getApplicationInfo(packageName, 0)).toString();
        } catch (Exception e) {
            Log.d(TAG, "获取appName失败: " + e.getMessage());
        }

        Notification notification = sbn.getNotification();
        if (notification == null) {
            Log.d(TAG, "notification为null，返回");
            return;
        }

        Bundle extras = notification.extras;
        CharSequence titleChars = extras.getCharSequence(Notification.EXTRA_TITLE);
        CharSequence contentChars = extras.getCharSequence(Notification.EXTRA_TEXT);
        if (contentChars == null || contentChars.length() == 0) {
            Log.d(TAG, "通知内容为空，返回");
            return;
        }

        String title = (titleChars == null) ? "" : titleChars.toString();
        String content = contentChars.toString();

        int notificationId = sbn.getId();
        Log.d(TAG, "准备上传通知，id: " + notificationId + ", 包名: " + packageName + ", appName: " + appName + ", 标题: " + title + ", 内容: " + content);
        NotificationItem item = new NotificationItem(notificationId, packageName, appName, title, content);
        NotificationSyncRequest request = new NotificationSyncRequest(java.util.Collections.singletonList(item));

        Gson gson = new Gson();
        String jsonPayload = gson.toJson(request);
        Log.d(TAG, "上传payload: " + jsonPayload);

        ApiFacotry.getSmsApi().syncNotifications(request).enqueue(new Callback<Response>() {
            @Override
            public void onResponse(Call<Response> call, retrofit2.Response<Response> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    Log.d(TAG, "通知上传成功: " + jsonPayload);
                } else {
                    Log.d(TAG, "通知上传失败，响应异常: " + (response.body() != null ? response.body().toString() : "body为null"));
                }
            }

            @Override
            public void onFailure(Call<Response> call, Throwable t) {
                Log.d(TAG, "通知上传失败，异常: " + t.getMessage(), t);
            }
        });
    }
} 