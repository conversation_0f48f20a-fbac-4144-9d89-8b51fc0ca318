// 简单的JavaScript验证脚本
// 测试基本的JavaScript功能

console.log("=== JavaScript引擎验证脚本 ===");

// 测试基本运算
var result1 = 1 + 1;
console.log("基本运算测试: 1 + 1 = " + result1);

// 测试字符串操作
var str = "Hello, QuickJS!";
console.log("字符串测试: " + str);

// 测试数组操作
var arr = [1, 2, 3, 4, 5];
console.log("数组测试: " + arr.join(", "));

// 测试对象操作
var obj = {
    name: "QuickJS",
    version: "1.0",
    platform: "Android"
};
console.log("对象测试: " + JSON.stringify(obj));

// 测试函数
function testFunction(x, y) {
    return x * y;
}
var result2 = testFunction(6, 7);
console.log("函数测试: 6 * 7 = " + result2);

// 测试Android桥接（如果可用）
if (typeof Android !== 'undefined') {
    console.log("Android桥接可用");
    Android.log("来自JavaScript的日志消息");
    
    var timestamp = Android.getCurrentTimestamp();
    console.log("当前时间戳: " + timestamp);
    
    var deviceInfo = Android.getDeviceInfo();
    console.log("设备信息: " + deviceInfo);
} else {
    console.log("Android桥接不可用（这在单元测试中是正常的）");
}

console.log("=== 验证脚本执行完成 ===");

// 返回测试结果
"验证脚本执行成功";
