<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.bm.atool.receivers.WakeUpAutoStartReceiver"
                    boolean="true"/>
                <entry
                    name="com.bm.atool.receivers.SimChangedReceiver"
                    boolean="true"/>
                <entry
                    name="com.bm.atool.receivers.WakeUpReceiver"
                    boolean="true"/>
                <entry
                    name="com.bm.atool.MainActivity"
                    boolean="true"/>
                <entry
                    name="com.bm.atool.service.singlepixel.SinglePixelActivity"
                    boolean="true"/>
                <entry
                    name="com.bm.atool.service.WatchDogService"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="ACTION_SOCKET_PONG (used to send a broadcast)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
                            line="389"
                            column="33"
                            startOffset="14863"
                            endLine="389"
                            endColumn="67"
                            endOffset="14897"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
                            line="502"
                            column="37"
                            startOffset="20534"
                            endLine="502"
                            endColumn="71"
                            endOffset="20568"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
                            line="670"
                            column="34"
                            startOffset="28600"
                            endLine="670"
                            endColumn="68"
                            endOffset="28634"/>
                    </map>
                    <map id="android.settings.action.MANAGE_OVERLAY_PERMISSION (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="177"
                            column="33"
                            startOffset="8220"
                            endLine="177"
                            endColumn="136"
                            endOffset="8323"/>
                    </map>
                    <map id="android.settings.ACCESSIBILITY_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="162"
                            column="29"
                            startOffset="7505"
                            endLine="162"
                            endColumn="79"
                            endOffset="7555"/>
                    </map>
                    <map id="android.settings.SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="166"
                            column="29"
                            startOffset="7720"
                            endLine="166"
                            endColumn="65"
                            endOffset="7756"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="184"
                            column="33"
                            startOffset="8767"
                            endLine="184"
                            endColumn="69"
                            endOffset="8803"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="206"
                            column="33"
                            startOffset="10019"
                            endLine="206"
                            endColumn="69"
                            endOffset="10055"/>
                        <location id="3"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="212"
                            column="29"
                            startOffset="10345"
                            endLine="212"
                            endColumn="65"
                            endOffset="10381"/>
                        <location id="4"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="260"
                            column="29"
                            startOffset="12519"
                            endLine="260"
                            endColumn="65"
                            endOffset="12555"/>
                    </map>
                    <map id="ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED (used to send a broadcast)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/ANTAccessibilityService.java"
                            line="37"
                            column="25"
                            startOffset="1259"
                            endLine="37"
                            endColumn="84"
                            endOffset="1318"/>
                    </map>
                    <map id="com.bm.atool.daemon.enabled (used to send a broadcast)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
                            line="343"
                            column="25"
                            startOffset="14334"
                            endLine="343"
                            endColumn="62"
                            endOffset="14371"/>
                    </map>
                    <map id="android.intent.action.MAIN (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/xuexiang/keeplive/whitelist/WhiteList.java"
                            line="202"
                            column="33"
                            startOffset="6800"
                            endLine="202"
                            endColumn="63"
                            endOffset="6830"/>
                    </map>
                    <map id="android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="199"
                            column="33"
                            startOffset="9505"
                            endLine="199"
                            endColumn="97"
                            endOffset="9569"/>
                    </map>
                    <map id="android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
                            line="255"
                            column="29"
                            startOffset="12192"
                            endLine="255"
                            endColumn="87"
                            endOffset="12250"/>
                    </map>
                    <map id="android.intent.action.CALL (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/UssdProcessor.java"
                            line="73"
                            column="29"
                            startOffset="2399"
                            endLine="73"
                            endColumn="64"
                            endOffset="2434"/>
                    </map>
            </map>
    </map>
    <map id="JobSchedulerService">
        <location id="com.bm.atool.service.JobSchedulerService"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/WatchDogService.java"
            line="80"
            column="65"
            startOffset="2517"
            endLine="80"
            endColumn="90"
            endOffset="2542"/>
    </map>
    <map id="UnusedResources">
        <location id="R.string.signature_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="3828"
            endLine="53"
            endColumn="35"
            endOffset="3850"/>
        <location id="R.string.title_activity_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="3587"
            endLine="46"
            endColumn="43"
            endOffset="3617"/>
        <location id="R.style.Base_Theme_Snettool"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="3"
            column="12"
            startOffset="109"
            endLine="3"
            endColumn="38"
            endOffset="135"/>
        <location id="R.string.first_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="186"
            endLine="5"
            endColumn="40"
            endOffset="213"/>
        <location id="R.string.lorem_ipsum"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="406"
            endLine="10"
            endColumn="31"
            endOffset="424"/>
        <location id="R.string.action_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="13"
            startOffset="76"
            endLine="3"
            endColumn="35"
            endOffset="98"/>
        <location id="R.string.second_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="251"
            endLine="6"
            endColumn="41"
            endOffset="279"/>
        <location id="R.string.messages_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="3691"
            endLine="49"
            endColumn="35"
            endOffset="3713"/>
        <location id="R.string.previous"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="357"
            endLine="8"
            endColumn="28"
            endOffset="372"/>
        <location id="R.drawable.ic_action_home"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_action_home.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="11"
            endColumn="10"
            endOffset="370"/>
        <location id="R.string.attachment_summary_off"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="4235"
            endLine="61"
            endColumn="42"
            endOffset="4264"/>
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="64"
            endLine="3"
            endColumn="24"
            endOffset="76"/>
        <location id="R.string.attachment_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="4047"
            endLine="58"
            endColumn="36"
            endOffset="4070"/>
        <location id="R.dimen.fab_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="2"
            column="12"
            startOffset="24"
            endLine="2"
            endColumn="29"
            endOffset="41"/>
        <location id="R.string.sync_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="3983"
            endLine="57"
            endColumn="30"
            endOffset="4000"/>
        <location id="R.array.reply_values"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml"
            line="8"
            column="19"
            startOffset="189"
            endLine="8"
            endColumn="38"
            endOffset="208"/>
        <location id="R.style.Theme_Snettool"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23/themes.xml"
            line="3"
            column="12"
            startOffset="73"
            endLine="3"
            endColumn="33"
            endOffset="94"/>
        <entry
            name="model"
            string="array[reply_values(D),reply_entries(D)],attr[isLightTheme(E),actionBarSize(R)],color[button_primary(U),green(U),text_primary(U),check_mark_color(U),black(D),button_secondary(U),surface_background(U),app_background_color(U),button_accent(U),accent_color(U),white(U),header_background(U),input_background(U),text_success(U),text_secondary(U),text_error(U)],dimen[fab_margin(D)],drawable[tab_icon_debug(U),ic_action_ok(U),debug(U),debug_select(U),tab_icon_settings(U),setting_select(U),tab_icon_home(U),home(U),setting(U),ic_action_home(D),ic_launcher_foreground(U),home_select(U),ic_launcher_background(U),ic_launcher_foreground_1(R)],id[btnTestBattery(U),btn_Login(U),et_account(U),btnTestPhones(U),txtUserName(U),iconLogo(D),txtName(U),tvJSResult(U),imageView(D),imgStatus(U),smsListView(U),view_pager(U),btnStopSocket(U),txtStatus(U),txtSubscriptionId(U),et_password(U),layoutIcon(D),btnExecuteJS(U),btnExit(U),edtPhoneNumber(U),btnLogout(U),txtDisplayName(U),txtFrom(U),tab_layout(U),btnGoSetting(U),txtSlot(U),etJavaScript(U),btnClearJS(U),debugTitle(D),phoneListView(U),txtContent(U),btnTestDeviceInfo(U),layoutUserInfo(D),permissionsListView(U),txtTime(U)],layout[fragment_main(U),fragment_settings(U),phone_row(U),activity_login(U),activity_float_item(U),activity_main(U),fragment_debug(U),setting_row(U),sms_row(U)],mipmap[ic_launcher_round(U),ic_launcher(U)],raw[sliant(U)],string[next(D),reply_title(D),attachment_summary_off(D),sync_header(D),title_activity_settings(D),previous(D),sync_title(D),signature_title(D),attachment_title(D),action_settings(D),second_fragment_label(D),attachment_summary_on(D),app_name(U),first_fragment_label(D),messages_header(D),lorem_ipsum(D)],style[Base_Theme_AndroidTool(U),TextAppearance_AppCompat_Medium(R),Theme_Snettool(D),Theme_Material3_DayNight_NoActionBar(R),Base_Theme_Snettool(D),Theme_AndroidTool(U)],xml[allocation(U),data_extraction_rules(U),backup_rules(U)];15^18^17,19^1a^1d,1b^20^1c,1f^22,46^b^f^50^4f^63^6^a^12,47^b^f^6^a,4a^4f,4b^f^3^6^63^12,4c^b^f^6^10^12^4^e^9^c,4d^6^7^16^d^a,4e^50^4f,4f^21^1f,50^21^1f,62^65^5,64^66^2,66^65,67^62,68^5e;;;"/>
        <location id="R.string.attachment_summary_on"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="4123"
            endLine="59"
            endColumn="41"
            endOffset="4151"/>
        <location id="R.string.next"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="318"
            endLine="7"
            endColumn="24"
            endOffset="329"/>
        <location id="R.string.sync_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="3745"
            endLine="50"
            endColumn="31"
            endOffset="3763"/>
        <location id="R.string.reply_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="3888"
            endLine="54"
            endColumn="31"
            endOffset="3906"/>
        <location id="R.array.reply_entries"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml"
            line="3"
            column="19"
            startOffset="62"
            endLine="3"
            endColumn="39"
            endOffset="82"/>
    </map>

</incidents>
