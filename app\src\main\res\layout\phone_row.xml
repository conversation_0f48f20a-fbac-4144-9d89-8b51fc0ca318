<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="1dp">
    <TextView
        android:id="@+id/txtSlot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:layout_centerVertical="true"
        android:textColor="@android:color/black"
        android:layout_marginEnd="8dp"/>

    <TextView
        android:id="@+id/txtSubscriptionId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:layout_centerVertical="true"
        android:textColor="@android:color/darker_gray"
        android:layout_marginEnd="8dp"/>

    <com.bm.atool.ui.views.BlackUnderlineEditText
        android:id="@+id/edtPhoneNumber"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:hint="请输入手机号码"
        android:inputType="phone"
        android:layout_centerVertical="true"
        android:textColor="@android:color/black"
        android:layout_marginEnd="8dp"/>
    <TextView
        android:id="@+id/txtDisplayName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=""
        android:layout_centerVertical="true"
        android:textColor="@android:color/black" />

    <!--    <TextView-->
    <!--        android:id="@+id/txtName"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:text="Android 6.0"-->
    <!--        android:layout_centerVertical="true"-->
    <!--        android:textColor="@android:color/black" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/txtName"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:text="Android 6.0"-->
    <!--        android:layout_centerVertical="true"-->
    <!--        android:textColor="@android:color/black" />-->

</LinearLayout>