http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-land/dimens.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-w1240dp/dimens.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-w600dp/dimens.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/tab_icon_debug.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_action_ok.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_action_ok.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_action_ok.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_action_ok.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/ic_action_ok.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/debug.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/debug_select.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/tab_icon_settings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/setting_select.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/tab_icon_home.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/setting.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_action_home.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/ic_action_home.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/ic_action_home.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/ic_action_home.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/ic_action_home.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24/ic_launcher_foreground.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/home_select.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_debug.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_float_item.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/setting_row.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/phone_row.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_settings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/sliant.mp3,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/allocation.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+array:reply_values,0,Vreply,reply_all,;reply_entries,0,VReply,Reply to all,;+color:button_primary,1,V"#66BB6A";green,1,V"#4CAF50";text_primary,1,V"#37474F";check_mark_color,1,V"#FFECB3";black,1,V"#FF000000";button_secondary,1,V"#90A4AE";surface_background,1,V"#F8FFF9";app_background_color,1,V"#ECF5EE";accent_color,1,V"#81C784";button_accent,1,V"#81C784";white,1,V"#FFFFFFFF";header_background,1,V"#DAEADC";input_background,1,V"#F5F5F5";text_success,1,V"#4CAF50";text_secondary,1,V"#607D8B";text_error,1,V"#F44336";+dimen:fab_margin,2,V"16dp";fab_margin,3,V"48dp";fab_margin,4,V"200dp";fab_margin,5,V"48dp";+drawable:tab_icon_debug,6,F;ic_action_ok,7,F;ic_action_ok,8,F;ic_action_ok,9,F;ic_action_ok,10,F;ic_action_ok,11,F;debug,12,F;debug_select,13,F;tab_icon_settings,14,F;setting_select,15,F;tab_icon_home,16,F;home,17,F;setting,18,F;ic_action_home,19,F;ic_action_home,20,F;ic_action_home,21,F;ic_action_home,22,F;ic_action_home,23,F;ic_launcher_foreground,24,F;home_select,25,F;ic_launcher_background,26,F;+id:btnTestBattery,27,F;btn_Login,28,F;et_account,28,F;btnTestPhones,27,F;txtUserName,29,F;iconLogo,30,F;txtName,31,F;tvJSResult,27,F;imageView,29,F;imageView,32,F;imgStatus,29,F;imgStatus,31,F;smsListView,29,F;view_pager,33,F;btnStopSocket,27,F;txtStatus,32,F;txtSubscriptionId,34,F;et_password,28,F;layoutIcon,29,F;btnExecuteJS,27,F;btnExit,35,F;edtPhoneNumber,34,F;btnLogout,35,F;txtDisplayName,34,F;txtFrom,32,F;tab_layout,33,F;btnGoSetting,31,F;txtSlot,34,F;etJavaScript,27,F;btnClearJS,27,F;debugTitle,27,F;phoneListView,29,F;txtContent,32,F;btnTestDeviceInfo,27,F;layoutUserInfo,29,F;permissionsListView,35,F;txtTime,32,F;+layout:fragment_main,29,F;fragment_settings,35,F;phone_row,34,F;activity_login,28,F;activity_float_item,30,F;activity_main,33,F;fragment_debug,27,F;setting_row,31,F;sms_row,32,F;+mipmap:ic_launcher_round,36,F;ic_launcher_round,37,F;ic_launcher_round,38,F;ic_launcher_round,39,F;ic_launcher_round,40,F;ic_launcher_round,41,F;ic_launcher,42,F;ic_launcher,43,F;ic_launcher,44,F;ic_launcher,45,F;ic_launcher,46,F;ic_launcher,47,F;+raw:sliant,48,F;+string:next,49,V"Next";reply_title,49,V"Default reply action";attachment_summary_off,49,V"Only download attachments when manually requested";sync_header,49,V"Sync";title_activity_settings,49,V"SettingsActivity";previous,49,V"Previous";sync_title,49,V"Sync email periodically";signature_title,49,V"Your signature";attachment_title,49,V"Download incoming attachments";action_settings,49,V"Settings";second_fragment_label,49,V"Second Fragment";attachment_summary_on,49,V"Automatically download attachments for incoming emails";app_name,49,V"android-tool";first_fragment_label,49,V"First Fragment";messages_header,49,V"Messages";lorem_ipsum,49,V"Lorem ipsum dolor sit amet\, consectetur adipiscing elit. Nam in scelerisque sem. Mauris volutpat\, dolor id interdum ullamcorper\, risus dolor egestas lectus\, sit amet mattis purus dui nec risus. Maecenas non sodales nisi\, vel dictum dolor. Class aptent taciti sociosqu ad litora torquent per conubia nostra\, per inceptos himenaeos. Suspendisse blandit eleifend diam\, vel rutrum tellus vulputate quis. Aliquam eget libero aliquet\, imperdiet nisl a\, ornare ex. Sed rhoncus est ut libero porta lobortis. Fusce in dictum tellus.

 Suspendisse interdum ornare ante. Aliquam nec cursus lorem. Morbi id magna felis. Vivamus egestas\, est a condimentum egestas\, turpis nisl iaculis ipsum\, in dictum tellus dolor sed neque. Morbi tellus erat\, dapibus ut sem a\, iaculis tincidunt dui. Interdum et malesuada fames ac ante ipsum primis in faucibus. Curabitur et eros porttitor\, ultricies urna vitae\, molestie nibh. Phasellus at commodo eros\, non aliquet metus. Sed maximus nisl nec dolor bibendum\, vel congue leo egestas.

 Sed interdum tortor nibh\, in sagittis risus mollis quis. Curabitur mi odio\, condimentum sit amet auctor at\, mollis non turpis. Nullam pretium libero vestibulum\, finibus orci vel\, molestie quam. Fusce blandit tincidunt nulla\, quis sollicitudin libero facilisis et. Integer interdum nunc ligula\, et fermentum metus hendrerit id. Vestibulum lectus felis\, dictum at lacinia sit amet\, tristique id quam. Cras eu consequat dui. Suspendisse sodales nunc ligula\, in lobortis sem porta sed. Integer id ultrices magna\, in luctus elit. Sed a pellentesque est.

 Aenean nunc velit\, lacinia sed dolor sed\, ultrices viverra nulla. Etiam a venenatis nibh. Morbi laoreet\, tortor sed facilisis varius\, nibh orci rhoncus nulla\, id elementum leo dui non lorem. Nam mollis ipsum quis auctor varius. Quisque elementum eu libero sed commodo. In eros nisl\, imperdiet vel imperdiet et\, scelerisque a mauris. Pellentesque varius ex nunc\, quis imperdiet eros placerat ac. Duis finibus orci et est auctor tincidunt. Sed non viverra ipsum. Nunc quis augue egestas\, cursus lorem at\, molestie sem. Morbi a consectetur ipsum\, a placerat diam. Etiam vulputate dignissim convallis. Integer faucibus mauris sit amet finibus convallis.

 Phasellus in aliquet mi. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. In volutpat arcu ut felis sagittis\, in finibus massa gravida. Pellentesque id tellus orci. Integer dictum\, lorem sed efficitur ullamcorper\, libero justo consectetur ipsum\, in mollis nisl ex sed nisl. Donec maximus ullamcorper sodales. Praesent bibendum rhoncus tellus nec feugiat. In a ornare nulla. Donec rhoncus libero vel nunc consequat\, quis tincidunt nisl eleifend. Cras bibendum enim a justo luctus vestibulum. Fusce dictum libero quis erat maximus\, vitae volutpat diam dignissim.";+style:Theme.AndroidTool,50,VDBase.Theme.AndroidTool,;Theme.Snettool,51,VDBase.Theme.Snettool,android\:navigationBarColor:@android\:color/transparent,android\:statusBarColor:@android\:color/transparent,android\:windowLightStatusBar:?attr/isLightTheme,;Base.Theme.AndroidTool,50,VDTheme.Material3.DayNight.NoActionBar,colorPrimary:#F5F5F5,colorControlNormal:#000000,colorControlActivated:@color/green,colorControlHighlight:@color/green,android\:statusBarColor:#000000,;Base.Theme.Snettool,52,VDTheme.Material3.DayNight.NoActionBar,colorPrimary:#F5F5F5,android\:statusBarColor:#000000,;+xml:allocation,53,F;data_extraction_rules,54,F;backup_rules,55,F;