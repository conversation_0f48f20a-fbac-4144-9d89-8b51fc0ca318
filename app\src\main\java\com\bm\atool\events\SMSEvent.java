package com.bm.atool.events;

public class SMSEvent {
    public static final int UPLOAD_STATUS_PENDDING = 0;
    public static final int UPLOAD_STATUS_UPLOADED = 1;
    public static final int UPLOAD_STATUS_FAILED = 2;
    public String content;
    public String from;
    public String to;
    public int slot;
    public String  subscriptionId;
    public Integer updateStatus = UPLOAD_STATUS_PENDDING;
    public String messageId;
}
