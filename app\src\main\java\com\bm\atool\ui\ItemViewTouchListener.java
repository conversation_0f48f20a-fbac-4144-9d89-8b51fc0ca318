package com.bm.atool.ui;

import android.content.Intent;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;

import com.bm.atool.MainActivity;

import java.util.Objects;

/**
 * @功能:处理悬浮窗拖动更新位置
 * @User Lmy
 * @Creat 4/16/21 9:41 AM
 * @Compony 永远相信美好的事情即将发生
 */
public class ItemViewTouchListener implements View.OnTouchListener{
    private WindowManager.LayoutParams wl;
    private WindowManager windowManager;
    private Float x = 0F;
    private Float y = 0F;
    private int lastAction = 0;
    public  ItemViewTouchListener(WindowManager.LayoutParams wl,WindowManager windowManager){
        this.wl = wl;
        this.windowManager = windowManager;
    }

    @Override
    public boolean onTouch(View v, MotionEvent motionEvent) {
        switch (motionEvent.getAction()){
            case MotionEvent.ACTION_DOWN:{
                x = motionEvent.getRawX();
                y = motionEvent.getRawY();
                lastAction = motionEvent.getAction();
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                Float nowX = motionEvent.getRawX();
                Float nowY = motionEvent.getRawY();
                Float movedX = nowX - x;
                Float movedY = nowY - y;
                x = nowX;
                y = nowY;
                wl.x += movedX;
                wl.y += movedY;
                lastAction = motionEvent.getAction();
                //更新悬浮球控件位置
                if(!Objects.isNull(windowManager)){
                    windowManager.updateViewLayout(v,wl);
                }
                break;
            }
            case MotionEvent.ACTION_UP:{
                if (lastAction == MotionEvent.ACTION_DOWN) {
                    Intent intent = new Intent(v.getContext(), MainActivity.class);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    v.getContext().startActivity(intent);
                }
                lastAction = motionEvent.getAction();
            }
            default:{
                break;
            }
        }

        return false;
    }
}