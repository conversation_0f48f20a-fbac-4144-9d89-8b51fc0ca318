package com.bm.atool.receivers;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.bm.atool.Sys;

import java.lang.reflect.Method;

public class WatchDogStatusReceiver extends BroadcastReceiver {

    private static final String TAG = WatchDogStatusReceiver.class.getSimpleName();
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.e(TAG, "StartWatchReceiver.onReceive: " + intent.getAction()
                + " enabled: " + String.valueOf(intent.getBooleanExtra(NotificationCompat.CATEGORY_STATUS, false))
                + " process: " + getProcessName()
                + " Thread: " + Thread.currentThread().getName()+"["+ Thread.currentThread().getId() +"]");
        if(Sys.ACTION_DAEMON_ENABLED.equals(intent.getAction())){
            Boolean enableWatchDog = intent.getBooleanExtra(NotificationCompat.CATEGORY_STATUS, false);
            Sys.updateWatchDogEnabled(enableWatchDog);
        }
    }
    private static String getProcessName() {
        if (Build.VERSION.SDK_INT >= 28)
            return Application.getProcessName();

        // Using the same technique as Application.getProcessName() for older devices
        // Using reflection since ActivityThread is an internal API

        try {
            @SuppressLint("PrivateApi")
            Class<?> activityThread = Class.forName("android.app.ActivityThread");

            // Before API 18, the method was incorrectly named "currentPackageName", but it still returned the process name
            // See https://github.com/aosp-mirror/platform_frameworks_base/commit/b57a50bd16ce25db441da5c1b63d48721bb90687
            String methodName = Build.VERSION.SDK_INT >= 18 ? "currentProcessName" : "currentPackageName";
            Method getProcessName = activityThread.getDeclaredMethod(methodName);
            return (String) getProcessName.invoke(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}