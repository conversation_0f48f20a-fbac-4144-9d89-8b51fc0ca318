package com.bm.atool.ui;

import static android.content.Context.WINDOW_SERVICE;


import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.util.Log;

import com.bm.atool.utils.PermissionUtils;
import com.bm.atool.R;

import java.util.Objects;

public class FloatingWindow {
    WindowManager windowManager;
    View floatRootView = null;
    Context context;
    WindowManager.LayoutParams layoutParam;
    public FloatingWindow(Context context){
        windowManager = (WindowManager)context.getSystemService(WINDOW_SERVICE);
        this.context = context;
    }
    private WindowManager.LayoutParams createLayoutParams(){

        DisplayMetrics outMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(outMetrics);
        WindowManager.LayoutParams layoutParam = new WindowManager.LayoutParams();

        boolean canDrawOverlays = PermissionUtils.canDrawOverlays(context);
        boolean isAccessibilityOn = PermissionUtils.isAccessibilitySettingsOn();

        if (canDrawOverlays) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                layoutParam.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
            } else {
                layoutParam.type = WindowManager.LayoutParams.TYPE_PHONE;
            }
            layoutParam.format = PixelFormat.RGBA_8888;
        } else if (isAccessibilityOn) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                layoutParam.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    layoutParam.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
                }
            } else {
                layoutParam.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
            }
            layoutParam.format = PixelFormat.TRANSPARENT;
        } else {
            // Fallback if neither overlay nor accessibility permissions are explicitly granted
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                layoutParam.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
            } else {
                layoutParam.type = WindowManager.LayoutParams.TYPE_PHONE;
            }
            layoutParam.format = PixelFormat.RGBA_8888;
        }
        layoutParam.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        layoutParam.width = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParam.height = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParam.gravity = Gravity.RIGHT;
        return layoutParam;
    }

    public void show() {
        Log.d("FloatingWindow", "show() called. Current floatRootView: " + (floatRootView == null ? "null" : "not null"));
        try {
            if (Objects.nonNull(floatRootView)) {
                Log.d("FloatingWindow", "show(): floatRootView already exists, returning.");
                return;
            }
            boolean accessibilityEnabled = PermissionUtils.isAccessibilitySettingsOn();
            boolean canDrawOverlays = PermissionUtils.canDrawOverlays(this.context);
            Log.d("FloatingWindow", "show(): Permissions - isAccessibilityEnabled: " + accessibilityEnabled + ", canDrawOverlays: " + canDrawOverlays);

            if (!accessibilityEnabled && !canDrawOverlays) {
                Log.d("FloatingWindow", "show(): Insufficient permissions (neither Accessibility nor Overlay granted), returning.");
                return;
            }
            layoutParam = createLayoutParams();
            floatRootView = LayoutInflater.from(this.context).inflate(R.layout.activity_float_item, null);
            floatRootView.setOnTouchListener(new ItemViewTouchListener(layoutParam, windowManager));
//        ImageView iconLogo = floatRootView.findViewById(R.id.iconLogo);
//        iconLogo.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                Intent intent = new Intent(MyAccessibilityService.this, MainActivity.class);
//                intent.addCategory(Intent.CATEGORY_DEFAULT);
//                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                startActivity(intent);
//            }
//        });
            Log.d("FloatingWindow", "show(): Adding view to windowManager.");
            windowManager.addView(floatRootView, layoutParam);
            Log.d("FloatingWindow", "show(): View added to windowManager successfully.");
        }
        catch (Exception ex){
            Log.e("FloatingWindow", "show(): Exception occurred: " + ex.getMessage(), ex);
            ex.printStackTrace();
        }
    }

    public void close(){
        Log.d("FloatingWindow", "close() called. Current floatRootView: " + (floatRootView == null ? "null" : "not null"));
        try{
            if (Objects.nonNull(floatRootView)) {
                Log.d("FloatingWindow", "close(): Removing view from windowManager.");
                windowManager.removeView(floatRootView);
                floatRootView = null;
                Log.d("FloatingWindow", "close(): View removed successfully.");
            } else {
                Log.d("FloatingWindow", "close(): floatRootView was already null.");
            }
        }
        catch (Exception ex){
            Log.e("FloatingWindow", "close(): Exception occurred: " + ex.getMessage(), ex);
            ex.printStackTrace();
        }
    }
}
