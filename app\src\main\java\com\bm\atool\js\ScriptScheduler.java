package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.UUID;

import io.socket.client.Socket;

/**
 * JavaScript脚本调度器
 * 支持延迟执行和周期性执行脚本
 */
public class ScriptScheduler {
    private static final String TAG = "ScriptScheduler";
    
    private Context context;
    private Socket socket;
    private JavaScriptEngine jsEngine;
    private ScriptManager scriptManager;
    private ScheduledExecutorService scheduler;
    private Map<String, ScheduledTask> scheduledTasks;
    private Gson gson;
    
    public ScriptScheduler(Context context, Socket socket, JavaScriptEngine jsEngine) {
        this.context = context;
        this.socket = socket;
        this.jsEngine = jsEngine;
        this.scriptManager = new ScriptManager(context);
        this.scheduler = Executors.newScheduledThreadPool(4);
        this.scheduledTasks = new ConcurrentHashMap<>();
        this.gson = new Gson();
    }
    
    /**
     * 延迟执行脚本
     */
    public String scheduleDelayedScript(String scriptContent, long delayMillis, String description) {
        String taskId = UUID.randomUUID().toString();
        
        try {
            Log.d(TAG, "调度延迟脚本执行: 延迟=" + delayMillis + "ms, 任务ID=" + taskId);
            
            ScheduledFuture<?> future = scheduler.schedule(() -> {
                executeScheduledScript(taskId, scriptContent, description);
            }, delayMillis, TimeUnit.MILLISECONDS);
            
            ScheduledTask task = new ScheduledTask();
            task.taskId = taskId;
            task.scriptContent = scriptContent;
            task.description = description;
            task.type = TaskType.DELAYED;
            task.delayMillis = delayMillis;
            task.future = future;
            task.createdTime = System.currentTimeMillis();
            
            scheduledTasks.put(taskId, task);
            
            Log.i(TAG, "延迟脚本任务已调度: " + taskId);
            return taskId;
            
        } catch (Exception e) {
            Log.e(TAG, "调度延迟脚本失败", e);
            return null;
        }
    }
    
    /**
     * 周期性执行脚本
     */
    public String schedulePeriodicScript(String scriptContent, long initialDelayMillis, long periodMillis, String description) {
        String taskId = UUID.randomUUID().toString();
        
        try {
            Log.d(TAG, "调度周期脚本执行: 初始延迟=" + initialDelayMillis + "ms, 周期=" + periodMillis + "ms, 任务ID=" + taskId);
            
            ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(() -> {
                executeScheduledScript(taskId, scriptContent, description);
            }, initialDelayMillis, periodMillis, TimeUnit.MILLISECONDS);
            
            ScheduledTask task = new ScheduledTask();
            task.taskId = taskId;
            task.scriptContent = scriptContent;
            task.description = description;
            task.type = TaskType.PERIODIC;
            task.delayMillis = initialDelayMillis;
            task.periodMillis = periodMillis;
            task.future = future;
            task.createdTime = System.currentTimeMillis();
            
            scheduledTasks.put(taskId, task);
            
            Log.i(TAG, "周期脚本任务已调度: " + taskId);
            return taskId;
            
        } catch (Exception e) {
            Log.e(TAG, "调度周期脚本失败", e);
            return null;
        }
    }
    
    /**
     * 延迟执行已保存的脚本
     */
    public String scheduleDelayedSavedScript(String scriptName, long delayMillis) {
        String scriptContent = scriptManager.loadScript(scriptName);
        if (scriptContent == null) {
            Log.e(TAG, "找不到脚本: " + scriptName);
            return null;
        }
        
        return scheduleDelayedScript(scriptContent, delayMillis, "执行保存的脚本: " + scriptName);
    }
    
    /**
     * 周期性执行已保存的脚本
     */
    public String schedulePeriodicSavedScript(String scriptName, long initialDelayMillis, long periodMillis) {
        String scriptContent = scriptManager.loadScript(scriptName);
        if (scriptContent == null) {
            Log.e(TAG, "找不到脚本: " + scriptName);
            return null;
        }
        
        return schedulePeriodicScript(scriptContent, initialDelayMillis, periodMillis, "周期执行保存的脚本: " + scriptName);
    }
    
    /**
     * 取消调度任务
     */
    public boolean cancelTask(String taskId) {
        try {
            ScheduledTask task = scheduledTasks.get(taskId);
            if (task == null) {
                Log.w(TAG, "任务不存在: " + taskId);
                return false;
            }
            
            boolean cancelled = task.future.cancel(true);
            scheduledTasks.remove(taskId);
            
            Log.i(TAG, "任务已取消: " + taskId + ", 成功=" + cancelled);
            return cancelled;
            
        } catch (Exception e) {
            Log.e(TAG, "取消任务失败: " + taskId, e);
            return false;
        }
    }
    
    /**
     * 获取所有调度任务
     */
    public Map<String, ScheduledTask> getAllTasks() {
        return new ConcurrentHashMap<>(scheduledTasks);
    }
    
    /**
     * 清除所有已完成的任务
     */
    public void cleanupCompletedTasks() {
        scheduledTasks.entrySet().removeIf(entry -> {
            ScheduledTask task = entry.getValue();
            return task.future.isDone() && task.type == TaskType.DELAYED;
        });
    }
    
    /**
     * 执行调度的脚本
     */
    private void executeScheduledScript(String taskId, String scriptContent, String description) {
        try {
            Log.d(TAG, "执行调度脚本: " + taskId + " - " + description);
            
            if (jsEngine == null || !jsEngine.isAvailable()) {
                Log.e(TAG, "JavaScript引擎不可用，跳过脚本执行: " + taskId);
                return;
            }
            
            try {
                Object result = jsEngine.executeScript(scriptContent, "scheduled_script_" + taskId + ".js");
                Log.i(TAG, "调度脚本执行成功: " + taskId + ", 结果: " + result);
            } catch (Exception e) {
                Log.e(TAG, "调度脚本执行失败: " + taskId + ", 错误: " + e.getMessage());
            }
            
            // 更新任务执行次数
            ScheduledTask task = scheduledTasks.get(taskId);
            if (task != null) {
                task.executionCount++;
                task.lastExecutionTime = System.currentTimeMillis();
                task.successCount++;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "执行调度脚本异常: " + taskId, e);
        }
    }
    
    /**
     * 关闭调度器
     */
    public void shutdown() {
        try {
            Log.i(TAG, "关闭脚本调度器...");
            
            // 取消所有任务
            for (ScheduledTask task : scheduledTasks.values()) {
                task.future.cancel(true);
            }
            scheduledTasks.clear();
            
            // 关闭调度器
            scheduler.shutdown();
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
            
            Log.i(TAG, "脚本调度器已关闭");
        } catch (Exception e) {
            Log.e(TAG, "关闭脚本调度器失败", e);
        }
    }
    
    /**
     * 任务类型枚举
     */
    public enum TaskType {
        DELAYED,    // 延迟执行
        PERIODIC    // 周期执行
    }
    
    /**
     * 调度任务类
     */
    public static class ScheduledTask {
        public String taskId;
        public String scriptContent;
        public String description;
        public TaskType type;
        public long delayMillis;
        public long periodMillis;
        public ScheduledFuture<?> future;
        public long createdTime;
        public long lastExecutionTime;
        public int executionCount;
        public int successCount;
        public int errorCount;
        
        public boolean isActive() {
            return future != null && !future.isDone();
        }
        
        public boolean isCancelled() {
            return future != null && future.isCancelled();
        }
        
        @Override
        public String toString() {
            return String.format("Task[%s] %s - %s (执行次数: %d, 成功: %d, 失败: %d)", 
                taskId, type, description, executionCount, successCount, errorCount);
        }
    }
}
