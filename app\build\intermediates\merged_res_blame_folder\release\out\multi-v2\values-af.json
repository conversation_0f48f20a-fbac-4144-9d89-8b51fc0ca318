{"logs": [{"outputFile": "com.bm.atool.app-mergeReleaseResources-43:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,345,484,653,732", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "171,259,340,479,648,727,804"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4056,4191,8239,8397,8718,8887,8966", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "4122,4274,8315,8531,8882,8961,9038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,157", "endColumns": "101,116", "endOffsets": "152,269"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8020,8122", "endColumns": "101,116", "endOffsets": "8117,8234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3029,3127,3229,3327,3425,3532,3641,8617", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3122,3224,3322,3420,3527,3636,3756,8713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,307,407,521,602,666,754,820,883,969,1030,1088,1154,1217,1272,1390,1447,1509,1564,1633,1752,1840,1923,2032,2115,2196,2283,2350,2416,2485,2561,2647,2721,2800,2873,2944,3031,3102,3191,3281,3353,3428,3515,3566,3633,3714,3798,3860,3924,3987,4091,4200,4296,4407", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,113,80,63,87,65,62,85,60,57,65,62,54,117,56,61,54,68,118,87,82,108,82,80,86,66,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,76", "endOffsets": "224,302,402,516,597,661,749,815,878,964,1025,1083,1149,1212,1267,1385,1442,1504,1559,1628,1747,1835,1918,2027,2110,2191,2278,2345,2411,2480,2556,2642,2716,2795,2868,2939,3026,3097,3186,3276,3348,3423,3510,3561,3628,3709,3793,3855,3919,3982,4086,4195,4291,4402,4479"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2951,3761,3861,3975,4127,4279,4367,4433,4496,4582,4643,4701,4767,4830,4885,5003,5060,5122,5177,5246,5365,5453,5536,5645,5728,5809,5896,5963,6029,6098,6174,6260,6334,6413,6486,6557,6644,6715,6804,6894,6966,7041,7128,7179,7246,7327,7411,7473,7537,7600,7704,7813,7909,8320", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,77,99,113,80,63,87,65,62,85,60,57,65,62,54,117,56,61,54,68,118,87,82,108,82,80,86,66,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,76", "endOffsets": "274,3024,3856,3970,4051,4186,4362,4428,4491,4577,4638,4696,4762,4825,4880,4998,5055,5117,5172,5241,5360,5448,5531,5640,5723,5804,5891,5958,6024,6093,6169,6255,6329,6408,6481,6552,6639,6710,6799,6889,6961,7036,7123,7174,7241,7322,7406,7468,7532,7595,7699,7808,7904,8015,8392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "279,387,483,589,674,777,895,972,1048,1139,1232,1327,1421,1520,1613,1708,1807,1902,1996,2077,2184,2289,2386,2494,2597,2699,2853,8536", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "382,478,584,669,772,890,967,1043,1134,1227,1322,1416,1515,1608,1703,1802,1897,1991,2072,2179,2284,2381,2489,2592,2694,2848,2946,8612"}}]}]}