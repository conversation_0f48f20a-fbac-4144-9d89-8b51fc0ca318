package com.bm.atool.js;

import android.content.Context;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * ScriptDownloader 功能测试
 */
@RunWith(AndroidJUnit4.class)
public class ScriptDownloaderTest {
    
    private Context context;
    private ScriptDownloader scriptDownloader;
    
    @Before
    public void setUp() {
        context = ApplicationProvider.getApplicationContext();
        scriptDownloader = new ScriptDownloader(context);
    }
    
    @After
    public void tearDown() {
        if (scriptDownloader != null) {
            scriptDownloader.cleanup();
        }
    }
    
    @Test
    public void testDownloaderInitialization() {
        assertNotNull("ScriptDownloader应该初始化成功", scriptDownloader);
    }
    
    @Test
    public void testHttpDownloadWithValidUrl() {
        // 使用一个简单的测试URL
        String testUrl = "https://httpbin.org/json";
        
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript(testUrl, 10);
            
            ScriptDownloader.DownloadResult result = future.get(15, TimeUnit.SECONDS);
            
            assertNotNull("下载结果不应该为空", result);
            assertTrue("下载应该成功", result.success);
            assertNotNull("脚本内容不应该为空", result.script);
            assertFalse("脚本内容不应该为空字符串", result.script.isEmpty());
            
        } catch (Exception e) {
            fail("HTTP下载测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testHttpDownloadWithInvalidUrl() {
        String invalidUrl = "https://invalid-url-that-does-not-exist.com/script.js";
        
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript(invalidUrl, 5);
            
            ScriptDownloader.DownloadResult result = future.get(10, TimeUnit.SECONDS);
            
            assertNotNull("下载结果不应该为空", result);
            assertFalse("下载应该失败", result.success);
            assertNotNull("应该有错误信息", result.error);
            
        } catch (Exception e) {
            fail("无效URL测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testHttpDownloadTimeout() {
        // 使用一个会延迟响应的URL来测试超时
        String slowUrl = "https://httpbin.org/delay/10"; // 延迟10秒
        
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript(slowUrl, 2); // 2秒超时
            
            ScriptDownloader.DownloadResult result = future.get(5, TimeUnit.SECONDS);
            
            assertNotNull("下载结果不应该为空", result);
            assertFalse("下载应该因超时失败", result.success);
            assertNotNull("应该有超时错误信息", result.error);
            assertTrue("错误信息应该包含超时相关内容", 
                      result.error.toLowerCase().contains("timeout") || 
                      result.error.toLowerCase().contains("超时"));
            
        } catch (Exception e) {
            fail("超时测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testSocketScriptRequest() {
        // 测试Socket脚本请求功能
        String scriptId = "test_script_001";
        
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.requestScriptViaSocket(scriptId, 10);
            
            // 由于没有实际的Socket服务器，这个测试可能会失败
            // 但我们可以验证方法调用不会抛出异常
            ScriptDownloader.DownloadResult result = future.get(15, TimeUnit.SECONDS);
            
            assertNotNull("Socket请求结果不应该为空", result);
            // 由于没有服务器，预期会失败
            assertFalse("Socket请求应该失败（无服务器）", result.success);
            
        } catch (Exception e) {
            // 预期的异常，因为没有Socket服务器
            assertTrue("应该是连接相关的异常", 
                      e.getMessage().contains("connect") || 
                      e.getMessage().contains("connection") ||
                      e.getMessage().contains("timeout"));
        }
    }
    
    @Test
    public void testMultipleDownloads() {
        // 测试并发下载
        String[] urls = {
            "https://httpbin.org/json",
            "https://httpbin.org/uuid",
            "https://httpbin.org/ip"
        };
        
        try {
            CompletableFuture<ScriptDownloader.DownloadResult>[] futures = new CompletableFuture[urls.length];
            
            // 启动所有下载
            for (int i = 0; i < urls.length; i++) {
                futures[i] = scriptDownloader.downloadScript(urls[i], 10);
            }
            
            // 等待所有下载完成
            int successCount = 0;
            for (int i = 0; i < futures.length; i++) {
                ScriptDownloader.DownloadResult result = futures[i].get(15, TimeUnit.SECONDS);
                assertNotNull("下载结果 " + i + " 不应该为空", result);
                if (result.success) {
                    successCount++;
                }
            }
            
            assertTrue("至少应该有一个下载成功", successCount > 0);
            
        } catch (Exception e) {
            fail("并发下载测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testDownloadResultStructure() {
        // 测试下载结果的数据结构
        String testUrl = "https://httpbin.org/json";
        
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript(testUrl, 10);
            
            ScriptDownloader.DownloadResult result = future.get(15, TimeUnit.SECONDS);
            
            assertNotNull("下载结果不应该为空", result);
            
            // 检查结果结构
            if (result.success) {
                assertNotNull("成功时脚本内容不应该为空", result.script);
                assertNull("成功时错误信息应该为空", result.error);
                assertTrue("脚本内容应该有实际内容", result.script.length() > 0);
            } else {
                assertNotNull("失败时错误信息不应该为空", result.error);
                assertTrue("错误信息应该有实际内容", result.error.length() > 0);
            }
            
        } catch (Exception e) {
            fail("结果结构测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testCleanupFunction() {
        // 测试清理功能
        try {
            scriptDownloader.cleanup();
            // 清理后应该仍然可以创建新的下载任务
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript("https://httpbin.org/json", 5);
            
            ScriptDownloader.DownloadResult result = future.get(10, TimeUnit.SECONDS);
            assertNotNull("清理后仍应该能够下载", result);
            
        } catch (Exception e) {
            fail("清理功能测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testErrorHandling() {
        // 测试各种错误情况
        
        // 1. 空URL
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript("", 5);
            
            ScriptDownloader.DownloadResult result = future.get(10, TimeUnit.SECONDS);
            assertNotNull("空URL结果不应该为空", result);
            assertFalse("空URL应该失败", result.success);
            
        } catch (Exception e) {
            // 也可能直接抛出异常
            assertTrue("应该是URL相关的异常", 
                      e.getMessage().contains("URL") || 
                      e.getMessage().contains("url"));
        }
        
        // 2. 无效超时值
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript("https://httpbin.org/json", -1);
            
            ScriptDownloader.DownloadResult result = future.get(10, TimeUnit.SECONDS);
            assertNotNull("无效超时结果不应该为空", result);
            // 应该使用默认超时值或失败
            
        } catch (Exception e) {
            // 也可能直接抛出异常
            assertTrue("应该是超时相关的异常", 
                      e.getMessage().contains("timeout") || 
                      e.getMessage().contains("超时"));
        }
    }
    
    @Test
    public void testLargeScriptDownload() {
        // 测试下载较大的脚本文件
        String largeUrl = "https://httpbin.org/base64/SFRUUEJJTiBpcyBhd2Vzb21l"; // 返回较大内容
        
        try {
            CompletableFuture<ScriptDownloader.DownloadResult> future = 
                scriptDownloader.downloadScript(largeUrl, 15);
            
            ScriptDownloader.DownloadResult result = future.get(20, TimeUnit.SECONDS);
            
            assertNotNull("大文件下载结果不应该为空", result);
            if (result.success) {
                assertNotNull("大文件内容不应该为空", result.script);
                assertTrue("大文件应该有内容", result.script.length() > 0);
            }
            
        } catch (Exception e) {
            fail("大文件下载测试失败: " + e.getMessage());
        }
    }
}
