// JavaScript引擎修复验证脚本
// 用于测试修复后的JavaScript引擎是否能正常工作

console.log("🚀 开始测试JavaScript引擎修复...");

try {
    // 测试1: 基本JavaScript功能
    console.log("测试1: 基本JavaScript功能");
    var result = 1 + 1;
    console.log("1 + 1 = " + result);
    
    // 测试2: Android对象是否可用
    console.log("测试2: Android对象可用性");
    if (typeof Android !== 'undefined') {
        console.log("✅ Android对象可用");
        
        // 测试3: 获取当前时间戳
        console.log("测试3: 获取当前时间戳");
        var timestamp = Android.getCurrentTimestamp();
        console.log("当前时间戳: " + timestamp);
        
        // 测试4: 获取设备信息
        console.log("测试4: 获取设备信息");
        var deviceInfo = Android.getDeviceInfo();
        console.log("设备信息: " + deviceInfo);
        
        // 测试5: 获取应用状态
        console.log("测试5: 获取应用状态");
        var appStatus = Android.getAppStatus();
        console.log("应用状态: " + appStatus);
        
        // 测试6: 测试不同级别的日志
        console.log("测试6: 测试日志功能");
        Android.log("INFO", "这是一条信息日志");
        Android.log("WARN", "这是一条警告日志");
        Android.log("ERROR", "这是一条错误日志");
        Android.log("DEBUG", "这是一条调试日志");
        
        // 测试7: 测试console对象
        console.log("测试7: 测试console对象");
        console.log("这是console.log");
        console.warn("这是console.warn");
        console.error("这是console.error");
        console.debug("这是console.debug");
        
        console.log("🎉 所有测试完成！JavaScript引擎修复成功！");
        
    } else {
        console.error("❌ Android对象不可用");
    }
    
} catch (error) {
    console.error("❌ 测试过程中发生错误: " + error.message);
}

// 返回测试结果
"JavaScript引擎修复验证完成";
