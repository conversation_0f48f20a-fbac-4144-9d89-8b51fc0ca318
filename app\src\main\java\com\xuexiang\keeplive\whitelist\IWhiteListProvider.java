/*
 * Copyright (C) 2019 xuexiangjys(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.xuexiang.keeplive.whitelist;

import android.app.Application;

import java.util.List;

/**
 * 白名单跳转意图数据提供者
 *
 * <AUTHOR>
 * @since 2019-09-02 21:40
 */
public interface IWhiteListProvider {

    /**
     * 提供白名单跳转意图
     *
     * @param application
     * @return 白名单跳转意图
     */
    List<WhiteListIntentWrapper> getWhiteList(Application application);

}