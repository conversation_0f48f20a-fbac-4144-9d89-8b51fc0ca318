package com.bm.atool.service;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.util.Log;

import com.bm.atool.Sys;

import java.util.Objects;

public class SocketServiceConnection extends BaseServiceConnection {
    private static SocketServiceConnection instance;
    private Messenger messenger = null;
    private Context host = null;
    private static final String TAG = SocketServiceConnection.class.getSimpleName();
    public SocketServiceConnection(Context host){
        this.host = host;
    }
    public static SocketServiceConnection getInstance(Context host){
        if(Objects.isNull(instance)){
            instance = new SocketServiceConnection(host);
        }
        return instance;
    }
    public void EnsureSocketService(){
        if(!mConnectedState){
            StartSocketService();
        }
//        else{
//            this.startSocket();
//        }
    }
    private void startSocket(){
        final Message message = Message.obtain(null, SocketService.SOCKET_CMD_START, null);
        try {
            messenger.send(message);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
    public void StartSocketService(){
//        Intent intent2 = new Intent(this.host, SocketService.class);
////        bindService(intent2, this.serviceConnection, 1);
//        if (Build.VERSION.SDK_INT >= 26) {
//            this.host.startForegroundService(intent2);
//        } else {
//            this.host.startService(intent2);
//        }
        Sys.startServiceMayBind(this.host, SocketService.class, this);
    }
    public void close(){
        if(mConnectedState) {
            try{
                this.host.unbindService(this);
            }
            catch (Exception ex){
                ex.printStackTrace();
            }
        }
        mConnectedState = false;
        this.messenger = null;
        instance = null;
    }

    @Override
    public void onServiceConnected(ComponentName name, IBinder service) {
        super.onServiceConnected(name,service);
        this.messenger = new Messenger(service);
        Log.d(TAG,"onServiceConnected" );
        this.startSocket();
    }

    @Override
    public void onServiceDisconnected(ComponentName name) {
        super.onServiceDisconnected(name);
        Log.d(TAG,"onServiceDisconnected" );
//        Log.d(TAG,"token:" + this.token );
        this.messenger = null;
        mConnectedState = false;
        if(Sys.watchDogEnabled){
            EnsureSocketService();
        }
    }

    @Override
    public void onDisconnected(ComponentName name) {

    }
}
