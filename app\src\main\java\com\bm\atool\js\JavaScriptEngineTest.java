package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

/**
 * JavaScript引擎功能测试类
 * 用于验证JavaScript引擎的基本功能
 */
public class JavaScriptEngineTest {
    private static final String TAG = "JavaScriptEngineTest";
    
    /**
     * 运行JavaScript引擎测试
     * @param context Android上下文
     */
    public static void runTests(Context context) {
        Log.d(TAG, "开始JavaScript引擎测试");
        
        JavaScriptEngine jsEngine = null;
        try {
            // 初始化JavaScript引擎
            jsEngine = new JavaScriptEngine(context, null);
            Log.d(TAG, "JavaScript引擎初始化成功: " + jsEngine.isAvailable());
            
            if (!jsEngine.isAvailable()) {
                Log.e(TAG, "JavaScript引擎不可用");
                return;
            }
            
            // 测试基本计算
            testBasicCalculation(jsEngine);
            
            // 测试字符串操作
            testStringOperations(jsEngine);
            
            // 测试函数定义和调用
            testFunctionExecution(jsEngine);
            
            // 测试复杂脚本
            testComplexScript(jsEngine);
            
            // 测试Android桥接
            testAndroidBridge(jsEngine);
            
            Log.d(TAG, "JavaScript引擎测试完成");
            
        } catch (Exception e) {
            Log.e(TAG, "JavaScript引擎测试失败", e);
        } finally {
            if (jsEngine != null) {
                jsEngine.close();
            }
        }
    }
    
    private static void testBasicCalculation(JavaScriptEngine jsEngine) {
        try {
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("1 + 1");
            Log.d(TAG, "基本计算测试: 1 + 1 = " + result.result + 
                  ", success: " + result.success + 
                  ", error: " + result.error);
        } catch (Exception e) {
            Log.e(TAG, "基本计算测试失败", e);
        }
    }
    
    private static void testStringOperations(JavaScriptEngine jsEngine) {
        try {
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("'Hello ' + 'World'");
            Log.d(TAG, "字符串操作测试: 'Hello ' + 'World' = " + result.result + 
                  ", success: " + result.success + 
                  ", error: " + result.error);
        } catch (Exception e) {
            Log.e(TAG, "字符串操作测试失败", e);
        }
    }
    
    private static void testFunctionExecution(JavaScriptEngine jsEngine) {
        try {
            // 定义函数
            jsEngine.executeScript("function add(a, b) { return a + b; }");
            // 调用函数
            JavaScriptEngine.ScriptResult result = jsEngine.executeFunction("add", 5, 3);
            Log.d(TAG, "函数执行测试: add(5, 3) = " + result.result + 
                  ", success: " + result.success + 
                  ", error: " + result.error);
        } catch (Exception e) {
            Log.e(TAG, "函数执行测试失败", e);
        }
    }
    
    private static void testComplexScript(JavaScriptEngine jsEngine) {
        try {
            String fibonacciScript = 
                "function fibonacci(n) {" +
                "  if (n <= 1) return n;" +
                "  return fibonacci(n - 1) + fibonacci(n - 2);" +
                "}" +
                "fibonacci(10)";
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript(fibonacciScript);
            Log.d(TAG, "复杂脚本测试: fibonacci(10) = " + result.result + 
                  ", success: " + result.success + 
                  ", error: " + result.error);
        } catch (Exception e) {
            Log.e(TAG, "复杂脚本测试失败", e);
        }
    }
    
    private static void testAndroidBridge(JavaScriptEngine jsEngine) {
        try {
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof Android");
            Log.d(TAG, "Android桥接测试: typeof Android = " + result.result + 
                  ", success: " + result.success + 
                  ", error: " + result.error);
        } catch (Exception e) {
            Log.e(TAG, "Android桥接测试失败", e);
        }
    }
}