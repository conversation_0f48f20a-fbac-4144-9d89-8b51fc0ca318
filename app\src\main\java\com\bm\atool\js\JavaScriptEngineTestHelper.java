package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

/**
 * JavaScript引擎测试辅助类
 * 提供各种测试功能来验证JavaScript引擎的正确性
 */
public class JavaScriptEngineTestHelper {
    private static final String TAG = "JSEngineTestHelper";

    /**
     * 测试结果类
     */
    public static class TestResult {
        public final boolean success;
        public final String message;

        public TestResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }

    /**
     * 测试JavaScript引擎初始化
     * @param context Android上下文
     * @return 测试结果
     */
    public static TestResult testEngineInitialization(Context context) {
        JavaScriptEngine jsEngine = null;
        try {
            Log.i(TAG, "开始测试JavaScript引擎初始化...");

            jsEngine = new JavaScriptEngine(context, null);

            if (!jsEngine.isAvailable()) {
                return new TestResult(false, "JavaScript引擎初始化后不可用");
            }

            // 测试基本的JavaScript执行
            try {
                Object result = jsEngine.executeScript("1 + 1", "test.js");
                if (result == null) {
                    return new TestResult(false, "基本JavaScript执行返回null");
                }
            } catch (Exception e) {
                return new TestResult(false, "基本JavaScript执行失败: " + e.getMessage());
            }

            Log.i(TAG, "JavaScript引擎初始化测试成功");
            return new TestResult(true, "JavaScript引擎初始化成功，基本功能正常");

        } catch (Exception e) {
            Log.e(TAG, "JavaScript引擎初始化测试失败", e);
            return new TestResult(false, "JavaScript引擎初始化异常: " + e.getMessage());
        } finally {
            if (jsEngine != null) {
                jsEngine.close();
            }
        }
    }

    /**
     * 运行所有测试
     * @param context Android上下文
     * @return 测试结果
     */
    public static TestResult runAllTests(Context context) {
        Log.i(TAG, "开始运行所有JavaScript引擎测试...");

        // 测试引擎初始化
        TestResult initResult = testEngineInitialization(context);
        if (!initResult.success) {
            return new TestResult(false, "引擎初始化测试失败: " + initResult.message);
        }

        Log.i(TAG, "所有JavaScript引擎测试通过");
        return new TestResult(true, "所有测试通过");
    }
}