// 测试获取设备信息
console.log("开始获取设备信息...");

try {
    var deviceInfo = System.getDeviceInfo();
    console.log("设备信息获取成功:");
    console.log("品牌: " + JSON.parse(deviceInfo).brand);
    console.log("型号: " + JSON.parse(deviceInfo).model);
    console.log("制造商: " + JSON.parse(deviceInfo).manufacturer);
    console.log("Android版本: " + JSON.parse(deviceInfo).androidVersion);
    
    "设备信息获取成功: " + deviceInfo;
} catch (e) {
    console.error("获取设备信息失败: " + e.message);
    "错误: " + e.message;
}
