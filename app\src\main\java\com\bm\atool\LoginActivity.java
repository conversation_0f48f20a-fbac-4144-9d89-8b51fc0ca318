package com.bm.atool;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.model.ApiFacotry;
import com.bm.atool.model.LoginRequest;
import com.bm.atool.model.LoginResponse;
import com.bm.atool.model.PermissionModel;
import com.bm.atool.model.Response;
import com.bm.atool.utils.PermissionUtils;
//import com.bm.atool.R;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import retrofit2.Call;
import retrofit2.Callback;

public class LoginActivity extends AppCompatActivity {
    private static final String TAG = LoginActivity.class.getSimpleName();
    private ProgressDialog progressDialog;
    private boolean isReturningFromPermissionRequest = false;
    private static final int PERMISSION_REQUEST_CODE = 1;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

//        EditText password2 = (EditText) findViewById(R.id.et_password);
//        password2.setOnEditorActionListener(new TextView.OnEditorActionListener() {
//            @Override // android.widget.TextView.OnEditorActionListener
//            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
//                if (actionId == 6) {
//                    hideInputKeyboard();
//                    return true;
//                }
//                return true;
//            }
//        });
        Button submit = (Button) findViewById(R.id.btn_Login);
        submit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                hideInputKeyboard();
                checkAllPermissionsAndProceed();
            }
        });
        
        // 在Activity创建时就尝试检查并请求所有权限
        if (!isReturningFromPermissionRequest) {
            new Handler().postDelayed(this::checkAllPermissionsAndProceed, 500);
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        if (isReturningFromPermissionRequest) {
            isReturningFromPermissionRequest = false;
            new Handler().postDelayed(this::checkAllPermissionsAndProceed, 500);
        }
    }
    
    private void checkAllPermissionsAndProceed() {
        Log.d(TAG, "checkAllPermissionsAndProceed: Starting permission check...");
        String appName = getApplicationInfo().loadLabel(getPackageManager()).toString();

        ArrayList<PermissionModel> allPermissions = PermissionUtils.getAllPermissions(this);
        
        // 1. 检查并请求运行时权限
        List<String> ungrantedRuntimePermissions = allPermissions.stream()
                .filter(p -> p.fullName != null && !p.granted 
                        && !p.fullName.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
                        && !p.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                        && !p.name.equals("Accessibility"))
                .map(p -> p.fullName)
                .collect(Collectors.toList());

        if (!ungrantedRuntimePermissions.isEmpty()) {
            String[] permissionsArray = ungrantedRuntimePermissions.toArray(new String[0]);
            Log.d(TAG, "checkAllPermissionsAndProceed: Requesting runtime permissions: " + String.join(", ", permissionsArray));
            ActivityCompat.requestPermissions(this, permissionsArray, PERMISSION_REQUEST_CODE);
            return;
        }
        
        // 2. 检查并请求特殊权限（无障碍、悬浮窗、电池优化）
        boolean hasAccessibility = PermissionUtils.isAccessibilitySettingsOn(this);
        boolean canDrawOverlays = PermissionUtils.canDrawOverlays(this);
        boolean ignoringBatteryOptimizations = PermissionUtils.isIgnoringBatteryOptimizations(this);

        if (!hasAccessibility) {
            Toast.makeText(this, "Please grant Accessibility permission for " + appName, Toast.LENGTH_LONG).show();
            PermissionUtils.accessibilityToSettingPage(this);
            isReturningFromPermissionRequest = true;
            return;
        }

        if (!canDrawOverlays) {
            Toast.makeText(this, "Please grant Display over other apps permission for " + appName, Toast.LENGTH_LONG).show();
            PermissionUtils.manageOverlayToSettingPage(this);
            isReturningFromPermissionRequest = true;
            return;
        }

        if (!ignoringBatteryOptimizations) {
            Toast.makeText(this, "Please grant Battery Optimization permission for " + appName, Toast.LENGTH_LONG).show();
            PermissionUtils.batteryOptimizationToSettingPage(this);
            isReturningFromPermissionRequest = true;
            return;
        }
        
        // 所有权限都已授予，可以进行登录
        Log.d(TAG, "checkAllPermissionsAndProceed: All permissions granted. Proceeding to login.");
        loginProceed();
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            Sys.onPermissionChangeEvent(PermissionUtils.getAllPermissions(this)); // 更新权限模型状态

            if (allGranted) {
                Log.d(TAG, "onRequestPermissionsResult: Runtime permissions granted, checking special permissions.");
                // 运行时权限授予后，继续检查特殊权限
                new Handler().postDelayed(this::checkAllPermissionsAndProceed, 500); // 延迟检查以防UI未完全更新
            } else {
                Log.d(TAG, "onRequestPermissionsResult: Some runtime permissions denied. Re-checking all permissions.");
                Toast.makeText(this, "Some permissions were denied. Please grant them.", Toast.LENGTH_LONG).show();
                new Handler().postDelayed(this::checkAllPermissionsAndProceed, 500);
            }
        }
    }

    private LoginRequest makeLoginRequest(){
        EditText username = (EditText) findViewById(R.id.et_account);
        EditText password = (EditText) findViewById(R.id.et_password);
        final String usernameText = username.getText().toString();
        String passwordText = password.getText().toString();
        if (usernameText.isEmpty()) {
            //Toast.makeText(this, "Please enter Your User Name", Toast.LENGTH_SHORT).show();
            return null;
        }
        if (passwordText.isEmpty()) {
            //Toast.makeText(this, "Please enter your Password", Toast.LENGTH_SHORT).show();
            return null;
        }
        LoginRequest req = new LoginRequest();
        req.setUsername(usernameText);
        req.setPassword(passwordText);
        req.setTenantId(Env.tenantId);
        return req;
    }
    
    // 实际的登录逻辑
    private void loginProceed() {
        LoginRequest req = this.makeLoginRequest();
        if(Objects.isNull(req)){
            return;
        }

        LoginActivity that = this;
        try {
            showProcessing();
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Call<Response<LoginResponse>> responseCall = ApiFacotry.getSmsApi().login(req);
                        responseCall.enqueue(new Callback<Response<LoginResponse>>() {
                            @Override
                            public void onResponse(Call<Response<LoginResponse>> call, retrofit2.Response<Response<LoginResponse>> response) {
                                if(Objects.isNull(response) || Objects.isNull(response.body())){
                                    showError("Login response is empty");
                                    return;
                                }
                                stopProcessing();

                                if(response.isSuccessful() && response.body().isSuccess()) {
                                    Sys.updateLogin(req.getUsername(),response.body().getData().getToken());
                                    Intent intent = new Intent(that,MainActivity.class);
                                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                                    that.startActivity(intent);
                                    finish();
                                } else {
                                    String msg = response.body().getMessage();
                                    if(Objects.isNull(msg) || msg.length() <1){
                                        msg ="login failed";
                                    }
                                    showError("error:" + msg);
                                }
                            }

                            @Override
                            public void onFailure(Call<Response<LoginResponse>> call, Throwable throwable) {
                                stopProcessing();
                                showError("error:" + throwable.getMessage());
                                throwable.printStackTrace();
                            }
                        });
                    }
                    catch (Exception ex){
                        stopProcessing();
                        ex.printStackTrace();
                        showError("error:" + ex.getMessage());
                    }
                    that.stopProcessing();
                }
                void showError(String err){
                    LoginActivity.this.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            try{
                                Toast.makeText(that, err, Toast.LENGTH_LONG).show();
                            }
                            catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }
                    });
                }
            }).start();
        } catch (Exception e) {
            e.printStackTrace();
            stopProcessing();
        }
    }

    private void hideInputKeyboard() {
        View currentFocus = getCurrentFocus();
        if (currentFocus == null) return;
        IBinder windowToken = currentFocus.getWindowToken();
        if (windowToken == null) return;
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(windowToken, 0);
    }

    private  void showProcessing(){
        if( Objects.isNull(this.progressDialog)){
            progressDialog = new ProgressDialog(this);
        }
        progressDialog.setMessage("processing....");
        progressDialog.setCancelable(false);
        if (!progressDialog.isShowing()) {
            progressDialog.show();
        }
    }

    private void stopProcessing() {
        if (!Objects.isNull(this.progressDialog)) {
            if(progressDialog.isShowing()){
                progressDialog.dismiss();
            }
            progressDialog = null;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if(!Sys.isLogin()){
            System.exit(0);
        }
    }
}