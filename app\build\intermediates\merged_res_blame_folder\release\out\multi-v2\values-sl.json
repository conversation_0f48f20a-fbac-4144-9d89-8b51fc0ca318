{"logs": [{"outputFile": "com.bm.atool.app-mergeReleaseResources-43:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "372,484,586,694,781,884,1003,1084,1162,1254,1348,1443,1537,1632,1726,1822,1922,2014,2106,2190,2298,2406,2506,2619,2727,2832,3012,8791", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "479,581,689,776,879,998,1079,1157,1249,1343,1438,1532,1627,1721,1817,1917,2009,2101,2185,2293,2401,2501,2614,2722,2827,3007,3107,8870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,120", "endOffsets": "158,279"}, "to": {"startLines": "97,98", "startColumns": "4,4", "startOffsets": "8263,8371", "endColumns": "107,120", "endOffsets": "8366,8487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,343,484,653,741", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "171,257,338,479,648,736,820"}, "to": {"startLines": "46,48,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4239,4376,8492,8650,8976,9145,9233", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "4305,4457,8568,8786,9140,9228,9312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "36,37,38,39,40,41,42,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3200,3297,3399,3497,3601,3704,3806,8875", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3292,3394,3492,3596,3699,3801,3918,8971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,322,410,516,642,726,792,886,962,1025,1137,1202,1256,1326,1386,1442,1554,1611,1673,1729,1802,1936,2021,2106,2219,2303,2386,2475,2542,2608,2681,2758,2842,2916,2992,3067,3140,3228,3301,3391,3482,3554,3628,3719,3771,3838,3922,4009,4071,4135,4198,4301,4398,4496,4593", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,87,105,125,83,65,93,75,62,111,64,53,69,59,55,111,56,61,55,72,133,84,84,112,83,82,88,66,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,76", "endOffsets": "317,405,511,637,721,787,881,957,1020,1132,1197,1251,1321,1381,1437,1549,1606,1668,1724,1797,1931,2016,2101,2214,2298,2381,2470,2537,2603,2676,2753,2837,2911,2987,3062,3135,3223,3296,3386,3477,3549,3623,3714,3766,3833,3917,4004,4066,4130,4193,4296,4393,4491,4588,4665"}, "to": {"startLines": "2,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3112,3923,4029,4155,4310,4462,4556,4632,4695,4807,4872,4926,4996,5056,5112,5224,5281,5343,5399,5472,5606,5691,5776,5889,5973,6056,6145,6212,6278,6351,6428,6512,6586,6662,6737,6810,6898,6971,7061,7152,7224,7298,7389,7441,7508,7592,7679,7741,7805,7868,7971,8068,8166,8573", "endLines": "7,35,43,44,45,47,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100", "endColumns": "12,87,105,125,83,65,93,75,62,111,64,53,69,59,55,111,56,61,55,72,133,84,84,112,83,82,88,66,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,76", "endOffsets": "367,3195,4024,4150,4234,4371,4551,4627,4690,4802,4867,4921,4991,5051,5107,5219,5276,5338,5394,5467,5601,5686,5771,5884,5968,6051,6140,6207,6273,6346,6423,6507,6581,6657,6732,6805,6893,6966,7056,7147,7219,7293,7384,7436,7503,7587,7674,7736,7800,7863,7966,8063,8161,8258,8645"}}]}]}