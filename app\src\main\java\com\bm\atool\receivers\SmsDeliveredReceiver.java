package com.bm.atool.receivers;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

public class SmsDeliveredReceiver extends BroadcastReceiver {
    private static final String a = SmsDeliveredReceiver.class.getSimpleName();

    @Override // android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        int intExtra = intent.getIntExtra("smsId", -1);
        boolean booleanExtra = intent.getBooleanExtra("isEncrypted", false);

        switch (getResultCode())
        {
            case Activity.RESULT_OK:
                //                        Toast.makeText(getBaseContext(), "SMS delivered",
                //                                Toast.LENGTH_SHORT).show();
                break;
            case Activity.RESULT_CANCELED:
                //                        Toast.makeText(getBaseContext(), "SMS not delivered",
                //                                Toast.LENGTH_SHORT).show();
                break;
        }
    }
}