package com.bm.atool.receivers;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.bm.atool.Sys;
import com.bm.atool.service.WatchDogService;

public class WakeUpAutoStartReceiver extends BroadcastReceiver {

    @SuppressLint("UnsafeProtectedBroadcastReceiver")
    @Override
    public void onReceive(Context context, Intent intent) {
        Sys.startServiceSafely(context, WatchDogService.class);
    }
}