package com.bm.atool;

import android.annotation.SuppressLint;
import android.app.Application;
import android.os.Build;
import android.os.Handler;
import android.util.Log;
import android.content.Context;

import com.bm.atool.service.SocketService;
import com.bm.atool.utils.PermissionUtils;

import me.weishu.reflection.Reflection;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class App extends Application {
    private static final String TAG = App.class.getSimpleName();
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.e(TAG, "on create app: " + getProcessName());
        Sys.app = this;
        Sys.initPhone(this);
        Sys.initPermissions(this);
        Sys.initWatchDogEnabled();
        
        // 在APP启动时尽早初始化Socket
        if (Sys.isLogin()) {
            Log.d(TAG, "用户已登录，尽早初始化Socket连接");
            SocketService.preInitSocketConnection(this);
        }

        Log.d(TAG, "App started, will check permissions after delay");
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                checkRequiredPermissions();

                if (Sys.isLogin()) {
                    SocketService.preInitSocketConnection(App.this);
                }
            }
        }, 1000);
    }
    
    private void checkRequiredPermissions() {
        if (Sys.app != null) {
            Log.d(TAG, "Checking required permissions on app start");
            
            // 确保权限模型已初始化
            if (Sys.permissionModels == null) {
                Sys.initPermissions(this);
            }
            
            // 检查特殊权限
            boolean hasAccessibility = PermissionUtils.isAccessibilitySettingsOn(this);
            boolean canDrawOverlays = PermissionUtils.canDrawOverlays(this);
            boolean ignoringBatteryOptimizations = PermissionUtils.isIgnoringBatteryOptimizations(this);
            
            Log.d(TAG, "Permission status - Accessibility: " + hasAccessibility + 
                    ", Overlay: " + canDrawOverlays + 
                    ", Battery: " + ignoringBatteryOptimizations);
            
            // 保存首次运行状态
            if (PermissionUtils.isFirstRun(this)) {
                PermissionUtils.setFirstRunCompleted(this);
            }
            
            // 注意：在这里不主动请求权限，而是在MainActivity或SettingsFragment中处理
            // 这样可以避免循环启动活动的问题
        }
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        try{
            Reflection.unseal(base);
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
    }

    public static String getProcessName() {
        if (Build.VERSION.SDK_INT >= 28)
            return Application.getProcessName();

        // Using the same technique as Application.getProcessName() for older devices
        // Using reflection since ActivityThread is an internal API

        try {
            @SuppressLint("PrivateApi")
            Class<?> activityThread = Class.forName("android.app.ActivityThread");

            // Before API 18, the method was incorrectly named "currentPackageName", but it still returned the process name
            // See https://github.com/aosp-mirror/platform_frameworks_base/commit/b57a50bd16ce25db441da5c1b63d48721bb90687
            String methodName = Build.VERSION.SDK_INT >= 18 ? "currentProcessName" : "currentPackageName";

            Method getProcessName = activityThread.getDeclaredMethod(methodName);
            return (String) getProcessName.invoke(null);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }
}
