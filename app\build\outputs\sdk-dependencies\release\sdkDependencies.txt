# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.2.0"
  }
  digests {
    sha256: "} \'O\314\257\226\215\033\234{0\246\023c\350\371\274\253\211\331\243\303\240G,g\0163O2`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.22"
  }
  digests {
    sha256: "\003\245\303\226\\\303pQ\022\216d\344gH\343\224\266\275L\227\372\201\306\336o\307+\375D\343B\033"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.22"
  }
  digests {
    sha256: "\320\3026^$7\357p\363E\206\325\017\005WC\367\227\026\274\376e\344\274r9\315\322f\236\367\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.7.2"
  }
  digests {
    sha256: "*\032\277\216\nY\215$k\036\334\267\036#\225\343\23723!\225\353\345\352@\243\335!5[\243\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.0"
  }
  digests {
    sha256: "\033\226\310\353\020\304\264\002\203\375\326\351\252t\377\377\005\372\344\361]T\366\033\246\235Q\177\315\024F\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.4"
  }
  digests {
    sha256: "?\334\016\355[\304\270>\351b\'tR\n-\262Tp7\016\254\321X\034\254\0367pO\t[\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.4"
  }
  digests {
    sha256: "\302L\213\262{\263 \304\2518qP\032~^\fa`v8\220{\031z\357gU\023\324\310 \276"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.2"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.0"
  }
  digests {
    sha256: "Y\305Na)\351&\034m\201\017\216\352Jn\342\364\231M\201b\261\315\237\310\214\234\204\311*\314\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.7.2"
  }
  digests {
    sha256: "\260\264 n\316\222\221\231%\006\037\337W\204\335!\360\021\2054`\236\217m\224\004\275\320\365\313Z="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.5.0"
  }
  digests {
    sha256: "\261M\311o\225\b_&\363j\253p;/y\035\023kq\351\035^\200\360\2406E1\aJi\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.7.7"
  }
  digests {
    sha256: "\346<}.\333cc\220\263\"ETWx~\a\0041H\334\332]\323xf\322\037\002\324U\337K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.7"
  }
  digests {
    sha256: "\v^\aR\207 :6\364j\373L\355\243\226Gh\242x\311\263gh\344\270\313\262\224\357\001\324\364"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.7"
  }
  digests {
    sha256: "9\331\275;\370\201\205\272\327\212\256\320\035\210\223\363u\334\237\016i\236P\227\377dA\242\335\212\257)"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.7.7"
  }
  digests {
    sha256: "\246\320\215\245\245\252\300g9\334\232\036\220H\273M\311\331q\237\377\022z4\263\200\367\333\226\312\223R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.11.0"
  }
  digests {
    sha256: "W\222\215nZn\336\262\253\323w\n\217\225\272D\334\344_;#\267\251\334+0\234X\025R\247\213"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.27.0"
  }
  digests {
    sha256: "$\311#7,X\343]\v\237\026\240(\222\233\271\256\334wR\030g\302t\362\275\a5\337[\241\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.11.0"
  }
  digests {
    sha256: "\237O\273\316pr\205\204\373\356\323\215@a\363mDw\350\233\312t\264\342\254\212\353h\031\260\376C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "3.14.9"
  }
  digests {
    sha256: "%p\372\265U\025\313\370\201\327\244\316\357I\374QT\220\274\002pW\346fwj(2FZ\354\240"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "1.17.2"
  }
  digests {
    sha256: "\370\f\344-/\372\304z\324\304~\035o\230\r`M$|\353\032\210g\005\317E\201\253\f\237\342\270"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.11.0"
  }
  digests {
    sha256: "=+Kf\211\tF\204e\305wd0\265r2\']\001eQ? \374\226\260\317\344\252S\023&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.googlecode.libphonenumber"
    artifactId: "libphonenumber"
    version: "8.13.25"
  }
  digests {
    sha256: "C\365L\264\261\017\220{\035\354;;\277\273\303\353\220*\322gi\332 \261\355\273\224JX\237$\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.socket"
    artifactId: "socket.io-client"
    version: "2.1.1"
  }
  digests {
    sha256: "E]2|\256G\361\026U\033R.c\201h\315\274\300\216 \224z\'f\215\322\340_ne\001\036"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.socket"
    artifactId: "engine.io-client"
    version: "2.1.0"
  }
  digests {
    sha256: "\204\273\224\244\033\v\321\366\365v(\004\354\300K6&\3039z\242\317\274\"\332\272n\260b.\345u"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.javassist"
    artifactId: "javassist"
    version: "3.28.0-GA"
  }
  digests {
    sha256: "W\320\251\351(o\202\364\352\250Q\022Q\206\231\177\201\033\357\316\016 `\377\n\025\247\177Z\235\331\247"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.reactivex.rxjava2"
    artifactId: "rxjava"
    version: "2.2.21"
  }
  digests {
    sha256: "Y\337eA\250@\001\217\017L\211\232\256OL\037C\203\364\301o\353Rha_\2768M(P\034"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.reactivestreams"
    artifactId: "reactive-streams"
    version: "1.0.3"
  }
  digests {
    sha256: "\035\356\004\201\a-\031\311)\266#\341U\341M/`\205\334\001\025)\240\240\333\357\310L\365q\330e"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.microg"
    artifactId: "safe-parcel"
    version: "1.7.0"
  }
  digests {
    sha256: "b\237^\277\333Px\326y\024!\2434\232\331\267\321\251\377m\b\314\023\266\241{\377\276\217\227V\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.0"
  }
  digests {
    sha256: "\306\376\241\216\005]\360^@s\204\243Mx\240\t\363\201\257\005)\333jv\"\253#4\267/\355{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.tananaev"
    artifactId: "adblib"
    version: "1.3"
  }
  digests {
    sha256: "\t\314e\266\304W\345Oyn\275&*\246c\251M\204\354\023\271\203\317o\215\330K)\242Al."
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.3"
  }
  digests {
    sha256: "\033\257\322\354\342\350\215\264\315\3705\247\370\360\336e\372\265\261\024yw\245\334\305\233|\033\214oP\200"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.tiann"
    artifactId: "FreeReflection"
    version: "3.2.2"
  }
  digests {
    sha256: "\221 *\236\231\333\032\021~\3668b-\226\361\361\261\233\211CF\360:a\321\036|\202\315/\177\363"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.github.megatronking.stringfog"
    artifactId: "xor"
    version: "5.0.0"
  }
  digests {
    sha256: "\021\027.\3462\274\303\217\302\207\032W\322\324b\377M\237\272z\202\357A\266p\223i\373\222Nb\322"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.megatronking.stringfog"
    artifactId: "interface"
    version: "5.0.0"
  }
  digests {
    sha256: "\227]\026\vv\336\371\312\255\177ru\261^\202\271\365\017\244J\3129\346#\376\006!\273\251\207\377\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "app.cash.quickjs"
    artifactId: "quickjs-android"
    version: "0.9.2"
  }
  digests {
    sha256: "\262\n\n\335\310\335\t\033\"\361ls\000\000 @0sB1g\215+\210S\213\340\254W\225\020\342"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 1
  library_dep_index: 41
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 34
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 49
  library_dep_index: 14
  library_dep_index: 31
  library_dep_index: 38
  library_dep_index: 54
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 41
}
library_dependencies {
  library_index: 7
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 28
  library_dep_index: 3
  library_dep_index: 40
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 34
}
library_dependencies {
  library_index: 10
  library_dep_index: 3
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
  library_dep_index: 12
}
library_dependencies {
  library_index: 13
  library_dep_index: 1
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 24
  library_dep_index: 37
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
  library_dep_index: 15
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 4
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 22
  library_dep_index: 3
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 3
}
library_dependencies {
  library_index: 24
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 25
  library_dep_index: 24
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 27
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 1
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 30
  library_dep_index: 14
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 32
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 37
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 34
  library_dep_index: 24
  library_dep_index: 31
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 37
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 9
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 35
}
library_dependencies {
  library_index: 37
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 24
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 27
  library_dep_index: 12
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 8
}
library_dependencies {
  library_index: 40
  library_dep_index: 7
  library_dep_index: 34
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 7
}
library_dependencies {
  library_index: 41
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 6
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 8
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 13
  library_dep_index: 8
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 8
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 47
  library_dep_index: 47
}
library_dependencies {
  library_index: 49
  library_dep_index: 7
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 34
  library_dep_index: 24
  library_dep_index: 14
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 50
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 51
  library_dep_index: 3
  library_dep_index: 52
}
library_dependencies {
  library_index: 50
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 37
  library_dep_index: 31
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 46
}
library_dependencies {
  library_index: 52
  library_dep_index: 40
  library_dep_index: 53
  library_dep_index: 34
  library_dep_index: 49
  library_dep_index: 25
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 49
}
library_dependencies {
  library_index: 53
  library_dep_index: 3
  library_dep_index: 8
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
}
library_dependencies {
  library_index: 55
  library_dep_index: 1
  library_dep_index: 6
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 9
  library_dep_index: 45
  library_dep_index: 60
  library_dep_index: 10
  library_dep_index: 49
  library_dep_index: 14
  library_dep_index: 65
  library_dep_index: 66
  library_dep_index: 42
  library_dep_index: 67
}
library_dependencies {
  library_index: 56
  library_dep_index: 1
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 46
  library_dep_index: 8
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 59
}
library_dependencies {
  library_index: 60
  library_dep_index: 9
  library_dep_index: 8
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 62
  library_dep_index: 50
  library_dep_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
}
library_dependencies {
  library_index: 63
  library_dep_index: 1
}
library_dependencies {
  library_index: 64
  library_dep_index: 1
}
library_dependencies {
  library_index: 65
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 46
  library_dep_index: 8
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 8
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
  library_dep_index: 49
  library_dep_index: 65
  library_dep_index: 9
  library_dep_index: 8
}
library_dependencies {
  library_index: 68
  library_dep_index: 52
  library_dep_index: 69
  library_dep_index: 72
  library_dep_index: 3
  library_dep_index: 69
  library_dep_index: 71
  library_dep_index: 70
}
library_dependencies {
  library_index: 69
  library_dep_index: 40
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 70
  library_dep_index: 3
  library_dep_index: 70
  library_dep_index: 68
  library_dep_index: 71
}
library_dependencies {
  library_index: 70
  library_dep_index: 1
  library_dep_index: 53
  library_dep_index: 34
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 68
  library_dep_index: 69
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 10
  library_dep_index: 46
  library_dep_index: 45
  library_dep_index: 69
  library_dep_index: 66
  library_dep_index: 55
  library_dep_index: 68
  library_dep_index: 69
  library_dep_index: 70
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 46
  library_dep_index: 9
  library_dep_index: 73
  library_dep_index: 66
}
library_dependencies {
  library_index: 73
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 74
  library_dep_index: 75
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 79
  library_dep_index: 76
  library_dep_index: 74
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 77
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 52
  library_dep_index: 65
  library_dep_index: 72
  library_dep_index: 8
}
library_dependencies {
  library_index: 90
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 92
  library_dep_index: 93
}
library_dependencies {
  library_index: 94
  library_dep_index: 1
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 6
  dependency_index: 55
  dependency_index: 58
  dependency_index: 68
  dependency_index: 71
  dependency_index: 74
  dependency_index: 76
  dependency_index: 79
  dependency_index: 80
  dependency_index: 81
  dependency_index: 83
  dependency_index: 84
  dependency_index: 86
  dependency_index: 87
  dependency_index: 30
  dependency_index: 88
  dependency_index: 89
  dependency_index: 90
  dependency_index: 91
  dependency_index: 92
  dependency_index: 94
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
