<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.2.0" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="29"
            column="36"
            startOffset="1655"
            endLine="29"
            endColumn="76"
            endOffset="1695"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement="">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="3050"
                endOffset="3070"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="52"
            column="9"
            startOffset="3050"
            endLine="52"
            endColumn="29"
            endOffset="3070"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="96"
            column="39"
            startOffset="4840"
            endLine="96"
            endColumn="75"
            endOffset="4876"/>
        <map>
            <condition targetGE="24"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="110"
            column="39"
            startOffset="5600"
            endLine="110"
            endColumn="75"
            endOffset="5636"/>
        <map>
            <condition targetGE="24"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="120"
            column="39"
            startOffset="6060"
            endLine="120"
            endColumn="75"
            endOffset="6096"/>
        <map>
            <condition targetGE="24"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-anydpi/ic_action_home.xml"
            line="9"
            column="26"
            startOffset="274"
            endLine="9"
            endColumn="46"
            endOffset="294"/>
        <map>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
            <entry
                name="containsGradient"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;5dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="35"
            column="9"
            startOffset="1235"
            endLine="35"
            endColumn="34"
            endOffset="1260"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;5dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;5dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="56"
            column="9"
            startOffset="1963"
            endLine="56"
            endColumn="34"
            endOffset="1988"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="20"
            column="30"
            startOffset="800"
            endLine="20"
            endColumn="50"
            endOffset="820"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;0dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;0dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_main.xml"
            line="30"
            column="17"
            startOffset="1194"
            endLine="30"
            endColumn="42"
            endOffset="1219"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentEnd=&quot;true&quot;"
            oldString="layout_alignParentRight"
            replacement="layout_alignParentEnd"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/setting_row.xml"
            line="21"
            column="5"
            startOffset="674"
            endLine="21"
            endColumn="36"
            endOffset="705"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete layout_alignParentRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="layout_alignParentRight"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/setting_row.xml"
            line="33"
            column="9"
            startOffset="1123"
            endLine="33"
            endColumn="40"
            endOffset="1154"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;0dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;0dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="24"
            column="13"
            startOffset="973"
            endLine="24"
            endColumn="38"
            endOffset="998"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="32"
            column="13"
            startOffset="1302"
            endLine="32"
            endColumn="32"
            endOffset="1321"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;6dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;6dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="32"
            column="13"
            startOffset="1302"
            endLine="32"
            endColumn="32"
            endOffset="1321"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;48dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;48dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/sms_row.xml"
            line="65"
            column="13"
            startOffset="2605"
            endLine="65"
            endColumn="38"
            endOffset="2630"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement="">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
                startOffset="333"
                endOffset="352"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="6"
            column="45"
            startOffset="333"
            endLine="6"
            endColumn="64"
            endOffset="352"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement="">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
                startOffset="328"
                endOffset="347"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="6"
            column="45"
            startOffset="328"
            endLine="6"
            endColumn="64"
            endOffset="347"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/LoginActivity.java"
            line="100"
            column="56"
            startOffset="3591"
            endLine="100"
            endColumn="99"
            endOffset="3634"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/MainActivity.java"
            line="350"
            column="44"
            startOffset="13648"
            endLine="350"
            endColumn="87"
            endOffset="13691"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/JobSchedulerService.java"
                startOffset="283"
                endOffset="323"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/JobSchedulerService.java"
            line="16"
            column="1"
            startOffset="283"
            endLine="16"
            endColumn="41"
            endOffset="323"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/singlepixel/SinglePixelActivity.java"
            line="37"
            column="36"
            startOffset="1144"
            endLine="37"
            endColumn="40"
            endOffset="1148"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 33">
        <fix-replace
            description="Delete @RequiresApi"
            replacement="">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
                startOffset="9966"
                endOffset="10014"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/service/SocketService.java"
            line="270"
            column="9"
            startOffset="9966"
            endLine="270"
            endColumn="57"
            endOffset="10014"/>
        <map>
            <condition minGE="ffffffff00000000"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
            line="68"
            column="65"
            startOffset="2714"
            endLine="68"
            endColumn="108"
            endOffset="2757"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffe000000" requiresApi="ffffffff00000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
            line="241"
            column="62"
            startOffset="9561"
            endLine="241"
            endColumn="79"
            endOffset="9578"/>
        <map>
            <api-levels id="minSdk"
                value="fffffffffe000000"/>
            <api-levels id="requiresApi"
                value="ffffffff00000000"/>
            <entry
                name="message"
                string="Field requires API level 33 (current min is %1$s): `android.content.Context#RECEIVER_EXPORTED`"/>
        </map>
    </incident>

    <incident
        id="UnspecifiedRegisterReceiverFlag"
        severity="error"
        message="`receiver` \&#xA;is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected \&#xA;broadcasts registered for an IntentFilter that cannot be inspected by lint">
        <fix-alternatives>
            <fix-replace
                description="Add RECEIVER_NOT_EXPORTED (preferred)"
                oldString="_lint_insert_end_"
                replacement=", android.content.Context.RECEIVER_NOT_EXPORTED"
                shortenNames="true"
                reformat="value">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
                    startOffset="9659"
                    endOffset="9665"/>
            </fix-replace>
            <fix-replace
                description="Add RECEIVER_EXPORTED"
                oldString="_lint_insert_end_"
                replacement=", android.content.Context.RECEIVER_EXPORTED"
                shortenNames="true"
                reformat="value">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
                    startOffset="9659"
                    endOffset="9665"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/Sys.java"
            line="244"
            column="20"
            startOffset="9624"
            endLine="244"
            endColumn="62"
            endOffset="9666"/>
        <map>
            <entry
                name="hasUnprotected"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/adapters/PermissionsAdapter.java"
            line="51"
            column="59"
            startOffset="1895"
            endLine="51"
            endColumn="102"
            endOffset="1938"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/FloatingWindow.java"
            line="68"
            column="39"
            startOffset="2890"
            endLine="68"
            endColumn="44"
            endOffset="2895"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/ui/SettingsFragment.java"
            line="75"
            column="95"
            startOffset="3112"
            endLine="75"
            endColumn="138"
            endOffset="3155"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="fffffffff8000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="50"
            column="77"
            startOffset="1784"
            endLine="50"
            endColumn="115"
            endOffset="1822"/>
        <map>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <api-levels id="requiresApi"
                value="fffffffff8000000"/>
            <entry
                name="message"
                string="Field requires API level 28 (current min is %1$s): `android.Manifest.permission#FOREGROUND_SERVICE`"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="ffffffff00000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="51"
            column="72"
            startOffset="1904"
            endLine="51"
            endColumn="110"
            endOffset="1942"/>
        <map>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <api-levels id="requiresApi"
                value="ffffffff00000000"/>
            <entry
                name="message"
                string="Field requires API level 33 (current min is %1$s): `android.Manifest.permission#POST_NOTIFICATIONS`"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="fffffffffe000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="59"
            column="77"
            startOffset="2510"
            endLine="59"
            endColumn="115"
            endOffset="2548"/>
        <map>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <api-levels id="requiresApi"
                value="fffffffffe000000"/>
            <entry
                name="message"
                string="Field requires API level 26 (current min is %1$s): `android.Manifest.permission#READ_PHONE_NUMBERS`"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="66"
            column="89"
            startOffset="3286"
            endLine="66"
            endColumn="132"
            endOffset="3329"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="78"
            column="28"
            startOffset="4062"
            endLine="78"
            endColumn="71"
            endOffset="4105"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="199"
            column="53"
            startOffset="9525"
            endLine="199"
            endColumn="96"
            endOffset="9568"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bm/atool/utils/PermissionUtils.java"
            line="241"
            column="64"
            startOffset="11646"
            endLine="241"
            endColumn="107"
            endOffset="11689"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/xuexiang/keeplive/whitelist/impl/DefaultWhiteListProvider.java"
            line="56"
            column="48"
            startOffset="1994"
            endLine="56"
            endColumn="91"
            endOffset="2037"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/xuexiang/keeplive/whitelist/WhiteListIntentWrapper.java"
            line="74"
            column="37"
            startOffset="1764"
            endLine="74"
            endColumn="58"
            endOffset="1785"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="UnspecifiedRegisterReceiverFlag"
        severity="error"
        message="`pongReceiver` \&#xA;is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected \&#xA;broadcasts registered for ACTION_SOCKET_PONG">
        <fix-alternatives>
            <fix-replace
                description="Add RECEIVER_NOT_EXPORTED (preferred)"
                oldString="_lint_insert_end_"
                replacement=", android.content.Context.RECEIVER_NOT_EXPORTED"
                shortenNames="true"
                reformat="value">
                <range
                    file="${:app*debug*MAIN*testSourceProvider*1*javaDir*0}/com/bm/atool/StressTest.java"
                    startOffset="3509"
                    endOffset="3515"/>
            </fix-replace>
            <fix-replace
                description="Add RECEIVER_EXPORTED"
                oldString="_lint_insert_end_"
                replacement=", android.content.Context.RECEIVER_EXPORTED"
                shortenNames="true"
                reformat="value">
                <range
                    file="${:app*debug*MAIN*testSourceProvider*1*javaDir*0}/com/bm/atool/StressTest.java"
                    startOffset="3509"
                    endOffset="3515"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*testSourceProvider*1*javaDir*0}/com/bm/atool/StressTest.java"
            line="104"
            column="9"
            startOffset="3470"
            endLine="104"
            endColumn="55"
            endOffset="3516"/>
        <map>
            <entry
                name="hasUnprotected"
                boolean="true"/>
        </map>
    </incident>

</incidents>
