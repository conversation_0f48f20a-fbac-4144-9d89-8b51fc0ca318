package com.bm.atool.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bm.atool.utils.PhoneUtils;
import com.bm.atool.Sys;

public class SimChangedReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d("sms-change",intent.getStringExtra("ss"));
        Sys.onPhoneCHangeEvent(PhoneUtils.getPhones(context));
    }
}
