com.bm.atool.LoginActivity
com.google.android.material.datepicker.MaterialTextInputPicker
androidx.viewpager.widget.ViewPager
androidx.annotation.Keep
com.google.android.material.datepicker.MaterialCalendar
androidx.navigation.fragment.DialogFragmentNavigator
androidx.preference.SwitchPreferenceCompat
androidx.activity.ImmLeaksCleaner
com.google.android.material.internal.CheckableImageButton
com.google.android.material.timepicker.TimePickerView
androidx.preference.PreferenceScreen
me.weishu.reflection.Reflection
com.bm.atool.receivers.WakeUpReceiver
androidx.preference.PreferenceGroup
okhttp3.internal.publicsuffix.PublicSuffixDatabase
com.google.android.material.snackbar.BaseTransientBottomBar$Behavior
androidx.preference.PreferenceCategory
androidx.core.graphics.drawable.IconCompatParcelizer
kotlin.internal.jdk7.JDK7PlatformImplementations
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior
com.google.android.material.internal.NavigationMenuItemView
com.google.android.material.timepicker.ClockFaceView
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback$Stub
com.bm.atool.service.SocketService
androidx.activity.OnBackPressedDispatcher$LifecycleOnBackPressedCancellable
com.google.android.material.appbar.AppBarLayout$BaseBehavior
androidx.lifecycle.ProcessLifecycleInitializer
android.support.v4.os.IResultReceiver$Stub
android.support.v4.os.IResultReceiver2$Stub
com.google.android.material.transformation.FabTransformationSheetBehavior
androidx.lifecycle.DefaultLifecycleObserverAdapter
androidx.preference.EditTextPreference
androidx.appcompat.app.AlertController$RecycleListView
com.google.android.material.bottomappbar.BottomAppBar$Behavior
androidx.coordinatorlayout.widget.CoordinatorLayout
androidx.profileinstaller.ProfileInstallerInitializer
com.bm.atool.model.PingModel
androidx.lifecycle.LiveData$LifecycleBoundObserver
com.google.android.material.snackbar.SnackbarContentLayout
androidx.core.app.RemoteActionCompat
androidx.preference.internal.PreferenceImageView
androidx.lifecycle.CompositeGeneratedAdaptersObserver
com.bm.atool.model.JavaScriptResponse
androidx.appcompat.view.menu.ExpandedMenuView
androidx.preference.SwitchPreference
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.activity.ComponentActivity$3
androidx.appcompat.widget.FitWindowsFrameLayout
com.google.android.material.tabs.TabLayout
com.google.android.material.datepicker.MaterialCalendarGridView
com.bm.atool.service.singlepixel.SinglePixelActivity
com.google.android.material.snackbar.Snackbar$SnackbarLayout
androidx.lifecycle.SavedStateHandleController
androidx.lifecycle.SingleGeneratedAdapterObserver
androidx.appcompat.widget.SwitchCompat
com.google.android.material.transformation.FabTransformationScrimBehavior
androidx.savedstate.Recreator
androidx.core.graphics.drawable.IconCompat
com.google.android.material.textfield.TextInputEditText
androidx.appcompat.widget.Toolbar
androidx.constraintlayout.widget.Guideline
androidx.preference.UnPressableLinearLayout
androidx.appcompat.widget.ActionBarContextView
com.bm.atool.model.SmsSyncRequest$SMS
com.bm.atool.model.SmsAckRequest
androidx.viewpager2.adapter.FragmentStateAdapter$5
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior
com.google.android.material.button.MaterialButton
com.bm.atool.ui.BaseFragment
androidx.activity.ComponentActivity$5
androidx.appcompat.widget.AlertDialogLayout
androidx.transition.FragmentTransitionSupport
androidx.fragment.app.DialogFragment
com.bm.atool.ui.DebugFragment
androidx.versionedparcelable.CustomVersionedParcelable
com.bm.atool.model.LoginResponse
com.bm.atool.model.PongModel
androidx.core.widget.NestedScrollView
androidx.core.app.CoreComponentFactory
androidx.appcompat.view.menu.ActionMenuItemView
com.google.android.material.theme.MaterialComponentsViewInflater
com.bm.atool.service.NotificationService
androidx.preference.SeekBarPreference
com.bm.atool.App
com.google.gson.reflect.TypeToken
kotlin.coroutines.jvm.internal.BaseContinuationImpl
com.bm.atool.ui.MainFragment
androidx.recyclerview.widget.StaggeredGridLayoutManager
com.bm.atool.service.PlayMusicService
androidx.lifecycle.LegacySavedStateHandleController$tryToAddRecreator$1
androidx.lifecycle.LifecycleCoroutineScopeImpl
com.bm.atool.model.JavaScriptRequest
androidx.preference.DialogPreference
androidx.cardview.widget.CardView
com.bm.atool.ui.views.BlackUnderlineEditText
androidx.preference.TwoStatePreference
com.google.android.material.chip.Chip
com.bm.atool.receivers.WakeUpAutoStartReceiver
androidx.appcompat.widget.DialogTitle
com.bm.atool.model.SubscriptionInfoModel
androidx.appcompat.widget.FitWindowsLinearLayout
androidx.appcompat.widget.ContentFrameLayout
kotlin.coroutines.Continuation
androidx.lifecycle.WithLifecycleStateKt$suspendWithStateAtLeastUnchecked$2$observer$1
androidx.preference.MultiSelectListPreference
com.bm.atool.receivers.SimChangedReceiver
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService$Stub
androidx.preference.DropDownPreference
com.bm.atool.js.ScriptManager$1
com.bm.atool.model.SendSmsRequest
com.google.android.material.timepicker.ClockHandView
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior
com.bm.atool.service.ANTAccessibilityService
com.google.android.material.datepicker.MaterialDatePicker
androidx.navigation.fragment.DialogFragmentNavigator$observer$1
com.google.android.material.chip.ChipGroup
com.google.android.material.textview.MaterialTextView
com.bm.atool.model.SmsSyncRequest
androidx.fragment.app.Fragment$6
com.google.android.material.textfield.TextInputLayout
androidx.core.app.RemoteActionCompatParcelizer
androidx.constraintlayout.widget.ConstraintLayout
com.google.android.material.internal.BaselineLayout
com.google.android.material.button.MaterialButtonToggleGroup
com.google.android.material.timepicker.ChipTextInputComboView
retrofit2.Response
androidx.fragment.app.FragmentManager$6
app.cash.quickjs.QuickJs
androidx.recyclerview.widget.GridLayoutManager
com.bm.atool.model.SmsApi
androidx.preference.CheckBoxPreference
kotlinx.coroutines.internal.StackTraceRecoveryKt
com.google.android.material.transformation.ExpandableBehavior
androidx.profileinstaller.ProfileInstallReceiver
androidx.activity.result.ActivityResultRegistry$1
com.google.android.material.behavior.HideBottomViewOnScrollBehavior
com.google.android.material.behavior.SwipeDismissBehavior
androidx.appcompat.view.menu.ListMenuItemView
com.google.android.material.internal.NavigationMenuView
android.support.v4.app.INotificationSideChannel$Stub
androidx.appcompat.widget.ButtonBarLayout
androidx.preference.Preference
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior
androidx.emoji2.text.EmojiCompatInitializer
com.bm.atool.service.JobSchedulerService
androidx.preference.ListPreference
androidx.appcompat.widget.SearchView$SearchAutoComplete
kotlin.internal.jdk8.JDK8PlatformImplementations
androidx.viewpager2.adapter.FragmentStateAdapter$2
com.google.android.material.appbar.MaterialToolbar
com.google.android.material.transformation.FabTransformationBehavior
com.bm.atool.model.Response
com.google.android.material.transformation.ExpandableTransformationBehavior
androidx.viewpager2.adapter.FragmentStateAdapter$FragmentMaxLifecycleEnforcer$3
androidx.lifecycle.SavedStateHandleAttacher
androidx.constraintlayout.widget.Barrier
com.bm.atool.ui.SettingsFragment
app.cash.quickjs.QuickJsException
androidx.lifecycle.ReflectiveGenericLifecycleObserver
androidx.versionedparcelable.ParcelImpl
androidx.appcompat.widget.ViewStubCompat
com.bm.atool.service.WatchDogService
androidx.recyclerview.widget.LinearLayoutManager
com.google.android.material.appbar.AppBarLayout$Behavior
com.bm.atool.receivers.SmsReceiver
com.google.android.material.bottomsheet.BottomSheetBehavior
androidx.recyclerview.widget.RecyclerView
androidx.appcompat.widget.ActionMenuView
androidx.startup.InitializationProvider
androidx.appcompat.widget.ActionBarContainer
androidx.appcompat.widget.ActionBarOverlayLayout
com.bm.atool.model.LoginRequest
androidx.appcompat.widget.ActivityChooserView$InnerLayout
android.support.v4.app.RemoteActionCompatParcelizer
com.bm.atool.MainActivity
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.lifecycle.RepeatOnLifecycleKt$repeatOnLifecycle$3$1$1$1
androidx.emoji2.text.EmojiCompatInitializer$1
androidx.activity.ComponentActivity$4
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
com.bm.atool.model.JavaScriptResponse: boolean success
com.bm.atool.model.SubscriptionInfoModel: java.lang.String phoneNumber
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.SendSmsRequest: long id
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
androidx.fragment.app.BackStackState: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentManagerState: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.PingModel: java.lang.String brand
com.bm.atool.model.SendSmsRequest: java.lang.String subscriptionId
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
kotlinx.coroutines.CancellableContinuationImpl: int _decision
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
com.google.android.material.button.MaterialButton$SavedState: android.os.Parcelable$Creator CREATOR
androidx.transition.ChangeBounds$7: androidx.transition.ChangeBounds$ViewBounds mViewBounds
com.bm.atool.model.LoginRequest: java.lang.Integer tenantId
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.bm.atool.model.SubscriptionInfoModel: java.lang.String carrier
com.google.android.material.textfield.TextInputLayout$SavedState: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentManager$LaunchedFragmentInfo: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.JavaScriptRequest: java.lang.Object[] args
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
com.bm.atool.model.SendSmsRequest: java.lang.String from
kotlinx.coroutines.InvokeOnCancelling: int _invoked
com.bm.atool.model.SubscriptionInfoModel: int signalStrength
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
com.bm.atool.model.PongModel: java.lang.Integer ttl
com.bm.atool.model.SmsSyncRequest$SMS: java.lang.String id
com.bm.atool.model.PingModel: java.util.ArrayList phones
com.bm.atool.model.Response: boolean success
com.bm.atool.model.SmsAckRequest: long id
com.bm.atool.model.LoginRequest: java.lang.String password
com.bm.atool.model.SmsSyncRequest$SMS: java.lang.String to
androidx.viewpager.widget.ViewPager$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.material.internal.CheckableImageButton$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.material.badge.BadgeDrawable$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
com.bm.atool.model.LoginResponse: java.lang.String token
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
com.bm.atool.model.JavaScriptRequest: int scriptVersion
com.google.android.material.bottomsheet.BottomSheetBehavior$SavedState: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.JavaScriptResponse: java.lang.String error
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
com.bm.atool.model.JavaScriptResponse: long executionTime
com.bm.atool.model.SubscriptionInfoModel: java.lang.String slot
kotlinx.coroutines.JobSupport: java.lang.Object _state
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
com.bm.atool.model.SubscriptionInfoModel: java.lang.String iccId
com.bm.atool.model.PingModel: java.lang.String deviceId
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.PingModel: int power
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
com.google.android.material.appbar.AppBarLayout$BaseBehavior$SavedState: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.JavaScriptRequest: int timeout
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.SendSmsRequest: java.lang.String targetPhone
com.bm.atool.model.JavaScriptResponse: java.lang.String result
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.material.datepicker.CalendarConstraints: android.os.Parcelable$Creator CREATOR
app.cash.quickjs.QuickJsException: java.util.regex.Pattern STACK_TRACE_PATTERN
com.bm.atool.model.PingModel: java.lang.String model
com.bm.atool.model.JavaScriptRequest: java.lang.String scriptName
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
kotlinx.coroutines.CompletedExceptionally: int _handled
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.JavaScriptResponse: java.lang.String id
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.bm.atool.model.JavaScriptRequest: java.lang.String script
com.bm.atool.model.JavaScriptRequest: java.lang.String functionName
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
com.bm.atool.model.SendSmsRequest: java.lang.String content
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
androidx.coordinatorlayout.widget.CoordinatorLayout$SavedState: android.os.Parcelable$Creator CREATOR
app.cash.quickjs.QuickJsException: java.lang.String STACK_TRACE_CLASS_NAME
com.bm.atool.model.Response: java.lang.String message
com.bm.atool.model.SmsAckRequest: int code
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
com.bm.atool.model.SendSmsRequest: java.lang.String to
com.bm.atool.model.SubscriptionInfoModel: java.lang.String subscriptionId
com.bm.atool.model.JavaScriptRequest: java.lang.String id
androidx.activity.result.IntentSenderRequest: android.os.Parcelable$Creator CREATOR
androidx.fragment.app.FragmentState: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.Response: java.lang.Object data
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
com.google.android.material.datepicker.DateValidatorPointForward: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.AppCompatSpinner$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.CancelledContinuation: int _resumed
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.DefaultExecutor: int debugStatus
com.bm.atool.model.Response: java.lang.String code
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
com.bm.atool.model.SmsSyncRequest: java.util.List sms
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
com.bm.atool.model.SmsSyncRequest$SMS: java.lang.String content
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
com.bm.atool.model.JavaScriptResponse: long timestamp
com.google.android.material.datepicker.Month: android.os.Parcelable$Creator CREATOR
com.bm.atool.model.SubscriptionInfoModel: java.lang.String imei
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
com.bm.atool.model.SmsSyncRequest$SMS: java.lang.String from
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
com.bm.atool.model.LoginRequest: java.lang.String username
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
androidx.fragment.app.BackStackRecordState: android.os.Parcelable$Creator CREATOR
androidx.activity.result.ActivityResult: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
com.bm.atool.model.JavaScriptRequest: java.lang.String type
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
com.google.android.material.timepicker.TimeModel: android.os.Parcelable$Creator CREATOR
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
com.google.android.material.tabs.TabLayout: void setTabMode(int)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getErrorContentDescription()
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
com.google.android.material.textfield.TextInputLayout: void setErrorIconTintList(android.content.res.ColorStateList)
io.reactivex.internal.disposables.EmptyDisposable: io.reactivex.internal.disposables.EmptyDisposable valueOf(java.lang.String)
com.google.android.material.appbar.AppBarLayout$BaseBehavior: AppBarLayout$BaseBehavior()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.textfield.TextInputLayout: void setStartIconTintList(android.content.res.ColorStateList)
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.ViewCompat$Api31Impl: void setOnReceiveContentListener(android.view.View,java.lang.String[],androidx.core.view.OnReceiveContentListener)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
com.google.android.material.button.MaterialButton: int getInsetBottom()
app.cash.quickjs.QuickJs: void destroyContext(long)
com.google.android.material.chip.Chip: float getTextStartPadding()
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeWidthFocused()
androidx.appcompat.widget.AppCompatSpinner: void setDropDownHorizontalOffset(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getSuffixText()
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
androidx.activity.OnBackPressedDispatcher$Api33Impl: android.window.OnBackInvokedCallback createOnBackInvokedCallback(kotlin.jvm.functions.Function0)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundResource(int)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.cardview.widget.CardView: void setCardBackgroundColor(int)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
com.google.android.material.textfield.TextInputLayout: void setTypeface(android.graphics.Typeface)
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
com.google.android.material.chip.Chip: float getChipStrokeWidth()
androidx.appcompat.widget.AppCompatEditText: void setEmojiCompatEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: void setEndIconOnLongClickListener(android.view.View$OnLongClickListener)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: void setEndIconVisible(boolean)
com.google.android.material.chip.ChipGroup: int getChipCount()
com.google.android.material.tabs.TabLayout$TabView: com.google.android.material.badge.BadgeDrawable getOrCreateBadge()
com.google.android.material.chip.ChipGroup: int getCheckedChipId()
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
com.google.android.material.transformation.FabTransformationSheetBehavior: FabTransformationSheetBehavior()
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
com.google.android.material.tabs.TabLayout$TabView: int getContentWidth()
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: void setOnPressedChangeListenerInternal(com.google.android.material.button.MaterialButton$OnPressedChangeListener)
com.google.android.material.button.MaterialButtonToggleGroup: MaterialButtonToggleGroup(android.content.Context,android.util.AttributeSet)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberFormat: com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberFormat[] values()
com.google.android.material.chip.Chip: void setIconStartPadding(float)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatToggleButton: void setBackgroundResource(int)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.cardview.widget.CardView: android.content.res.ColorStateList getCardBackgroundColor()
com.google.android.material.textfield.TextInputLayout: void setStartIconOnClickListener(android.view.View$OnClickListener)
com.google.android.material.button.MaterialButton: void setBackgroundColor(int)
com.google.android.material.tabs.TabLayout: android.content.res.ColorStateList getTabTextColors()
com.google.gson.ReflectionAccessFilter$FilterResult: com.google.gson.ReflectionAccessFilter$FilterResult valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportButtonTintList()
androidx.appcompat.widget.AppCompatCheckedTextView: android.graphics.PorterDuff$Mode getSupportCheckMarkTintMode()
androidx.appcompat.widget.AppCompatButton: void setAllCaps(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCounterTextColor()
com.bm.atool.ui.DebugFragment: DebugFragment()
com.google.android.material.internal.NavigationMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
com.google.android.material.chip.Chip: float getCloseIconSize()
com.google.android.material.button.MaterialButton: void setStrokeWidthResource(int)
androidx.core.app.ActivityCompat$Api23Impl: void requestPermissions(android.app.Activity,java.lang.String[],int)
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.constraintlayout.widget.ConstraintAttribute$AttributeType: androidx.constraintlayout.widget.ConstraintAttribute$AttributeType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatEditText: void setKeyListener(android.text.method.KeyListener)
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeWidth()
com.google.i18n.phonenumbers.PhoneNumberUtil$ValidationResult: com.google.i18n.phonenumbers.PhoneNumberUtil$ValidationResult valueOf(java.lang.String)
com.google.android.material.checkbox.MaterialCheckBox: void setUseMaterialThemeColors(boolean)
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.appcompat.widget.AppCompatEditText: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.android.material.chip.Chip: void setChipIconSize(float)
androidx.appcompat.widget.ListPopupWindow$Api24Impl: int getMaxAvailableHeight(android.widget.PopupWindow,android.view.View,int,boolean)
com.google.android.material.button.MaterialButtonToggleGroup: void setSelectionRequired(boolean)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.android.material.textfield.TextInputLayout: void setBoxCollapsedPaddingTop(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.ListPopupWindow$Api29Impl: void setIsClippedToScreen(android.widget.PopupWindow,boolean)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusTopStart()
androidx.coordinatorlayout.widget.CoordinatorLayout: int getNestedScrollAxes()
com.google.android.material.chip.Chip: void setSingleLine(boolean)
com.google.android.material.tabs.TabLayout$TabView: void setSelected(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
com.google.android.material.datepicker.MaterialDatePicker: MaterialDatePicker()
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
com.google.android.material.appbar.MaterialToolbar: void setNavigationIconTint(int)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
com.google.android.material.chip.ChipGroup: ChipGroup(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.ChipGroup: void setDividerDrawableVertical(android.graphics.drawable.Drawable)
com.google.android.material.chip.Chip: void setLines(int)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.appcompat.widget.AppCompatTextView: void setAllCaps(boolean)
com.bm.atool.js.AndroidBridge: java.lang.String clearSms(java.lang.String)
androidx.appcompat.widget.AppCompatCheckedTextView: void setBackgroundResource(int)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
com.google.android.material.textfield.TextInputLayout: int getCounterMaxLength()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
com.google.android.material.tabs.TabLayout: int getTabMaxWidth()
com.google.android.material.textfield.TextInputLayout: void setPlaceholderText(java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextEnabled(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getPasswordVisibilityToggleDrawable()
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
androidx.core.os.BuildCompat$Api30Impl: int getExtensionVersion(int)
com.google.android.material.button.MaterialButton: int getCornerRadius()
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
com.google.android.material.textfield.TextInputLayout: void setEnabled(boolean)
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
com.google.android.material.transformation.ExpandableTransformationBehavior: ExpandableTransformationBehavior(android.content.Context,android.util.AttributeSet)
com.google.i18n.phonenumbers.PhoneNumberUtil$ValidationResult: com.google.i18n.phonenumbers.PhoneNumberUtil$ValidationResult[] values()
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
com.google.gson.internal.bind.TypeAdapters$21: TypeAdapters$21()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
com.google.android.material.chip.Chip: void setCheckedIconTint(android.content.res.ColorStateList)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
com.google.android.material.internal.BaselineLayout: BaselineLayout(android.content.Context,android.util.AttributeSet)
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback$Stub: androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback asInterface(android.os.IBinder)
com.google.android.material.appbar.AppBarLayout$Behavior: AppBarLayout$Behavior()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: AppCompatTextViewAutoSizeHelper$Impl23()
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatImageButton: void setImageLevel(int)
com.google.android.material.internal.ForegroundLinearLayout: void setForeground(android.graphics.drawable.Drawable)
com.google.android.material.internal.NavigationMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setSuffixTextColor(android.content.res.ColorStateList)
io.socket.engineio.client.Transport$ReadyState: io.socket.engineio.client.Transport$ReadyState[] values()
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.snackbar.SnackbarContentLayout: android.widget.Button getActionView()
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
io.reactivex.internal.disposables.DisposableHelper: io.reactivex.internal.disposables.DisposableHelper[] values()
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
com.google.android.material.tabs.TabLayout: void setSelectedTabView(int)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleContentDescription(java.lang.CharSequence)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
com.google.android.material.tabs.TabLayout$TabView: void setTab(com.google.android.material.tabs.TabLayout$Tab)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
androidx.viewpager.widget.ViewPager: int getClientWidth()
kotlin.random.Random: Random()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
com.google.android.material.textfield.TextInputLayout: void setHelperText(java.lang.CharSequence)
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.constraintlayout.widget.ConstraintLayout: java.lang.String getSceneString()
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundMode(int)
com.google.android.material.textfield.TextInputLayout: void setCounterMaxLength(int)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.core.view.ViewCompat$Api31Impl: java.lang.String[] getReceiveContentMimeTypes(android.view.View)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.cardview.widget.CardView: int getContentPaddingBottom()
com.google.android.material.tabs.TabLayout: void setSelectedTabIndicatorHeight(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setEmojiCompatEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: void setHint(java.lang.CharSequence)
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
com.google.android.material.chip.Chip: void setCheckable(boolean)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
com.google.android.material.chip.Chip: android.text.TextUtils$TruncateAt getEllipsize()
com.google.android.material.chip.Chip: void setChipMinHeightResource(int)
com.google.android.material.internal.NavigationMenuItemView: void setCheckable(boolean)
androidx.emoji2.text.EmojiCompatInitializer: EmojiCompatInitializer()
com.google.android.material.appbar.MaterialToolbar: MaterialToolbar(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: void setPressed(boolean)
com.google.android.material.internal.CheckableImageButton: CheckableImageButton(android.content.Context,android.util.AttributeSet)
androidx.core.text.ICUCompat$Api24Impl: android.icu.util.ULocale forLocale(java.util.Locale)
com.google.android.material.tabs.TabLayout: void setTabRippleColorResource(int)
androidx.appcompat.widget.AppCompatToggleButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setMaxWidth(int)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.google.android.material.textfield.TextInputLayout: void setStartIconCheckable(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
com.google.i18n.phonenumbers.Phonenumber$PhoneNumber$CountryCodeSource: com.google.i18n.phonenumbers.Phonenumber$PhoneNumber$CountryCodeSource[] values()
com.google.android.material.tabs.TabLayout: void setSelectedTabIndicatorColor(int)
com.google.android.material.internal.NavigationMenuItemView: void setChecked(boolean)
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag valueOf(java.lang.String)
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
com.google.android.material.tabs.TabLayout: android.content.res.ColorStateList getTabRippleColor()
app.cash.quickjs.QuickJs: void set(long,java.lang.String,java.lang.Object,java.lang.Object[])
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
com.bm.atool.model.SubscriptionInfoModel: SubscriptionInfoModel()
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.appcompat.widget.MenuPopupWindow$Api23Impl: void setEnterTransition(android.widget.PopupWindow,android.transition.Transition)
com.google.android.material.chip.Chip: float getIconEndPadding()
com.google.android.material.chip.Chip: void setIconStartPaddingResource(int)
androidx.appcompat.widget.AppCompatToggleButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.snackbar.BaseTransientBottomBar$Behavior: BaseTransientBottomBar$Behavior()
com.google.gson.Gson$FutureTypeAdapter: Gson$FutureTypeAdapter()
androidx.appcompat.widget.AppCompatCheckedTextView: void setSupportCheckMarkTintMode(android.graphics.PorterDuff$Mode)
com.bm.atool.model.SendSmsRequest: SendSmsRequest()
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
com.google.android.material.button.MaterialButton: void setInternalBackground(android.graphics.drawable.Drawable)
androidx.viewpager.widget.ViewPager: int getOffscreenPageLimit()
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State valueOf(java.lang.String)
androidx.constraintlayout.widget.Barrier: void setAllowsGoneWidget(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
com.google.android.material.internal.NavigationMenuItemView: void setActionView(android.view.View)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
com.google.android.material.transformation.FabTransformationSheetBehavior: FabTransformationSheetBehavior(android.content.Context,android.util.AttributeSet)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getDefaultHintTextColor()
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
com.google.android.material.textfield.TextInputLayout: void setEditText(android.widget.EditText)
com.google.android.material.button.MaterialButtonToggleGroup: int getLastVisibleChildIndex()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
com.google.android.material.chip.ChipGroup: void setOnCheckedChangeListener(com.google.android.material.chip.ChipGroup$OnCheckedChangeListener)
androidx.appcompat.app.AppCompatDelegateImpl$Api33Impl: android.window.OnBackInvokedDispatcher getOnBackInvokedDispatcher(android.app.Activity)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
com.google.android.material.textfield.TextInputLayout: int getErrorCurrentTextColors()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.google.android.material.textfield.TextInputLayout: void setErrorIconVisible(boolean)
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: void setAutoSizeTextTypeUniformWithPresetSizes(android.widget.TextView,int[],int)
com.bm.atool.model.PongModel: PongModel()
com.google.android.material.chip.Chip: void setChipText(java.lang.CharSequence)
com.google.android.material.internal.ForegroundLinearLayout: void setForegroundGravity(int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
com.google.android.material.textfield.TextInputLayout: float getHintCollapsedTextHeight()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.emoji2.text.EmojiExclusions$EmojiExclusions_Api34: java.util.Set getExclusions()
com.google.android.material.button.MaterialButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
com.google.gson.internal.sql.SqlDateTypeAdapter$1: SqlDateTypeAdapter$1()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.core.content.res.ResourcesCompat$ThemeCompat$Api29Impl: void rebase(android.content.res.Resources$Theme)
com.google.android.material.chip.Chip: void setCloseIconEnabled(boolean)
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
com.google.android.material.chip.ChipGroup: void setSingleSelection(int)
com.google.android.material.internal.FlowLayout: int getItemSpacing()
com.google.android.material.datepicker.MaterialCalendar$CalendarSelector: com.google.android.material.datepicker.MaterialCalendar$CalendarSelector[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getSuggestedMinimumWidth()
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
com.google.android.material.chip.Chip: void setCloseIconTintResource(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: float getBackgroundOverlayColorAlpha()
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
androidx.recyclerview.widget.RecyclerView: void suppressLayout(boolean)
androidx.core.app.NotificationManagerCompat$Api24Impl: int getImportance(android.app.NotificationManager)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
com.google.gson.Strictness: com.google.gson.Strictness valueOf(java.lang.String)
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.constraintlayout.widget.ConstraintHelper: void setReferenceTags(java.lang.String)
com.google.android.material.internal.NavigationMenuItemView: void setNeedsEmptyIcon(boolean)
com.google.gson.internal.bind.TypeAdapters$6: TypeAdapters$6()
com.google.android.material.button.MaterialButton: int getIconSize()
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
androidx.activity.OnBackPressedDispatcher$Api33Impl: void registerOnBackInvokedCallback(java.lang.Object,int,java.lang.Object)
com.google.android.material.textfield.TextInputLayout: int getBoxCollapsedPaddingTop()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
com.google.android.material.chip.Chip: void setCloseIconResource(int)
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode valueOf(java.lang.String)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.chip.ChipGroup: void setFlexWrap(int)
com.google.android.material.tabs.TabLayout: android.content.res.ColorStateList getTabIconTint()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
com.google.android.material.textfield.TextInputLayout: void setErrorContentDescription(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.appcompat.view.WindowCallbackWrapper$Api23Impl: boolean onSearchRequested(android.view.Window$Callback,android.view.SearchEvent)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.coordinatorlayout.widget.CoordinatorLayout: int getSuggestedMinimumHeight()
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: int getAnimationMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
com.google.android.material.chip.ChipGroup: void setCheckedId(int)
com.google.android.material.tabs.TabLayout: TabLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setChipEndPadding(float)
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setBackInvokedCallbackEnabled(boolean)
androidx.appcompat.widget.MenuPopupWindow$Api23Impl: void setExitTransition(android.widget.PopupWindow,android.transition.Transition)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
com.google.android.material.textfield.TextInputLayout: com.google.android.material.internal.CheckableImageButton getEndIconView()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
com.google.gson.internal.sql.SqlTimeTypeAdapter$1: SqlTimeTypeAdapter$1()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.ViewGroupCompat$Api21Impl: boolean isTransitionGroup(android.view.ViewGroup)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
com.google.android.material.chip.ChipGroup: void setSingleLine(int)
com.bm.atool.service.JobSchedulerService: JobSchedulerService()
com.google.android.material.button.MaterialButton: android.graphics.drawable.Drawable getIcon()
com.google.gson.internal.bind.TypeAdapters$7: TypeAdapters$7()
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.app.AppLocalesMetadataHolderService$Api24Impl: int getDisabledComponentFlag()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPasswordVisibilityToggleContentDescription()
com.google.android.material.chip.ChipGroup: void setChipSpacingVertical(int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.internal.ForegroundLinearLayout: int getForegroundGravity()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setAnimationMode(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: float getActionTextColorAlpha()
androidx.appcompat.app.AppCompatDelegateImpl$Api24Impl: void generateConfigDelta_locale(android.content.res.Configuration,android.content.res.Configuration,android.content.res.Configuration)
io.socket.engineio.client.Socket$ReadyState: io.socket.engineio.client.Socket$ReadyState[] values()
com.google.android.material.internal.NavigationMenuItemView: void setTitle(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPlaceholderText()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.constraintlayout.widget.ConstraintLayout: int getMaxHeight()
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.chip.ChipGroup: java.util.List getCheckedChipIds()
com.google.android.material.textfield.TextInputLayout: void setCounterTextColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setSuffixText(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
com.google.android.material.textfield.TextInputLayout: void setEndIconOnClickListener(android.view.View$OnClickListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
com.google.gson.stream.JsonToken: com.google.gson.stream.JsonToken valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatSpinner$Api23Impl: void setDropDownViewTheme(android.widget.ThemedSpinnerAdapter,android.content.res.Resources$Theme)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleDrawable(android.graphics.drawable.Drawable)
app.cash.quickjs.QuickJsException: QuickJsException(java.lang.String,java.lang.String)
com.bm.atool.js.AndroidBridge: java.lang.String getAppStatus()
androidx.core.view.ViewGroupCompat$Api21Impl: void setTransitionGroup(android.view.ViewGroup,boolean)
androidx.cardview.widget.CardView: CardView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setVisibility(int)
com.google.android.material.textfield.MaterialAutoCompleteTextView: void setAdapter(android.widget.ListAdapter)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getBoxStrokeErrorColor()
androidx.core.os.LocaleListCompat$Api21Impl: java.util.Locale forLanguageTag(java.lang.String)
com.google.android.material.tabs.TabLayout: void setInlineLabel(boolean)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.cardview.widget.CardView: void setUseCompatPadding(boolean)
androidx.appcompat.widget.AppCompatCheckedTextView: void setSupportCheckMarkTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
io.socket.engineio.client.Transport$ReadyState: io.socket.engineio.client.Transport$ReadyState valueOf(java.lang.String)
com.google.android.material.chip.Chip: void setCloseIconStartPadding(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
com.bm.atool.model.LoginRequest: LoginRequest()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.constraintlayout.widget.Barrier: boolean getAllowsGoneWidget()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
com.bm.atool.js.AndroidBridge: java.lang.String getDeviceInfo()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setOnClickListener(android.view.View$OnClickListener)
com.bm.atool.receivers.WakeUpReceiver: WakeUpReceiver()
androidx.appcompat.app.AppCompatDelegate$Api33Impl: void localeManagerSetApplicationLocales(java.lang.Object,android.os.LocaleList)
com.bm.atool.model.SmsApi: retrofit2.Call login(com.bm.atool.model.LoginRequest)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
com.google.android.material.button.MaterialButton: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.appcompat.widget.AppCompatCheckedTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.bm.atool.js.AndroidBridge: java.lang.String sendSms(java.lang.String,java.lang.String,java.lang.String)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.appcompat.resources.Compatibility$Api21Impl: android.graphics.drawable.Drawable createFromXmlInner(android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
com.bm.atool.model.JavaScriptRequest: JavaScriptRequest()
com.google.android.material.internal.NavigationMenuView: NavigationMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation: LegacySavedStateHandleController$OnRecreation()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
com.google.android.material.tabs.TabLayout: void setUnboundedRipple(boolean)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getAdjustedDefault()
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
com.bm.atool.receivers.WakeUpAutoStartReceiver: WakeUpAutoStartReceiver()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportButtonTintList()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
com.bm.atool.js.AndroidBridge: void sleep(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
com.google.android.material.chip.ChipGroup: void setChipSpacing(int)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
com.google.android.material.chip.Chip: void setHideMotionSpecResource(int)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.AppCompatButton: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: int getAutoSizeStepGranularity(android.widget.TextView)
com.google.android.material.textfield.TextInputLayout: void setStartIconContentDescription(java.lang.CharSequence)
androidx.constraintlayout.widget.ConstraintLayout: void setId(int)
com.google.android.material.textfield.TextInputLayout: void setErrorIconOnClickListener(android.view.View$OnClickListener)
com.google.android.material.chip.Chip: void setChipCornerRadiusResource(int)
androidx.constraintlayout.widget.ConstraintLayout: void setMinHeight(int)
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
com.google.gson.internal.bind.TypeAdapters$15: TypeAdapters$15()
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundResource(int)
com.bm.atool.js.AndroidBridge: java.lang.String getBatteryLevel()
androidx.activity.ComponentActivity$Api33Impl: android.window.OnBackInvokedDispatcher getOnBackInvokedDispatcher(android.app.Activity)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
com.google.android.material.button.MaterialButton: void setBackgroundResource(int)
com.google.android.material.chip.Chip: void setRippleColorResource(int)
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList getDefault()
com.google.android.material.tabs.TabLayout: void setTabGravity(int)
androidx.appcompat.widget.Toolbar$Api33Impl: void tryRegisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
com.google.android.material.textfield.TextInputLayout: void setEndIconDrawable(int)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.google.android.material.chip.Chip: float getChipMinHeight()
androidx.appcompat.widget.AppCompatCheckedTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipIconTint()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
com.google.gson.Strictness: com.google.gson.Strictness[] values()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.recyclerview.widget.RecyclerView: int getBaseline()
com.google.android.material.textfield.TextInputLayout: android.graphics.Typeface getTypeface()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.Toolbar: android.view.View getNavButtonView()
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
androidx.appcompat.widget.SwitchCompat: float getThumbPosition()
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.appcompat.widget.AppCompatRadioButton: void setFilters(android.text.InputFilter[])
com.google.android.material.internal.ForegroundLinearLayout: android.graphics.drawable.Drawable getForeground()
com.google.gson.FieldNamingPolicy: com.google.gson.FieldNamingPolicy[] values()
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.AppCompatSpinner: void setPrompt(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.constraintlayout.core.widgets.analyzer.WidgetRun$RunType: androidx.constraintlayout.core.widgets.analyzer.WidgetRun$RunType valueOf(java.lang.String)
com.google.android.material.internal.FlowLayout: int getRowCount()
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
androidx.appcompat.widget.AppCompatTextView: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.appcompat.widget.AppCompatEditText: java.lang.CharSequence getText()
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar$Api33Impl: android.window.OnBackInvokedCallback newOnBackInvokedCallback(java.lang.Runnable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.appcompat.widget.SwitchCompat: void setAllCaps(boolean)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
androidx.constraintlayout.widget.Guideline: void setVisibility(int)
com.google.android.material.button.MaterialButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatToggleButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownVerticalOffset(int)
com.bm.atool.ui.SettingsFragment: SettingsFragment()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.appcompat.widget.AppCompatEditText: void setBackgroundResource(int)
com.google.android.material.transformation.ExpandableBehavior: ExpandableBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatEditText: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
okhttp3.Protocol: okhttp3.Protocol[] values()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
com.google.android.material.appbar.AppBarLayout$Behavior: AppBarLayout$Behavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setHideMotionSpec(com.google.android.material.animation.MotionSpec)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.constraintlayout.core.widgets.ConstraintAnchor$Type: androidx.constraintlayout.core.widgets.ConstraintAnchor$Type valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCheckMarkDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.google.android.material.tabs.TabLayout: int getTabMinWidth()
com.google.android.material.chip.Chip: void setIconEndPadding(float)
com.google.android.material.button.MaterialButton: void setIconGravity(int)
androidx.constraintlayout.widget.ConstraintHelper: void setReferencedIds(int[])
com.google.android.material.button.MaterialButton: void setIconTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.chip.Chip: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.widget.AppCompatToggleButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.cardview.widget.CardView: void setMinimumWidth(int)
com.google.gson.internal.bind.TypeAdapters$17: TypeAdapters$17()
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
com.google.android.material.chip.Chip: void setCheckedIconEnabled(boolean)
com.google.gson.internal.bind.TypeAdapters$14: TypeAdapters$14()
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatSpinner: android.graphics.drawable.Drawable getPopupBackground()
androidx.constraintlayout.widget.Barrier: void setDpMargin(int)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
com.google.android.material.chip.Chip: void setChipCornerRadius(float)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColorStateList(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getIconTintMode()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
com.bm.atool.model.SmsSyncRequest$SMS: SmsSyncRequest$SMS()
com.google.android.material.chip.Chip: com.google.android.material.resources.TextAppearance getTextAppearance()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.constraintlayout.widget.Barrier: int getType()
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
com.google.android.material.snackbar.SnackbarContentLayout: SnackbarContentLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.tabs.TabLayout: void setTabIndicatorAnimationMode(int)
androidx.constraintlayout.core.widgets.analyzer.WidgetRun$RunType: androidx.constraintlayout.core.widgets.analyzer.WidgetRun$RunType[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
com.google.android.material.button.MaterialButton: int getIconPadding()
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleEnabled(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
androidx.appcompat.widget.AppCompatButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: android.widget.EditText getEditText()
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.constraintlayout.core.widgets.analyzer.DependencyNode$Type: androidx.constraintlayout.core.widgets.analyzer.DependencyNode$Type valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setImageLevel(int)
com.google.android.material.chip.Chip: void setCheckedIconVisible(boolean)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setCloseIconEnabledResource(int)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getRippleColor()
com.google.android.material.tabs.TabLayout$TabView: com.google.android.material.tabs.TabLayout$Tab getTab()
androidx.appcompat.widget.AppCompatCheckBox: void setAllCaps(boolean)
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: Snackbar$SnackbarLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.ChipGroup: void setShowDividerVertical(int)
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
com.google.android.material.chip.Chip: android.content.res.ColorStateList getCloseIconTint()
com.google.android.material.chip.Chip: void setCheckedIconEnabledResource(int)
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
androidx.core.graphics.PaintCompat$Api23Impl: boolean hasGlyph(android.graphics.Paint,java.lang.String)
androidx.constraintlayout.widget.ConstraintLayout: ConstraintLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.appcompat.app.AppCompatDelegate$Api24Impl: android.os.LocaleList localeListForLanguageTags(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setMaxWidthResource(int)
androidx.core.view.PointerIconCompat$Api24Impl: android.view.PointerIcon load(android.content.res.Resources,int)
androidx.cardview.widget.CardView: float getMaxCardElevation()
com.google.android.material.textfield.TextInputEditText: java.lang.CharSequence getHintFromLayout()
com.google.android.material.button.MaterialButtonToggleGroup: int getVisibleButtonCount()
androidx.appcompat.widget.AppCompatToggleButton: void setAllCaps(boolean)
com.google.android.material.textfield.TextInputLayout: void setHintInternal(java.lang.CharSequence)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.android.material.textfield.TextInputLayout: void setEndIconTintList(android.content.res.ColorStateList)
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
androidx.appcompat.widget.AppCompatTextHelper$Api24Impl: void setTextLocales(android.widget.TextView,android.os.LocaleList)
androidx.appcompat.widget.AppCompatToggleButton: void setEmojiCompatEnabled(boolean)
com.google.i18n.phonenumbers.NumberParseException$ErrorType: com.google.i18n.phonenumbers.NumberParseException$ErrorType[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
com.google.android.material.chip.Chip: void setChipStrokeWidth(float)
okhttp3.internal.http2.ErrorCode: okhttp3.internal.http2.ErrorCode[] values()
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
com.bm.atool.service.WatchDogService: WatchDogService()
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextAppearance(int)
androidx.coordinatorlayout.widget.CoordinatorLayout: java.util.List getDependencySortedChildren()
com.google.android.material.textfield.TextInputLayout: void setCounterOverflowTextAppearance(int)
com.google.android.material.textfield.TextInputLayout: int getBoxBackgroundMode()
com.google.gson.internal.bind.TypeAdapters$16: TypeAdapters$16()
androidx.appcompat.widget.DropDownListView$Api21Impl: void drawableHotspotChanged(android.view.View,float,float)
androidx.cardview.widget.CardView: void setMinimumHeight(int)
com.google.gson.internal.sql.SqlDateTypeAdapter: SqlDateTypeAdapter()
com.google.gson.internal.bind.TypeAdapters$4: TypeAdapters$4()
androidx.transition.FragmentTransitionSupport: FragmentTransitionSupport()
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
androidx.cardview.widget.CardView: void setPreventCornerOverlap(boolean)
com.google.android.material.tabs.TabLayout$TabView: int getContentHeight()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.core.widget.NestedScrollView: int getScrollRange()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.appcompat.widget.AppCompatEditText: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.textfield.TextInputLayout: void setEndIconMode(int)
com.google.android.material.textfield.TextInputLayout: int getBaseline()
com.google.android.material.button.MaterialButton: int getIconGravity()
com.google.gson.ToNumberPolicy: com.google.gson.ToNumberPolicy valueOf(java.lang.String)
com.google.gson.internal.bind.TypeAdapters$26: TypeAdapters$26()
androidx.coordinatorlayout.widget.CoordinatorLayout: void setOnHierarchyChangeListener(android.view.ViewGroup$OnHierarchyChangeListener)
com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberType: com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberType[] values()
androidx.core.text.ICUCompat$Api24Impl: android.icu.util.ULocale addLikelySubtags(java.lang.Object)
app.cash.quickjs.QuickJs: long createContext()
com.google.android.material.chip.Chip: void setOnCloseIconClickListener(android.view.View$OnClickListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundResource(int)
androidx.appcompat.widget.AppCompatButton: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.SpinnerAdapter)
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State[] values()
com.google.android.material.textfield.TextInputLayout: com.google.android.material.internal.CheckableImageButton getEndIconToUpdateDummyDrawable()
androidx.appcompat.widget.SwitchCompat: void setTextOffInternal(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.viewpager.widget.ViewPager: ViewPager(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.ConstraintLayout: void setMaxWidth(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getCounterOverflowDescription()
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api34Impl: void setLineHeight(android.widget.TextView,int,float)
com.google.gson.internal.bind.TypeAdapters$11: TypeAdapters$11()
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
com.google.android.material.chip.ChipGroup: int getChipSpacingVertical()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
com.google.android.material.button.MaterialButton: void setRippleColor(android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
com.google.android.material.button.MaterialButton: void setShouldDrawSurfaceColorStroke(boolean)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.bm.atool.model.SmsAckRequest: SmsAckRequest()
com.google.android.material.textfield.TextInputEditText: com.google.android.material.textfield.TextInputLayout getTextInputLayout()
com.google.android.material.chip.Chip: java.lang.CharSequence getChipText()
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMaxTextSize()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
com.google.android.material.chip.Chip: void setChipTextResource(int)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService$Stub: androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService asInterface(android.os.IBinder)
com.google.android.material.textfield.TextInputLayout: int getPlaceholderTextAppearance()
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewUtils$Api29Impl: void computeFitSystemWindows(android.view.View,android.graphics.Rect,android.graphics.Rect)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
com.google.android.material.behavior.HideBottomViewOnScrollBehavior: HideBottomViewOnScrollBehavior()
androidx.core.widget.CompoundButtonCompat$Api23Impl: android.graphics.drawable.Drawable getButtonDrawable(android.widget.CompoundButton)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
com.google.android.material.button.MaterialButton: void setCornerRadiusResource(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
androidx.appcompat.widget.AppCompatToggleButton: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
com.google.android.material.timepicker.ChipTextInputComboView: ChipTextInputComboView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
com.google.android.material.chip.Chip: void setGravity(int)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatToggleButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.bm.atool.ui.MainFragment: MainFragment()
androidx.constraintlayout.widget.ConstraintLayout: int getPaddingWidth()
androidx.constraintlayout.widget.ConstraintLayout: int getOptimizationLevel()
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
com.google.android.material.button.MaterialButtonToggleGroup: void setSingleSelection(int)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeColor(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
com.google.android.material.button.MaterialButton: void setStrokeColorResource(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
com.google.android.material.textfield.TextInputLayout: void setHintAnimationEnabled(boolean)
androidx.constraintlayout.widget.Barrier: Barrier(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
com.google.android.material.textfield.TextInputLayout: android.widget.TextView getSuffixTextView()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.recyclerview.widget.RecyclerView: void setLayoutTransition(android.animation.LayoutTransition)
okhttp3.Protocol: okhttp3.Protocol valueOf(java.lang.String)
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
com.google.android.material.transformation.FabTransformationBehavior: FabTransformationBehavior()
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.graphics.PorterDuff$Mode getButtonTintMode(android.widget.CompoundButton)
kotlin.internal.jdk7.JDK7PlatformImplementations: JDK7PlatformImplementations()
androidx.appcompat.resources.Compatibility$Api21Impl: int getChangingConfigurations(android.content.res.TypedArray)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
com.google.android.material.chip.ChipGroup: void setShowDividerHorizontal(int)
androidx.appcompat.view.WindowCallbackWrapper$Api26Impl: void onPointerCaptureChanged(android.view.Window$Callback,boolean)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.content.ContextCompat$Api23Impl: int getColor(android.content.Context,int)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
com.google.android.material.textfield.TextInputLayout: com.google.android.material.textfield.EndIconDelegate getEndIconDelegate()
com.google.gson.reflect.TypeToken: TypeToken()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
com.google.android.material.textfield.TextInputLayout: void setErrorIconOnLongClickListener(android.view.View$OnLongClickListener)
com.google.android.material.chip.Chip: void setChipBackgroundColor(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
okhttp3.TlsVersion: okhttp3.TlsVersion[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
com.bm.atool.js.AndroidBridge: void log(java.lang.String,java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
com.google.android.material.chip.Chip: void setShowMotionSpec(com.google.android.material.animation.MotionSpec)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getChipIcon()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
com.google.android.material.textfield.TextInputLayout: void setHelperTextTextAppearance(int)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
com.bm.atool.js.AndroidBridge: java.lang.String getCurrentTimestamp()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.chip.Chip: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.SearchView$Api29Impl: void refreshAutoCompleteResults(android.widget.AutoCompleteTextView)
com.google.gson.ReflectionAccessFilter$FilterResult: com.google.gson.ReflectionAccessFilter$FilterResult[] values()
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
com.google.gson.internal.bind.TypeAdapters$25: TypeAdapters$25()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatTextHelper$Api28Impl: android.graphics.Typeface create(android.graphics.Typeface,int,boolean)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
com.google.android.material.chip.Chip: void setChipBackgroundColorResource(int)
androidx.appcompat.widget.AppCompatSpinner: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior: AppBarLayout$ScrollingViewBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
com.google.android.material.tabs.TabLayout: int getTabGravity()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.constraintlayout.widget.Guideline: void setGuidelineBegin(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
com.google.android.material.chip.Chip: void setCloseIconHovered(boolean)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.button.MaterialButtonToggleGroup: java.lang.CharSequence getAccessibilityClassName()
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getHint()
androidx.constraintlayout.core.SolverVariable$Type: androidx.constraintlayout.core.SolverVariable$Type valueOf(java.lang.String)
com.google.android.material.appbar.MaterialToolbar: void setSubtitleCentered(boolean)
androidx.appcompat.app.AppCompatDelegateImpl$Api24Impl: androidx.core.os.LocaleListCompat getLocales(android.content.res.Configuration)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
com.google.android.material.internal.CheckableImageButton: void setChecked(boolean)
androidx.appcompat.widget.AppCompatButton: void setAutoSizeTextTypeWithDefaults(int)
androidx.cardview.widget.CardView: int getContentPaddingLeft()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.appcompat.widget.AppCompatEditText: androidx.appcompat.widget.AppCompatEditText$SuperCaller getSuperCaller()
com.google.android.material.internal.NavigationMenuView: int getWindowAnimations()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
com.google.gson.internal.bind.SerializationDelegatingTypeAdapter: SerializationDelegatingTypeAdapter()
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.viewpager.widget.ViewPager: void setCurrentItem(int)
com.google.android.material.chip.Chip: void setOnCheckedChangeListenerInternal(android.widget.CompoundButton$OnCheckedChangeListener)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.bm.atool.receivers.SmsReceiver: SmsReceiver()
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.viewpager.widget.ViewPager: int getCurrentItem()
android.support.v4.app.INotificationSideChannel$Stub: android.support.v4.app.INotificationSideChannel asInterface(android.os.IBinder)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getCheckedIcon()
com.google.android.material.chip.Chip: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
com.google.android.material.chip.Chip: com.google.android.material.animation.MotionSpec getShowMotionSpec()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
com.google.android.material.internal.NavigationMenuItemView: void setMaxLines(int)
androidx.coordinatorlayout.widget.CoordinatorLayout: android.graphics.drawable.Drawable getStatusBarBackground()
androidx.cardview.widget.CardView: float getCardElevation()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
com.google.android.material.transformation.FabTransformationScrimBehavior: FabTransformationScrimBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
com.google.android.material.chip.Chip: com.google.android.material.animation.MotionSpec getHideMotionSpec()
com.google.gson.stream.JsonToken: com.google.gson.stream.JsonToken[] values()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getPlaceholderTextColor()
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
com.google.gson.internal.bind.TypeAdapters$24: TypeAdapters$24()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
androidx.appcompat.widget.SwitchCompat: void setTextOnInternal(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
androidx.cardview.widget.CardView: void setCardBackgroundColor(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getBackgroundTintList()
com.google.android.material.chip.Chip: void setCloseIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
com.google.android.material.chip.Chip: void setMinLines(int)
com.google.android.material.internal.FlowLayout: int getLineSpacing()
com.bm.atool.model.SmsApi: retrofit2.Call syncNotifications(com.bm.atool.model.NotificationSyncRequest)
androidx.fragment.app.FragmentContainerView: void setDrawDisappearingViewsLast(boolean)
com.google.android.material.button.MaterialButtonToggleGroup: int getCheckedButtonId()
com.google.android.material.chip.Chip: void setTextStartPaddingResource(int)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeErrorColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputEditText: TextInputEditText(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
com.google.android.material.chip.Chip: void setBackgroundColor(int)
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeTextType()
com.google.android.material.timepicker.ClockHandView: ClockHandView(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: void setCheckedIconResource(int)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getCheckedIconTint()
androidx.appcompat.app.AppCompatActivity: void setContentView(android.view.View)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
com.google.gson.internal.bind.TypeAdapters$22: TypeAdapters$22()
androidx.appcompat.widget.AppCompatToggleButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getStartIconDrawable()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
com.google.android.material.button.MaterialButton: void setRippleColorResource(int)
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
com.google.android.material.transformation.FabTransformationScrimBehavior: FabTransformationScrimBehavior()
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.constraintlayout.widget.ConstraintLayout: androidx.constraintlayout.widget.SharedValues getSharedValues()
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
com.google.android.material.textfield.TextInputLayout: void setCounterEnabled(boolean)
com.google.gson.internal.bind.TypeAdapters$2: TypeAdapters$2()
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact[] values()
com.google.android.material.tabs.TabLayout: void setSelectedTabIndicator(int)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: int getEndIconMode()
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
com.google.android.material.chip.Chip: void setChipIconVisible(boolean)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleContentDescription(int)
com.google.android.material.textfield.TextInputLayout: void setHintTextAppearance(int)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.constraintlayout.widget.Barrier: int getMargin()
androidx.appcompat.widget.AppCompatTextClassifierHelper$Api26Impl: android.view.textclassifier.TextClassifier getTextClassifier(android.widget.TextView)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.cardview.widget.CardView: void setMaxCardElevation(float)
com.google.android.material.chip.Chip: void setCloseIconSizeResource(int)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeStepGranularity()
androidx.appcompat.widget.Toolbar: java.util.ArrayList getCurrentMenuItems()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatCheckBox: void setEmojiCompatEnabled(boolean)
app.cash.quickjs.QuickJsException: void addJavaScriptStack(java.lang.Throwable,java.lang.String)
com.google.android.material.chip.Chip: void setTextAppearance(int)
com.google.android.material.chip.Chip: void setTextEndPaddingResource(int)
androidx.appcompat.widget.DropDownListView$Api33Impl: boolean isSelectedChildViewEnabled(android.widget.AbsListView)
com.google.android.material.chip.Chip: void setCloseIconStartPaddingResource(int)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.appcompat.resources.Compatibility$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.AppCompatTextView: void setFilters(android.text.InputFilter[])
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintMode(android.widget.CompoundButton,android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior: FloatingActionButton$BaseBehavior(android.content.Context,android.util.AttributeSet)
androidx.constraintlayout.widget.Barrier: void setType(int)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
okhttp3.internal.publicsuffix.PublicSuffixDatabase: PublicSuffixDatabase()
com.google.android.material.tabs.TabLayout: void setupWithViewPager(androidx.viewpager.widget.ViewPager)
androidx.appcompat.widget.AppCompatCheckedTextView: void setAllCaps(boolean)
androidx.core.app.NotificationManagerCompat$Api24Impl: boolean areNotificationsEnabled(android.app.NotificationManager)
com.bm.atool.service.PlayMusicService: PlayMusicService()
com.google.android.material.chip.Chip: android.graphics.RectF getCloseIconTouchBounds()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setOnLayoutChangeListener(com.google.android.material.snackbar.BaseTransientBottomBar$OnLayoutChangeListener)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthFocusedResource(int)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
com.google.android.material.textfield.TextInputLayout: int getMaxWidth()
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
com.google.android.material.tabs.TabLayout: void setOnTabSelectedListener(com.google.android.material.tabs.TabLayout$OnTabSelectedListener)
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.constraintlayout.core.SolverVariable$Type: androidx.constraintlayout.core.SolverVariable$Type[] values()
androidx.viewpager.widget.ViewPager: void setOffscreenPageLimit(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
com.google.android.material.chip.Chip: void setChipMinHeight(float)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
androidx.appcompat.widget.AppCompatCheckedTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
com.google.android.material.textfield.TextInputLayout: void setMinWidth(int)
com.google.android.material.chip.Chip: void setCheckedIconVisible(int)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setErrorIconDrawable(int)
androidx.viewpager.widget.ViewPager: void setAdapter(androidx.viewpager.widget.PagerAdapter)
com.google.android.material.chip.Chip: void setChipDrawable(com.google.android.material.chip.ChipDrawable)
com.google.android.material.chip.Chip: void setChecked(boolean)
com.google.android.material.chip.Chip: float getTextEndPadding()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: int getMaxInlineActionWidth()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.constraintlayout.widget.ConstraintLayout: void setMaxHeight(int)
com.google.android.material.textfield.TextInputLayout: void setHintTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.ListPopupWindow$Api29Impl: void setEpicenterBounds(android.widget.PopupWindow,android.graphics.Rect)
com.google.gson.internal.bind.TypeAdapters$28: TypeAdapters$28()
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setKeyListener(android.text.method.KeyListener)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getPrefixTextColor()
androidx.appcompat.widget.AppCompatCheckedTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.AppCompatReceiveContentHelper$OnDropApi24Impl: boolean onDropForTextView(android.view.DragEvent,android.widget.TextView,android.app.Activity)
com.google.android.material.chip.Chip: void setCloseIconSize(float)
com.google.gson.internal.bind.TypeAdapters$9: TypeAdapters$9()
androidx.core.os.LocaleListCompat$Api24Impl: android.os.LocaleList createLocaleList(java.util.Locale[])
androidx.appcompat.widget.AppCompatEditText: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api24Impl: void updateDragShadow(android.view.View,android.view.View$DragShadowBuilder)
com.google.gson.FieldNamingPolicy: com.google.gson.FieldNamingPolicy valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
com.google.android.material.tabs.TabLayout: int getSelectedTabPosition()
com.google.android.material.transformation.FabTransformationBehavior: FabTransformationBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.transformation.ExpandableBehavior: ExpandableBehavior()
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
com.google.gson.ToNumberPolicy: com.google.gson.ToNumberPolicy[] values()
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButtonToggleGroup: java.util.List getCheckedButtonIds()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.appcompat.app.AppCompatDelegateImpl$Api33Impl: void unregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setKeyListener(android.text.method.KeyListener)
com.google.android.material.chip.Chip: float getChipIconSize()
com.bm.atool.MainActivity: MainActivity()
com.google.android.material.chip.Chip: Chip(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.Adapter)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
com.google.gson.internal.Excluder: Excluder()
com.google.android.material.textfield.TextInputLayout: void setEndIconContentDescription(int)
androidx.appcompat.widget.AppCompatTextView: void setEmojiCompatEnabled(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
com.bm.atool.receivers.SimChangedReceiver: SimChangedReceiver()
androidx.loader.app.LoaderManagerImpl$LoaderViewModel: LoaderManagerImpl$LoaderViewModel()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Api23Impl: android.text.StaticLayout createStaticLayoutForMeasuring(java.lang.CharSequence,android.text.Layout$Alignment,int,int,android.widget.TextView,android.text.TextPaint,androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl)
androidx.appcompat.app.AppCompatDelegateImpl$Api24Impl: void setLocales(android.content.res.Configuration,androidx.core.os.LocaleListCompat)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
com.google.gson.internal.bind.TypeAdapters$5: TypeAdapters$5()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
com.google.android.material.textfield.TextInputLayout: void setEndIconTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.textfield.TextInputLayout: void setStartIconOnLongClickListener(android.view.View$OnLongClickListener)
com.google.android.material.chip.Chip: void setCloseIconVisible(boolean)
androidx.core.view.ViewCompat$Api24Impl: void dispatchFinishTemporaryDetach(android.view.View)
com.google.android.material.chip.Chip: void setTextEndPadding(float)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getCounterOverflowTextColor()
com.google.android.material.textfield.TextInputLayout: int getHelperTextCurrentTextColor()
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
androidx.core.view.ViewGroupCompat$Api21Impl: int getNestedScrollAxes(android.view.ViewGroup)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
com.google.android.material.textfield.TextInputEditText: void setTextInputLayoutFocusedRectEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: void setEndIconContentDescription(java.lang.CharSequence)
com.google.android.material.internal.FlowLayout: void setSingleLine(boolean)
com.google.android.material.textfield.TextInputLayout: void setPlaceholderTextColor(android.content.res.ColorStateList)
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
com.google.android.material.internal.BaselineLayout: int getBaseline()
com.google.android.material.chip.ChipGroup: void setDividerDrawableHorizontal(android.graphics.drawable.Drawable)
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
androidx.appcompat.app.AppCompatDelegateImpl$Api21Impl: boolean isPowerSaveMode(android.os.PowerManager)
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.appcompat.app.AppCompatDelegateImpl$Api24Impl: void setDefaultLocales(androidx.core.os.LocaleListCompat)
androidx.appcompat.widget.AppCompatButton: int[] getAutoSizeTextAvailableSizes()
com.google.android.material.tabs.TabLayout: void setTabIconTintResource(int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.coordinatorlayout.widget.CoordinatorLayout: CoordinatorLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: android.graphics.PorterDuff$Mode getBackgroundTintMode()
androidx.constraintlayout.widget.ConstraintLayout: int getMinWidth()
androidx.constraintlayout.core.widgets.ConstraintAnchor$Type: androidx.constraintlayout.core.widgets.ConstraintAnchor$Type[] values()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
com.google.android.material.textfield.TextInputLayout: void setPrefixTextColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setMinWidthResource(int)
com.google.android.material.chip.Chip: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
com.google.android.material.chip.Chip: void setCheckedIconTintResource(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
com.google.android.material.textfield.TextInputLayout: void setEndIconActivated(boolean)
androidx.appcompat.widget.AppCompatEditText: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.lifecycle.ReportFragment: ReportFragment()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
com.google.android.material.chip.Chip: void setChipIconTint(android.content.res.ColorStateList)
androidx.core.content.ContextCompat$Api23Impl: java.lang.String getSystemServiceName(android.content.Context,java.lang.Class)
com.google.android.material.button.MaterialButton: com.google.android.material.shape.ShapeAppearanceModel getShapeAppearanceModel()
com.google.android.material.behavior.HideBottomViewOnScrollBehavior: HideBottomViewOnScrollBehavior(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatTextHelper$Api24Impl: android.os.LocaleList forLanguageTags(java.lang.String)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
okhttp3.TlsVersion: okhttp3.TlsVersion valueOf(java.lang.String)
com.google.android.material.button.MaterialButton: int getStrokeWidth()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: void setAutoSizeTextTypeUniformWithConfiguration(android.widget.TextView,int,int,int,int)
com.google.android.material.textfield.TextInputEditText: java.lang.CharSequence getHint()
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: AppCompatTextViewAutoSizeHelper$Impl()
com.google.android.material.internal.NavigationMenuItemView: void setIconPadding(int)
androidx.core.content.ContextCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.appcompat.widget.AppCompatToggleButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setBackground(android.graphics.drawable.Drawable)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
com.google.gson.internal.bind.JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory: JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory()
com.google.android.material.textfield.TextInputLayout: void setCounterTextAppearance(int)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatCheckedTextView: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.activity.OnBackPressedDispatcher$Api33Impl: void unregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.appcompat.widget.TooltipCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.SearchView$Api29Impl: void setInputMethodMode(androidx.appcompat.widget.SearchView$SearchAutoComplete,int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior: AppBarLayout$ScrollingViewBehavior()
com.google.android.material.internal.NavigationMenuItemView: void setIconTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
com.google.i18n.phonenumbers.NumberParseException$ErrorType: com.google.i18n.phonenumbers.NumberParseException$ErrorType valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
com.bm.atool.model.LoginResponse: LoginResponse()
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
com.google.android.material.chip.Chip: void setChipStartPaddingResource(int)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.gson.internal.bind.TypeAdapters$3: TypeAdapters$3()
androidx.activity.ComponentActivity: void setContentView(android.view.View)
com.google.android.material.internal.NavigationMenuItemView: void setTextAppearance(int)
androidx.core.view.ViewCompat$Api31Impl: androidx.core.view.ContentInfoCompat performReceiveContent(android.view.View,androidx.core.view.ContentInfoCompat)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
com.google.gson.internal.bind.TypeAdapters$23: TypeAdapters$23()
kotlin.internal.jdk8.JDK8PlatformImplementations: JDK8PlatformImplementations()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
com.google.android.material.datepicker.MaterialCalendarGridView: MaterialCalendarGridView(android.content.Context,android.util.AttributeSet)
com.google.android.material.timepicker.TimePickerView: TimePickerView(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButtonToggleGroup: void setupButtonChild(com.google.android.material.button.MaterialButton)
androidx.core.app.ActivityCompat$Api23Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setErrorIconTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatCheckedTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
androidx.constraintlayout.core.widgets.ConstraintWidget$DimensionBehaviour: androidx.constraintlayout.core.widgets.ConstraintWidget$DimensionBehaviour valueOf(java.lang.String)
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.google.android.material.appbar.MaterialToolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
androidx.viewpager.widget.ViewPager: void setOnPageChangeListener(androidx.viewpager.widget.ViewPager$OnPageChangeListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
com.google.android.material.chip.Chip: float getCloseIconEndPadding()
com.google.android.material.chip.Chip: void setTextAppearanceResource(int)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.appcompat.widget.Toolbar$Api33Impl: void tryUnregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getIconTint()
androidx.appcompat.widget.AppCompatCheckBox: void setFilters(android.text.InputFilter[])
com.google.android.material.chip.Chip: void setChipIconVisible(int)
com.google.android.material.internal.FlowLayout: void setLineSpacing(int)
androidx.appcompat.widget.AppCompatTextHelper$Api21Impl: java.util.Locale forLanguageTag(java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.DropDownListView$Api33Impl: void setSelectedChildViewEnabled(android.widget.AbsListView,boolean)
com.google.gson.internal.bind.TypeAdapters$27: TypeAdapters$27()
androidx.cardview.widget.CardView: void setRadius(float)
com.google.android.material.chip.ChipGroup: void setSingleSelection(boolean)
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
androidx.constraintlayout.widget.ConstraintLayout: void setOptimizationLevel(int)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.button.MaterialButton: void setIconResource(int)
com.google.android.material.internal.CheckableImageButton: void setCheckable(boolean)
com.google.android.material.textfield.TextInputLayout: void setDefaultHintTextColor(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setStartIconTintMode(android.graphics.PorterDuff$Mode)
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatButton: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatSpinner: int getDropDownWidth()
com.google.gson.internal.bind.DefaultDateTypeAdapter$1: DefaultDateTypeAdapter$1()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipStrokeColor()
androidx.appcompat.widget.AppCompatEditText: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api24Impl: void dispatchStartTemporaryDetach(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.AppCompatButton: void setSupportAllCaps(boolean)
app.cash.quickjs.QuickJs: java.lang.Object evaluate(long,java.lang.String,java.lang.String)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
com.google.android.material.textfield.TextInputLayout: void setStartIconDrawable(int)
com.google.android.material.chip.Chip: void setBackgroundResource(int)
androidx.appcompat.widget.SwitchCompat: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
com.google.android.material.chip.Chip: void setElevation(float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
com.google.android.material.tabs.TabLayout: int getTabIndicatorAnimationMode()
androidx.core.view.ViewCompat$Api24Impl: boolean startDragAndDrop(android.view.View,android.content.ClipData,android.view.View$DragShadowBuilder,java.lang.Object,int)
com.google.android.material.internal.NavigationMenuItemView: NavigationMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
com.google.android.material.chip.Chip: void setTextStartPadding(float)
com.google.android.material.appbar.MaterialToolbar: void setTitleCentered(boolean)
com.google.android.material.tabs.TabLayout: int getTabCount()
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getErrorIconDrawable()
androidx.constraintlayout.core.widgets.ConstraintWidget$DimensionBehaviour: androidx.constraintlayout.core.widgets.ConstraintWidget$DimensionBehaviour[] values()
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
com.google.gson.internal.sql.SqlTimestampTypeAdapter$1: SqlTimestampTypeAdapter$1()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
com.google.gson.internal.bind.TypeAdapters$18: TypeAdapters$18()
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior: ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getBackgroundDrawable()
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setEmojiCompatEnabled(boolean)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
com.google.gson.internal.bind.TypeAdapters$29: TypeAdapters$29()
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
androidx.appcompat.app.AppCompatDelegateImpl$Api21Impl: java.lang.String toLanguageTag(java.util.Locale)
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: boolean isHorizontallyScrollable(android.widget.TextView)
com.google.android.material.textfield.TextInputLayout: TextInputLayout(android.content.Context,android.util.AttributeSet)
androidx.emoji2.text.ConcurrencyHelpers$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
com.google.android.material.textfield.TextInputLayout: void setStartIconContentDescription(int)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.appcompat.widget.AppCompatEditText: android.text.Editable getText()
com.google.android.material.button.MaterialButton: int getTextWidth()
androidx.constraintlayout.widget.Guideline: Guideline(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: void setIconSize(int)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
io.socket.client.Manager$ReadyState: io.socket.client.Manager$ReadyState[] values()
com.bm.atool.model.SmsApi: retrofit2.Call sync(com.bm.atool.model.SmsSyncRequest)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMinTextSize()
com.google.android.material.datepicker.MaterialTextInputPicker: MaterialTextInputPicker()
com.google.android.material.chip.Chip: float getCloseIconStartPadding()
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
com.google.gson.LongSerializationPolicy: com.google.gson.LongSerializationPolicy valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
com.google.android.material.datepicker.MaterialCalendar: MaterialCalendar()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
com.google.gson.Gson$3: Gson$3()
com.bm.atool.model.JavaScriptResponse: JavaScriptResponse()
com.google.android.material.appbar.AppBarLayout$BaseBehavior: AppBarLayout$BaseBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleDrawable(int)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
com.google.android.material.datepicker.MaterialCalendar$CalendarSelector: com.google.android.material.datepicker.MaterialCalendar$CalendarSelector valueOf(java.lang.String)
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setOnClickListener(android.view.View$OnClickListener)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
com.google.android.material.textfield.TextInputLayout: void setErrorIconDrawable(android.graphics.drawable.Drawable)
com.google.android.material.chip.ChipGroup: void setSingleLine(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
com.google.android.material.button.MaterialButtonToggleGroup: int getFirstVisibleChildIndex()
com.google.android.material.textfield.TextInputLayout: void setHint(int)
com.google.android.material.tabs.TabLayout$TabView: com.google.android.material.badge.BadgeDrawable getBadge()
com.google.android.material.button.MaterialButton: void setIconPadding(int)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidth(int)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getPrefixText()
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: void setError(java.lang.CharSequence)
com.google.i18n.phonenumbers.Phonenumber$PhoneNumber$CountryCodeSource: com.google.i18n.phonenumbers.Phonenumber$PhoneNumber$CountryCodeSource valueOf(java.lang.String)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: android.content.res.ColorStateList getStrokeColor()
androidx.appcompat.widget.AppCompatRadioButton: void setEmojiCompatEnabled(boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
com.google.android.material.button.MaterialButton: void setStrokeWidth(int)
com.google.android.material.textfield.TextInputLayout: int getBoxBackgroundColor()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
com.google.android.material.textfield.TextInputLayout: void setCounterOverflowTextColor(android.content.res.ColorStateList)
app.cash.quickjs.QuickJsException: QuickJsException(java.lang.String)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthFocused(int)
androidx.appcompat.widget.AppCompatCheckedTextView: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.chip.ChipGroup: void setSelectionRequired(boolean)
com.google.android.material.textfield.TextInputLayout: void setErrorTextColor(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setChipIconSizeResource(int)
androidx.appcompat.widget.AppCompatToggleButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
com.google.android.material.radiobutton.MaterialRadioButton: android.content.res.ColorStateList getMaterialThemeColorsTintList()
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
com.google.android.material.floatingactionbutton.FloatingActionButton$BaseBehavior: FloatingActionButton$BaseBehavior()
androidx.appcompat.widget.AppCompatToggleButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.ChipGroup: void setChipSpacingHorizontalResource(int)
com.google.android.material.snackbar.SnackbarContentLayout: android.widget.TextView getMessageView()
androidx.viewpager.widget.ViewPager: int getPageMargin()
com.google.android.material.chip.Chip: void setEnsureMinTouchTargetSize(boolean)
androidx.cardview.widget.CardView: boolean getPreventCornerOverlap()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButtonToggleGroup: void setCheckedId(int)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.AppCompatCheckedTextView: void setEmojiCompatEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getError()
com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberType: com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberType valueOf(java.lang.String)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
com.google.android.material.chip.Chip: float getChipCornerRadius()
com.google.android.material.tabs.TabLayout: void setInlineLabelResource(int)
androidx.cardview.widget.CardView: void setCardElevation(float)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
com.google.android.material.chip.Chip: void setRippleColor(android.content.res.ColorStateList)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.appcompat.widget.AppCompatCheckedTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getCloseIcon()
com.google.android.material.tabs.TabLayout: void setTabTextColors(android.content.res.ColorStateList)
androidx.constraintlayout.core.widgets.analyzer.DependencyNode$Type: androidx.constraintlayout.core.widgets.analyzer.DependencyNode$Type[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
com.google.android.material.textfield.TextInputLayout: int getErrorTextCurrentColor()
androidx.appcompat.widget.DrawableUtils$Api29Impl: android.graphics.Insets getOpticalInsets(android.graphics.drawable.Drawable)
com.google.android.material.textfield.TextInputLayout: int getMinWidth()
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
androidx.viewpager.widget.ViewPager: void setPageMargin(int)
com.google.android.material.textfield.TextInputLayout: void setSuffixTextAppearance(int)
com.google.android.material.chip.Chip: android.graphics.Rect getCloseIconTouchBoundsInt()
com.google.android.material.tabs.TabLayout: int getTabMode()
com.google.android.material.button.MaterialButton: void setStrokeColor(android.content.res.ColorStateList)
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
com.google.android.material.textfield.TextInputLayout: void setEndIconDrawable(android.graphics.drawable.Drawable)
androidx.constraintlayout.widget.ConstraintAttribute$AttributeType: androidx.constraintlayout.widget.ConstraintAttribute$AttributeType[] values()
androidx.cardview.widget.CardView: boolean getUseCompatPadding()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownHorizontalOffset()
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatSpinner: androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup getInternalPopup()
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
com.google.android.material.tabs.TabLayout: void setTabRippleColor(android.content.res.ColorStateList)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
com.google.android.material.textfield.TextInputLayout: android.graphics.drawable.Drawable getEndIconDrawable()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.material.button.MaterialButton: void setInsetBottom(int)
com.google.android.material.textfield.TextInputLayout: void setExpandedHintEnabled(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
com.google.android.material.textfield.TextInputLayout: void setHintEnabled(boolean)
androidx.appcompat.widget.AppCompatSpinner: android.content.Context getPopupContext()
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
com.google.android.material.textfield.TextInputLayout: void setErrorTextAppearance(int)
com.google.android.material.behavior.SwipeDismissBehavior: SwipeDismissBehavior()
com.google.android.material.snackbar.Snackbar$SnackbarLayout: void setBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatCheckedTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
com.bm.atool.service.SocketService: SocketService()
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
com.google.android.material.tabs.TabLayout: void setSelectedTabIndicatorGravity(int)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.core.view.PointerIconCompat$Api24Impl: android.view.PointerIcon getSystemIcon(android.content.Context,int)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.cardview.widget.CardView: int getContentPaddingRight()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
com.bm.atool.ui.BaseFragment: BaseFragment()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.fragment.app.FragmentContainerView: void setLayoutTransition(android.animation.LayoutTransition)
androidx.constraintlayout.widget.Barrier: void setMargin(int)
com.google.gson.internal.bind.TypeAdapters$1: TypeAdapters$1()
com.google.android.material.button.MaterialButton: MaterialButton(android.content.Context,android.util.AttributeSet)
com.google.android.material.tabs.TabLayout: int getTabIndicatorGravity()
com.google.android.material.chip.Chip: void setMaxLines(int)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
com.google.android.material.textfield.TextInputLayout: void setTextInputAccessibilityDelegate(com.google.android.material.textfield.TextInputLayout$AccessibilityDelegate)
com.google.android.material.textfield.TextInputLayout: void setHelperTextColor(android.content.res.ColorStateList)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.viewpager.widget.ViewPager: void setScrollingCacheEnabled(boolean)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
com.bm.atool.ui.views.BlackUnderlineEditText: BlackUnderlineEditText(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
com.google.android.material.chip.Chip: void setChipIcon(android.graphics.drawable.Drawable)
com.bm.atool.LoginActivity: LoginActivity()
com.google.android.material.chip.ChipGroup: void setChipSpacingResource(int)
com.bm.atool.service.singlepixel.SinglePixelActivity: SinglePixelActivity()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatButton: void setEmojiCompatEnabled(boolean)
androidx.constraintlayout.widget.ConstraintLayout: void setMinWidth(int)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
com.google.android.material.textfield.TextInputLayout: android.widget.TextView getPrefixTextView()
androidx.appcompat.widget.AppCompatReceiveContentHelper$OnDropApi24Impl: boolean onDropForView(android.view.DragEvent,android.view.View,android.app.Activity)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
com.google.android.material.textfield.TextInputLayout: void setPrefixTextAppearance(int)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(int)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
com.google.gson.internal.bind.TypeAdapters$13: TypeAdapters$13()
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundResource(int)
com.google.android.material.chip.Chip: void setCloseIconTint(android.content.res.ColorStateList)
com.google.android.material.textfield.TextInputLayout: void setPasswordVisibilityToggleTintList(android.content.res.ColorStateList)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
androidx.appcompat.widget.AppCompatTextHelper$Api26Impl: boolean setFontVariationSettings(android.widget.TextView,java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior: FloatingActionButton$Behavior()
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintList(android.content.res.ColorStateList)
com.bm.atool.js.AndroidBridge: java.lang.String getPhoneNumbers()
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
com.google.android.material.textfield.TextInputLayout: void setEndIconCheckable(boolean)
com.google.android.material.radiobutton.MaterialRadioButton: void setUseMaterialThemeColors(boolean)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
androidx.constraintlayout.widget.ConstraintLayout: void setConstraintSet(androidx.constraintlayout.widget.ConstraintSet)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.material.button.MaterialButton: void setInsetTop(int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
com.google.android.material.internal.NavigationMenuItemView: void setTextColor(android.content.res.ColorStateList)
com.google.android.material.appbar.MaterialToolbar: void setElevation(float)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
com.bm.atool.js.AndroidBridge: java.lang.String emitSocketMessage(java.lang.String,java.lang.String)
com.google.android.material.tabs.TabLayout: int getDefaultHeight()
com.google.android.material.transformation.ExpandableTransformationBehavior: ExpandableTransformationBehavior()
com.google.android.material.chip.Chip: android.graphics.drawable.Drawable getChipDrawable()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
androidx.appcompat.widget.Toolbar: void setTitle(int)
androidx.cardview.widget.CardView: float getRadius()
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
androidx.appcompat.view.menu.ActionMenuItemView: java.lang.CharSequence getAccessibilityClassName()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
androidx.coordinatorlayout.widget.CoordinatorLayout: void setFitsSystemWindows(boolean)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColor(int)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
com.google.android.material.chip.Chip: void setBackground(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.chip.Chip: void setChipStartPadding(float)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
com.google.gson.internal.bind.TypeAdapters$20: TypeAdapters$20()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
com.google.android.material.chip.ChipGroup: void setChipSpacingVerticalResource(int)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.appcompat.app.AppCompatDelegate$Api33Impl: android.os.LocaleList localeManagerGetApplicationLocales(java.lang.Object)
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintList(android.widget.CompoundButton,android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
com.google.android.material.tabs.TabLayout: void setElevation(float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.appcompat.widget.AppCompatCheckedTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.constraintlayout.widget.ConstraintHelper: int[] getReferencedIds()
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(int)
com.google.android.material.tabs.TabLayout: void setOnTabSelectedListener(com.google.android.material.tabs.TabLayout$BaseOnTabSelectedListener)
com.bm.atool.model.Response: Response()
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getChipBackgroundColor()
androidx.constraintlayout.widget.ConstraintLayout: int getMaxWidth()
com.google.android.material.tabs.TabLayout: android.graphics.drawable.Drawable getTabSelectedIndicator()
androidx.appcompat.widget.SwitchCompat: void setFilters(android.text.InputFilter[])
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
com.google.android.material.chip.Chip: void setChipIconTintResource(int)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
com.google.android.material.chip.ChipGroup: void setChipSpacingHorizontal(int)
com.google.android.material.tabs.TabLayout: void setScrollAnimatorListener(android.animation.Animator$AnimatorListener)
com.google.android.material.chip.Chip: void setCloseIconEndPadding(float)
com.google.android.material.textfield.TextInputLayout: void setErrorEnabled(boolean)
androidx.recyclerview.widget.RecyclerView: boolean isLayoutSuppressed()
com.google.android.material.chip.Chip: void setChipIconEnabled(boolean)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getEndIconContentDescription()
androidx.core.view.ViewCompat$Api24Impl: void setPointerIcon(android.view.View,android.view.PointerIcon)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
androidx.startup.InitializationProvider: InitializationProvider()
com.google.android.material.chip.Chip: float getChipEndPadding()
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusBottomStart()
com.google.android.material.chip.Chip: void setTextAppearance(com.google.android.material.resources.TextAppearance)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
com.google.android.material.chip.ChipGroup: int getChipSpacingHorizontal()
com.google.android.material.button.MaterialButton: void setIconTintResource(int)
com.google.android.material.tabs.TabLayout: void setTabIconTint(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.viewpager.widget.ViewPager: void setPageMarginDrawable(int)
androidx.viewpager.widget.ViewPager: androidx.viewpager.widget.PagerAdapter getAdapter()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.core.text.ICUCompat$Api21Impl: java.lang.String getScript(java.util.Locale)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
com.google.android.material.tabs.TabLayout: void setSelectedTabIndicator(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: void setEmojiCompatEnabled(boolean)
com.google.android.material.button.MaterialButton: java.lang.String getA11yClassName()
com.google.android.material.chip.Chip: void setChipStrokeColorResource(int)
androidx.fragment.app.FragmentContainerView: androidx.fragment.app.Fragment getFragment()
com.google.android.material.bottomappbar.BottomAppBar$Behavior: BottomAppBar$Behavior(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.constraintlayout.widget.ConstraintHelper: void setIds(java.lang.String)
com.google.android.material.chip.Chip: android.content.res.ColorStateList getRippleColor()
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
androidx.recyclerview.widget.RecyclerView: java.lang.CharSequence getAccessibilityClassName()
com.google.android.material.textfield.TextInputLayout: void setPrefixText(java.lang.CharSequence)
com.google.android.material.timepicker.ClockFaceView: ClockFaceView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.appcompat.widget.Toolbar: void setLogo(int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getHelperText()
androidx.appcompat.widget.AppCompatTextView: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
com.google.android.material.chip.Chip: void setCheckableResource(int)
com.bm.atool.model.PingModel: PingModel()
androidx.appcompat.widget.AppCompatRadioButton: void setAllCaps(boolean)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
com.google.android.material.bottomappbar.BottomAppBar$Behavior: BottomAppBar$Behavior()
com.google.android.material.internal.NavigationMenuItemView: void setHorizontalPadding(int)
com.google.android.material.textfield.TextInputLayout: void setBoxBackgroundColorResource(int)
com.google.android.material.textfield.TextInputLayout: void setStartIconDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCheckMarkDrawable(int)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownWidth(int)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeColorStateList(android.content.res.ColorStateList)
com.google.android.material.button.MaterialButtonToggleGroup: void setGeneratedIdIfNeeded(com.google.android.material.button.MaterialButton)
androidx.appcompat.widget.AppCompatCheckedTextView: android.content.res.ColorStateList getSupportCheckMarkTintList()
com.google.android.material.bottomsheet.BottomSheetBehavior: BottomSheetBehavior()
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.constraintlayout.widget.Guideline: void setGuidelineEnd(int)
com.google.android.material.chip.Chip: void setLayoutDirection(int)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusBottomEnd()
androidx.core.text.ICUCompat$Api24Impl: java.lang.String getScript(java.lang.Object)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
com.google.android.material.button.MaterialButton: int getTextHeight()
com.bm.atool.service.ANTAccessibilityService: ANTAccessibilityService()
com.google.android.material.chip.Chip: void setEllipsize(android.text.TextUtils$TruncateAt)
com.google.android.material.tabs.TabLayout: void setTabsFromPagerAdapter(androidx.viewpager.widget.PagerAdapter)
androidx.appcompat.view.WindowCallbackWrapper$Api24Impl: void onProvideKeyboardShortcuts(android.view.Window$Callback,java.util.List,android.view.Menu,int)
com.google.gson.internal.bind.ArrayTypeAdapter$1: ArrayTypeAdapter$1()
com.google.android.material.chip.Chip: void setChipStrokeWidthResource(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
com.google.android.material.checkbox.MaterialCheckBox: android.content.res.ColorStateList getMaterialThemeColorsTintList()
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior: ExtendedFloatingActionButton$ExtendedFloatingActionButtonBehavior()
com.google.android.material.button.MaterialButton: void setShapeAppearanceModel(com.google.android.material.shape.ShapeAppearanceModel)
androidx.appcompat.view.WindowCallbackWrapper$Api23Impl: android.view.ActionMode onWindowStartingActionMode(android.view.Window$Callback,android.view.ActionMode$Callback,int)
com.google.android.material.textfield.TextInputLayout: java.lang.CharSequence getStartIconContentDescription()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
com.google.android.material.chip.Chip: void setIconEndPaddingResource(int)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
androidx.core.view.PointerIconCompat$Api24Impl: android.view.PointerIcon create(android.graphics.Bitmap,float,float)
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.AppCompatButton: void setFilters(android.text.InputFilter[])
androidx.appcompat.widget.AppCompatRadioButton: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
com.google.android.material.tabs.TabLayout: void setUnboundedRippleResource(int)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
android.support.v4.os.IResultReceiver2$Stub: android.support.v4.os.IResultReceiver2 asInterface(android.os.IBinder)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
com.google.android.material.chip.Chip: float getChipStartPadding()
com.google.android.material.snackbar.SnackbarContentLayout: void setMaxInlineActionWidth(int)
com.google.android.material.chip.Chip: java.lang.CharSequence getCloseIconContentDescription()
androidx.appcompat.widget.SwitchCompat: void setEnforceSwitchWidth(boolean)
com.google.android.material.textfield.TextInputLayout: int getHintCurrentCollapsedTextColor()
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getHintTextColor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.constraintlayout.widget.ConstraintLayout: int getMinHeight()
androidx.constraintlayout.widget.Guideline: void setFilterRedundantCalls(boolean)
com.google.android.material.textfield.TextInputLayout: void setBoxStrokeWidthResource(int)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
com.google.android.material.tabs.TabLayout: int getTabScrollRange()
com.google.android.material.textfield.TextInputLayout: float getBoxCornerRadiusTopEnd()
androidx.core.view.ViewCompat$Api24Impl: void cancelDragAndDrop(android.view.View)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.constraintlayout.widget.ConstraintLayout: void setOnConstraintsChanged(androidx.constraintlayout.widget.ConstraintsChangedListener)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
com.google.android.material.internal.FlowLayout: void setItemSpacing(int)
io.socket.client.Manager$ReadyState: io.socket.client.Manager$ReadyState valueOf(java.lang.String)
androidx.core.app.ActivityCompat$Api23Impl: void onSharedElementsReady(java.lang.Object)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
com.google.android.material.chip.Chip: void setCloseIconEndPaddingResource(int)
io.socket.engineio.client.Socket$ReadyState: io.socket.engineio.client.Socket$ReadyState valueOf(java.lang.String)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
com.google.android.material.button.MaterialButton: int getInsetTop()
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: int getMaxWidth()
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.constraintlayout.widget.Guideline: void setGuidelinePercent(float)
com.google.android.material.chip.Chip: void setChipEndPaddingResource(int)
com.google.android.material.textview.MaterialTextView: MaterialTextView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
com.google.android.material.chip.Chip: void setChipIconEnabledResource(int)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
com.google.android.material.button.MaterialButton: void setElevation(float)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
com.google.android.material.snackbar.BaseTransientBottomBar$SnackbarBaseLayout: void setOnAttachStateChangeListener(com.google.android.material.snackbar.BaseTransientBottomBar$OnAttachStateChangeListener)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.appcompat.widget.AppCompatSpinner: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
androidx.appcompat.widget.AppCompatTextView: androidx.appcompat.widget.AppCompatTextView$SuperCaller getSuperCaller()
com.google.android.material.internal.CheckableImageButton: void setPressable(boolean)
com.google.gson.internal.bind.TypeAdapters$12: TypeAdapters$12()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
com.google.android.material.chip.Chip: void setCheckedIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
android.support.v4.os.IResultReceiver$Stub: android.support.v4.os.IResultReceiver asInterface(android.os.IBinder)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
com.google.android.material.textfield.TextInputLayout: int getBoxStrokeColor()
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
com.google.android.material.chip.Chip: java.lang.CharSequence getAccessibilityClassName()
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
com.google.android.material.chip.Chip: void setChipStrokeColor(android.content.res.ColorStateList)
com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberFormat: com.google.i18n.phonenumbers.PhoneNumberUtil$PhoneNumberFormat valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
com.bm.atool.model.SmsSyncRequest: SmsSyncRequest()
com.google.gson.internal.bind.TypeAdapters$8: TypeAdapters$8()
com.google.android.material.textfield.MaterialAutoCompleteTextView: java.lang.CharSequence getHint()
com.bm.atool.service.NotificationService: NotificationService()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatToggleButton: void setFilters(android.text.InputFilter[])
com.google.android.material.theme.MaterialComponentsViewInflater: MaterialComponentsViewInflater()
com.google.gson.internal.sql.SqlTimeTypeAdapter: SqlTimeTypeAdapter()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
com.google.android.material.button.MaterialButton: void setCheckable(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.coordinatorlayout.widget.CoordinatorLayout: androidx.core.view.WindowInsetsCompat getLastWindowInsets()
com.google.android.material.textfield.TextInputLayout: void setStartIconVisible(boolean)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.appcompat.widget.AppCompatButton: android.view.ActionMode$Callback getCustomSelectionActionModeCallback()
androidx.viewpager.widget.ViewPager: void setPageMarginDrawable(android.graphics.drawable.Drawable)
com.google.android.material.button.MaterialButton: void setBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.fragment.app.DialogFragment: DialogFragment()
androidx.appcompat.widget.Toolbar$Api33Impl: android.window.OnBackInvokedDispatcher findOnBackInvokedDispatcher(android.view.View)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.AppCompatCheckedTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
com.bm.atool.App: App()
com.google.android.material.internal.CheckableImageButton: void setPressed(boolean)
com.google.android.material.textfield.TextInputLayout: void setHelperTextEnabled(boolean)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
com.google.android.material.button.MaterialButton: void setChecked(boolean)
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
com.google.android.material.bottomsheet.BottomSheetBehavior: BottomSheetBehavior(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: void setBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
app.cash.quickjs.QuickJsException: java.lang.StackTraceElement toStackTraceElement(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundResource(int)
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatEditText: void setTextClassifier(android.view.textclassifier.TextClassifier)
androidx.viewpager.widget.ViewPager: void setScrollState(int)
com.google.android.material.button.MaterialButtonToggleGroup: void setSingleSelection(boolean)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.appcompat.widget.AppCompatSpinner: java.lang.CharSequence getPrompt()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
com.bm.atool.js.AndroidBridge: java.lang.String executeUssd(java.lang.String,java.lang.String,int)
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.MenuPopupWindow$Api29Impl: void setTouchModal(android.widget.PopupWindow,boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
com.google.android.material.chip.Chip: void setChipIconResource(int)
com.google.android.material.button.MaterialButton: void setCornerRadius(int)
com.google.android.material.textfield.TextInputLayout: com.google.android.material.shape.MaterialShapeDrawable getBoxBackground()
io.reactivex.internal.disposables.DisposableHelper: io.reactivex.internal.disposables.DisposableHelper valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
com.google.android.material.chip.Chip: void setCloseIconVisible(int)
com.google.android.material.chip.Chip: void setCloseIconPressed(boolean)
com.google.android.material.tabs.TabLayout: void setTabIndicatorFullWidth(boolean)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
com.google.android.material.textfield.TextInputLayout: void setMaxWidth(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
com.google.gson.LongSerializationPolicy: com.google.gson.LongSerializationPolicy[] values()
androidx.appcompat.widget.AppCompatCheckedTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.appcompat.widget.AppCompatSpinner: int getDropDownVerticalOffset()
androidx.cardview.widget.CardView: int getContentPaddingTop()
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackgroundResource(int)
com.google.android.material.floatingactionbutton.FloatingActionButton$Behavior: FloatingActionButton$Behavior(android.content.Context,android.util.AttributeSet)
com.google.gson.internal.bind.TypeAdapters$19: TypeAdapters$19()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: AppCompatTextViewAutoSizeHelper$Impl29()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
com.bm.atool.model.SmsApi: retrofit2.Call ack(com.bm.atool.model.SmsAckRequest)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.coordinatorlayout.widget.CoordinatorLayout: void setStatusBarBackgroundColor(int)
com.google.android.material.chip.Chip: void setShowMotionSpecResource(int)
io.reactivex.internal.disposables.EmptyDisposable: io.reactivex.internal.disposables.EmptyDisposable[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
com.google.android.material.button.MaterialButton: void setBackgroundTintList(android.content.res.ColorStateList)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.os.LocaleListCompat$Api21Impl: boolean matchesLanguageAndScript(java.util.Locale,java.util.Locale)
com.google.android.material.internal.NavigationMenuItemView: void setIconSize(int)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
com.google.gson.internal.bind.TypeAdapters$10: TypeAdapters$10()
androidx.fragment.app.FragmentContainerView: void setOnApplyWindowInsetsListener(android.view.View$OnApplyWindowInsetsListener)
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.content.res.ColorStateList getButtonTintList(android.widget.CompoundButton)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
com.google.android.material.appbar.MaterialToolbar: java.lang.Integer getNavigationIconTint()
com.google.android.material.button.MaterialButton: void setIconTint(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
com.google.android.material.chip.ChipGroup: void setOnHierarchyChangeListener(android.view.ViewGroup$OnHierarchyChangeListener)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatCheckBox: androidx.appcompat.widget.AppCompatEmojiTextHelper getEmojiTextViewHelper()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
com.google.android.material.chip.Chip: void setCloseIconContentDescription(java.lang.CharSequence)
com.google.android.material.chip.Chip: float getIconStartPadding()
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
com.google.android.material.textfield.TextInputLayout: android.content.res.ColorStateList getSuffixTextColor()
androidx.appcompat.app.AppCompatDelegateImpl$Api33Impl: android.window.OnBackInvokedCallback registerOnBackPressedCallback(java.lang.Object,androidx.appcompat.app.AppCompatDelegateImpl)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
