// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SettingRowBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final Button btnGoSetting;

  @NonNull
  public final ImageView imgStatus;

  @NonNull
  public final TextView txtName;

  private SettingRowBinding(@NonNull RelativeLayout rootView, @NonNull Button btnGoSetting,
      @NonNull ImageView imgStatus, @NonNull TextView txtName) {
    this.rootView = rootView;
    this.btnGoSetting = btnGoSetting;
    this.imgStatus = imgStatus;
    this.txtName = txtName;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SettingRowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SettingRowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.setting_row, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SettingRowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnGoSetting;
      Button btnGoSetting = ViewBindings.findChildViewById(rootView, id);
      if (btnGoSetting == null) {
        break missingId;
      }

      id = R.id.imgStatus;
      ImageView imgStatus = ViewBindings.findChildViewById(rootView, id);
      if (imgStatus == null) {
        break missingId;
      }

      id = R.id.txtName;
      TextView txtName = ViewBindings.findChildViewById(rootView, id);
      if (txtName == null) {
        break missingId;
      }

      return new SettingRowBinding((RelativeLayout) rootView, btnGoSetting, imgStatus, txtName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
