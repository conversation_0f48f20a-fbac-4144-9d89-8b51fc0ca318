<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/app_background_color"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/debugTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Debug &amp; JavaScript Test"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:padding="8dp"
            android:background="@color/header_background"
            android:elevation="4dp"
            android:layout_marginBottom="16dp"/>

        <!-- JavaScript测试区域 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="JavaScript Test"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"/>

        <EditText
            android:id="@+id/etJavaScript"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:background="@color/input_background"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_secondary"
            android:hint="Enter JavaScript code here..."
            android:gravity="top|start"
            android:inputType="textMultiLine"
            android:padding="12dp"
            android:textSize="14sp"
            android:fontFamily="monospace"
            android:layout_marginBottom="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btnExecuteJS"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:backgroundTint="@color/button_primary"
                android:textColor="@color/white"
                android:text="Execute"
                android:layout_marginEnd="8dp"/>

            <Button
                android:id="@+id/btnClearJS"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:backgroundTint="@color/button_secondary"
                android:textColor="@color/white"
                android:text="Clear"
                android:layout_marginStart="8dp"/>

        </LinearLayout>

        <!-- 预设脚本按钮 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Quick Tests"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnTestDeviceInfo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:backgroundTint="@color/button_accent"
                android:textColor="@color/white"
                android:text="Device Info"
                android:textSize="12sp"
                android:layout_marginEnd="4dp"/>

            <Button
                android:id="@+id/btnTestPhones"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:backgroundTint="@color/button_accent"
                android:textColor="@color/white"
                android:text="Phone Numbers"
                android:textSize="12sp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"/>

            <Button
                android:id="@+id/btnTestBattery"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:backgroundTint="@color/button_accent"
                android:textColor="@color/white"
                android:text="Battery"
                android:textSize="12sp"
                android:layout_marginStart="4dp"/>

        </LinearLayout>

        <!-- 执行结果显示 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Execution Result"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"/>

        <TextView
            android:id="@+id/tvJSResult"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:background="@color/input_background"
            android:textColor="@color/text_primary"
            android:padding="12dp"
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:gravity="top|start"
            android:text="No result yet..."
            android:layout_marginBottom="16dp"/>

        <!-- 系统控制按钮 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="System Control"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"/>

        <Button
            android:id="@+id/btnStopSocket"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/button_primary"
            android:textColor="@color/white"
            android:elevation="4dp"
            android:padding="12dp"
            android:textSize="16sp"
            android:text="Stop All Services"
            android:layout_marginBottom="16dp"/>

    </LinearLayout>

</ScrollView>