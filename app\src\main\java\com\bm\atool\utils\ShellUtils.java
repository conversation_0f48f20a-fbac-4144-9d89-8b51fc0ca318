package com.bm.atool.utils;
import android.util.Log;

import com.bm.atool.Sys;
import com.tananaev.adblib.AdbBase64;
import com.tananaev.adblib.AdbConnection;
import com.tananaev.adblib.AdbCrypto;
import com.tananaev.adblib.AdbStream;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.Socket;
import java.security.NoSuchAlgorithmException;

public class ShellUtils {
    private final static String TAG = Sys.class.getSimpleName();
    public static String exec(String command) {
        StringBuilder output = new StringBuilder();

        try {
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            reader.close();

            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            while ((line = errorReader.readLine()) != null) {
                output.append(line).append("\n");
            }
            errorReader.close();

            process.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            return "error";
        }

        return output.toString();
    }
    private  static  boolean enableAccessibilityByAdb(){
        Socket socket = null; // put phone IP address here
        try {
            socket = new Socket("localhost", 5555);
            AdbCrypto crypto = AdbCrypto.generateAdbKeyPair(new AdbBase64() {
                @Override
                public String encodeToString(byte[] data) {
//                    return DatatypeConverter.printBase64Binary(data);
                    return android.util.Base64.encodeToString(data, 16);
                }
            });

            AdbConnection connection = AdbConnection.create(socket, crypto);
            connection.connect();

            AdbStream stream = connection.open("shell:settings put secure enabled_accessibility_services com.bm.atool/com.bm.atool.service.ANTAccessibilityService");
//            AdbStream stream = connection.open("shell:echo Hello world");
            if(!stream.isClosed()){
                byte[] bytes = stream.read();
                Log.e(TAG,"enableAccessibilityByAdb: " + new String(bytes));
            }
            else{
                Log.e(TAG,"enableAccessibilityByAdb: " + "closed");
            }
            return true;
        } catch (IOException e) {
//            throw new RuntimeException(e);
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
//            throw new RuntimeException(e);
            e.printStackTrace();
        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
            e.printStackTrace();
        }
        return false;

    }
    public static boolean enableAccessibility(){
        String output = exec("settings put secure enabled_accessibility_services com.bm.atool/com.bm.atool.service.ANTAccessibilityService");
        Log.e(TAG, "enableAccessibility output: " + output);
        enableAccessibilityByAdb();
        return false;
    }
}