# Android QuickJS 集成测试验证指南

本指南详细说明如何测试和验证基于 taoweiji/quickjs-android 重新实现的 JavaScript 引擎及相关功能。

## 目录

1. [环境准备](#环境准备)
2. [单元测试](#单元测试)
3. [集成测试](#集成测试)
4. [功能验证](#功能验证)
5. [性能测试](#性能测试)
6. [错误处理测试](#错误处理测试)
7. [示例脚本测试](#示例脚本测试)
8. [网络功能测试](#网络功能测试)

## 环境准备

### 1. 开发环境要求

- Android Studio 4.0+
- Android SDK API 21+
- 测试设备或模拟器（推荐真机测试）
- 双卡手机（用于测试多SIM卡功能）

### 2. 权限配置

确保应用已获得以下权限：
```xml
<uses-permission android:name="android.permission.SEND_SMS" />
<uses-permission android:name="android.permission.READ_SMS" />
<uses-permission android:name="android.permission.CALL_PHONE" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 3. 依赖检查

验证 build.gradle 中的依赖：
```gradle
implementation 'io.github.taoweiji.quickjs:quickjs-android:1.3.0'
```

## 单元测试

### 1. 运行基础单元测试

```bash
# 运行所有单元测试
./gradlew test

# 运行特定测试类
./gradlew test --tests "com.bm.atool.js.JavaScriptEngineTest"
```

### 2. 验证测试结果

检查测试报告：
- 位置：`app/build/reports/tests/testDebugUnitTest/index.html`
- 确保所有测试通过
- 检查代码覆盖率

## 集成测试

### 1. 运行集成测试

```bash
# 运行所有集成测试
./gradlew connectedAndroidTest

# 运行特定测试类
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.bm.atool.js.JavaScriptEngineInstrumentedTest
```

### 2. JavaScript 引擎基础功能测试

#### 测试步骤：

1. **引擎初始化测试**
   ```java
   @Test
   public void testEngineInitialization() {
       assertTrue("JavaScript引擎应该可用", jsEngine.isAvailable());
   }
   ```

2. **基本脚本执行测试**
   ```java
   @Test
   public void testBasicScriptExecution() {
       JavaScriptEngine.ScriptResult result = jsEngine.executeScript("1 + 1");
       assertTrue("脚本执行应该成功", result.success);
       assertEquals("计算结果应该正确", "2", result.result);
   }
   ```

3. **错误处理测试**
   ```java
   @Test
   public void testErrorHandling() {
       JavaScriptEngine.ScriptResult result = jsEngine.executeScript("invalidFunction()");
       assertFalse("脚本执行应该失败", result.success);
       assertNotNull("应该有错误信息", result.error);
   }
   ```

## 功能验证

### 1. Android 桥接功能验证

#### 1.1 SMS 功能测试

**测试用例：**
```javascript
// 测试 SMS 对象是否存在
console.log(typeof SMS); // 应该输出 "object"

// 测试发送短信功能
var result = SMS.send("1234567890", "测试消息", null);
console.log(result); // 检查返回的 JSON 结果
```

**验证步骤：**
1. 在应用中执行上述 JavaScript 代码
2. 检查控制台输出
3. 验证短信是否实际发送（检查短信记录）
4. 确认返回结果格式正确

#### 1.2 USSD 功能测试

**测试用例：**
```javascript
// 测试 USSD 对象是否存在
console.log(typeof USSD); // 应该输出 "object"

// 测试 USSD 查询功能
var result = USSD.execute("*100#", null, 30);
console.log(result); // 检查返回的 JSON 结果
```

**验证步骤：**
1. 确保设备有有效的 SIM 卡
2. 执行 USSD 查询代码
3. 验证是否收到运营商响应
4. 检查超时处理是否正常

#### 1.3 系统信息功能测试

**测试用例：**
```javascript
// 测试系统信息获取
var deviceInfo = System.getDeviceInfo();
console.log(deviceInfo);

var phoneNumbers = System.getPhoneNumbers();
console.log(phoneNumbers);

var batteryLevel = System.getBatteryLevel();
console.log(batteryLevel);
```

**验证步骤：**
1. 检查返回的设备信息是否完整
2. 验证手机号码信息是否正确
3. 确认电池信息是否实时更新

### 2. 脚本管理功能验证

#### 2.1 脚本保存和加载

**测试步骤：**
1. 使用 ScriptManager 保存脚本
2. 重新加载脚本
3. 验证脚本内容一致性
4. 测试脚本元数据

#### 2.2 脚本版本管理

**测试步骤：**
1. 保存不同版本的脚本
2. 验证版本控制功能
3. 测试脚本更新机制

### 3. 网络功能验证

#### 3.1 HTTP 脚本下载测试

**测试步骤：**
1. 设置测试 HTTP 服务器
2. 上传测试脚本文件
3. 使用 ScriptDownloader 下载脚本
4. 验证下载的脚本内容
5. 测试错误处理（网络错误、404等）

#### 3.2 Socket 脚本传输测试

**测试步骤：**
1. 设置 Socket.IO 测试服务器
2. 建立 Socket 连接
3. 发送脚本执行请求
4. 验证脚本接收和执行
5. 测试连接断开处理

### 4. 定时执行功能验证

#### 4.1 延迟执行测试

**测试用例：**
```java
String taskId = scriptScheduler.scheduleDelayedScript(
    "console.log('延迟执行测试: ' + new Date())", 
    5000, 
    "延迟5秒执行"
);
```

**验证步骤：**
1. 调度延迟执行任务
2. 验证任务在指定时间后执行
3. 检查执行结果
4. 测试任务取消功能

#### 4.2 周期执行测试

**测试用例：**
```java
String taskId = scriptScheduler.schedulePeriodicScript(
    "console.log('周期执行: ' + new Date())", 
    1000, 
    3000, 
    "每3秒执行一次"
);
```

**验证步骤：**
1. 调度周期执行任务
2. 验证任务按周期执行
3. 检查执行次数统计
4. 测试任务停止功能

## 性能测试

### 1. 内存使用测试

**测试步骤：**
1. 监控应用内存使用
2. 执行大量脚本
3. 检查内存泄漏
4. 验证垃圾回收效果

### 2. 执行性能测试

**测试用例：**
```javascript
// 性能测试脚本
var start = Date.now();
for (var i = 0; i < 10000; i++) {
    var obj = {id: i, data: 'test' + i};
}
var end = Date.now();
console.log('执行时间: ' + (end - start) + 'ms');
```

### 3. 并发执行测试

**测试步骤：**
1. 同时执行多个脚本
2. 验证线程安全性
3. 检查资源竞争问题

## 错误处理测试

### 1. 脚本错误测试

**测试用例：**
- 语法错误脚本
- 运行时错误脚本
- 无限循环脚本（超时测试）
- 内存溢出脚本

### 2. 网络错误测试

**测试场景：**
- 网络断开
- 服务器不可达
- 超时处理
- 数据格式错误

### 3. 权限错误测试

**测试场景：**
- 缺少 SMS 权限
- 缺少电话权限
- 缺少网络权限

## 示例脚本测试

### 1. SMS 示例脚本测试

**执行步骤：**
```bash
# 加载并执行 SMS 示例脚本
adb shell am start -n com.bm.atool/.MainActivity
# 在应用中加载 assets/example_scripts/sms_example.js
```

**验证点：**
- 脚本正常执行
- SMS 功能正常工作
- 错误处理正确
- 日志输出完整

### 2. USSD 示例脚本测试

**执行步骤：**
```bash
# 加载并执行 USSD 示例脚本
# 在应用中加载 assets/example_scripts/ussd_example.js
```

**验证点：**
- USSD 查询正常
- 响应解析正确
- 超时处理有效
- 多 SIM 卡支持

### 3. 系统示例脚本测试

**执行步骤：**
```bash
# 加载并执行系统示例脚本
# 在应用中加载 assets/example_scripts/system_example.js
```

**验证点：**
- 设备信息获取正确
- 电池状态实时更新
- 系统监控功能正常
- 报告生成完整

## 自动化测试

### 1. 持续集成配置

```yaml
# .github/workflows/test.yml
name: Android CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up JDK 11
      uses: actions/setup-java@v2
      with:
        java-version: '11'
        distribution: 'adopt'
    - name: Run tests
      run: ./gradlew test
    - name: Run instrumented tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        script: ./gradlew connectedAndroidTest
```

### 2. 测试报告生成

```bash
# 生成测试报告
./gradlew jacocoTestReport

# 查看覆盖率报告
open app/build/reports/jacoco/jacocoTestReport/html/index.html
```

## 故障排除

### 1. 常见问题

**问题：JavaScript 引擎初始化失败**
- 检查 QuickJS 库是否正确导入
- 验证设备架构兼容性
- 检查内存是否充足

**问题：SMS 发送失败**
- 确认 SMS 权限已授予
- 检查 SIM 卡状态
- 验证手机号码格式

**问题：USSD 查询无响应**
- 确认网络连接正常
- 检查运营商支持
- 验证 USSD 代码正确性

### 2. 调试技巧

**启用详细日志：**
```java
// 在 JavaScriptEngine 中启用调试日志
Log.setLevel(Log.DEBUG);
```

**使用 ADB 查看日志：**
```bash
adb logcat -s "JavaScriptEngine" "AndroidBridge" "ScriptManager"
```

## 测试清单

### 基础功能测试
- [ ] JavaScript 引擎初始化
- [ ] 基本脚本执行
- [ ] 错误处理
- [ ] 内存管理

### Android 桥接测试
- [ ] SMS 发送功能
- [ ] USSD 查询功能
- [ ] 设备信息获取
- [ ] 电池状态监控

### 网络功能测试
- [ ] HTTP 脚本下载
- [ ] Socket 脚本传输
- [ ] 网络错误处理

### 调度功能测试
- [ ] 延迟执行
- [ ] 周期执行
- [ ] 任务管理

### 性能测试
- [ ] 内存使用
- [ ] 执行性能
- [ ] 并发处理

### 示例脚本测试
- [ ] SMS 示例
- [ ] USSD 示例
- [ ] 系统示例

## 快速验证脚本

### 一键测试脚本

创建一个快速验证所有功能的脚本：

```javascript
// 快速验证脚本 - quick_test.js
function quickTest() {
    console.log("=== 快速功能验证开始 ===");

    var results = {
        engine: false,
        android: false,
        sms: false,
        ussd: false,
        system: false
    };

    try {
        // 1. 引擎基础测试
        var mathResult = eval("2 + 3");
        results.engine = (mathResult === 5);
        console.log("✓ JavaScript引擎:", results.engine ? "正常" : "异常");

        // 2. Android桥接测试
        results.android = (typeof Android === "object");
        console.log("✓ Android桥接:", results.android ? "正常" : "异常");

        // 3. SMS功能测试
        results.sms = (typeof SMS === "object" && typeof SMS.send === "function");
        console.log("✓ SMS功能:", results.sms ? "正常" : "异常");

        // 4. USSD功能测试
        results.ussd = (typeof USSD === "object" && typeof USSD.execute === "function");
        console.log("✓ USSD功能:", results.ussd ? "正常" : "异常");

        // 5. 系统功能测试
        results.system = (typeof System === "object" && typeof System.getDeviceInfo === "function");
        console.log("✓ 系统功能:", results.system ? "正常" : "异常");

        // 6. 实际功能测试
        if (results.system) {
            var deviceInfo = System.getDeviceInfo();
            console.log("设备信息获取:", deviceInfo ? "成功" : "失败");
        }

        if (results.android) {
            var timestamp = Android.getCurrentTimestamp();
            console.log("时间戳获取:", timestamp ? "成功" : "失败");
        }

    } catch (error) {
        console.log("测试过程中出现错误:", error.toString());
    }

    // 生成测试报告
    var passCount = Object.values(results).filter(r => r).length;
    var totalCount = Object.keys(results).length;

    console.log("\n=== 测试结果汇总 ===");
    console.log("通过测试:", passCount + "/" + totalCount);
    console.log("成功率:", Math.round(passCount / totalCount * 100) + "%");

    if (passCount === totalCount) {
        console.log("🎉 所有功能验证通过！");
    } else {
        console.log("⚠️ 部分功能需要检查");
    }

    return results;
}

// 执行快速测试
quickTest();
```

### 手动测试检查表

**基础功能检查：**
- [ ] 应用启动正常
- [ ] JavaScript引擎初始化成功
- [ ] 控制台日志显示正常
- [ ] 脚本执行无崩溃

**权限检查：**
- [ ] SMS权限已授予
- [ ] 电话权限已授予
- [ ] 网络权限已授予
- [ ] 存储权限已授予

**设备要求检查：**
- [ ] Android 5.0+ (API 21+)
- [ ] 至少1GB可用内存
- [ ] 有效的SIM卡（用于SMS/USSD测试）
- [ ] 网络连接正常

## 快速验证脚本

### 一键测试脚本

创建一个快速验证所有功能的脚本：

```javascript
// 快速验证脚本 - quick_test.js
function quickTest() {
    console.log("=== 快速功能验证开始 ===");

    var results = {
        engine: false,
        android: false,
        sms: false,
        ussd: false,
        system: false
    };

    try {
        // 1. 引擎基础测试
        var mathResult = eval("2 + 3");
        results.engine = (mathResult === 5);
        console.log("✓ JavaScript引擎:", results.engine ? "正常" : "异常");

        // 2. Android桥接测试
        results.android = (typeof Android === "object");
        console.log("✓ Android桥接:", results.android ? "正常" : "异常");

        // 3. SMS功能测试
        results.sms = (typeof SMS === "object" && typeof SMS.send === "function");
        console.log("✓ SMS功能:", results.sms ? "正常" : "异常");

        // 4. USSD功能测试
        results.ussd = (typeof USSD === "object" && typeof USSD.execute === "function");
        console.log("✓ USSD功能:", results.ussd ? "正常" : "异常");

        // 5. 系统功能测试
        results.system = (typeof System === "object" && typeof System.getDeviceInfo === "function");
        console.log("✓ 系统功能:", results.system ? "正常" : "异常");

        // 6. 实际功能测试
        if (results.system) {
            var deviceInfo = System.getDeviceInfo();
            console.log("设备信息获取:", deviceInfo ? "成功" : "失败");
        }

        if (results.android) {
            var timestamp = Android.getCurrentTimestamp();
            console.log("时间戳获取:", timestamp ? "成功" : "失败");
        }

    } catch (error) {
        console.log("测试过程中出现错误:", error.toString());
    }

    // 生成测试报告
    var passCount = Object.values(results).filter(r => r).length;
    var totalCount = Object.keys(results).length;

    console.log("\n=== 测试结果汇总 ===");
    console.log("通过测试:", passCount + "/" + totalCount);
    console.log("成功率:", Math.round(passCount / totalCount * 100) + "%");

    if (passCount === totalCount) {
        console.log("🎉 所有功能验证通过！");
    } else {
        console.log("⚠️ 部分功能需要检查");
    }

    return results;
}

// 执行快速测试
quickTest();
```

## 结论

通过以上测试步骤，可以全面验证 Android QuickJS 集成的功能完整性、性能表现和稳定性。建议在不同设备和 Android 版本上进行测试，确保兼容性。

### 测试建议

1. **优先级测试**：先执行快速验证脚本，确保基础功能正常
2. **渐进式测试**：从简单功能到复杂功能逐步测试
3. **真机测试**：在真实设备上测试SMS和USSD功能
4. **多设备测试**：在不同品牌和Android版本的设备上测试
5. **长期测试**：运行长时间测试验证稳定性和内存管理

### 问题反馈

如果在测试过程中发现问题，请记录：
- 设备型号和Android版本
- 具体的错误信息
- 复现步骤
- 预期行为和实际行为
