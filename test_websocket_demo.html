<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript引擎测试客户端</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        
        textarea {
            width: 100%;
            height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
        
        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .test-button {
            background-color: #28a745;
            padding: 15px;
            text-align: center;
            border-radius: 4px;
        }
        .test-button:hover { background-color: #218838; }
        
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🚀 JavaScript引擎测试客户端</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="input-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="http://*************:3000" placeholder="输入你的服务器地址">
        </div>
        
        <button onclick="connect()">连接</button>
        <button onclick="disconnect()">断开连接</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>
    
    <div class="container">
        <h2>JavaScript代码执行</h2>
        <textarea id="jsCode" placeholder="在这里输入JavaScript代码...">// 获取设备信息
console.log("开始测试JavaScript引擎...");
var deviceInfo = System.getDeviceInfo();
console.log("设备信息:", deviceInfo);

var phoneNumbers = System.getPhoneNumbers();
console.log("手机号码:", phoneNumbers);

var battery = System.getBatteryLevel();
console.log("电池电量:", battery + "%");

"测试完成！";</textarea>
        
        <div>
            <button onclick="executeJS()">执行JavaScript</button>
            <button onclick="loadDemoScript()">加载演示脚本</button>
        </div>
        
        <div class="quick-tests">
            <button class="test-button" onclick="testDeviceInfo()">测试设备信息</button>
            <button class="test-button" onclick="testPhoneNumbers()">测试手机号码</button>
            <button class="test-button" onclick="testBattery()">测试电池电量</button>
            <button class="test-button" onclick="testTimestamp()">测试时间戳</button>
        </div>
    </div>
    
    <div class="container">
        <h2>执行结果</h2>
        <div id="output" class="output">等待执行JavaScript代码...</div>
    </div>

    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script>
        let socket = null;
        let requestId = 1;
        
        function updateStatus(message, className) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + className;
        }
        
        function addOutput(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            if (!serverUrl) {
                alert('请输入服务器地址');
                return;
            }
            
            updateStatus('正在连接...', 'connecting');
            addOutput('正在连接到: ' + serverUrl);
            
            socket = io(serverUrl);
            
            socket.on('connect', () => {
                updateStatus('已连接', 'connected');
                addOutput('✅ WebSocket连接成功！');
            });
            
            socket.on('disconnect', () => {
                updateStatus('连接断开', 'disconnected');
                addOutput('❌ WebSocket连接断开');
            });
            
            socket.on('jsResponse', (data) => {
                addOutput('📨 收到JavaScript执行结果:');
                try {
                    const response = typeof data === 'string' ? JSON.parse(data) : data;
                    addOutput(`请求ID: ${response.id}`);
                    addOutput(`执行成功: ${response.success}`);
                    addOutput(`执行时间: ${response.executionTime}ms`);
                    
                    if (response.success) {
                        addOutput(`✅ 结果: ${response.result}`);
                    } else {
                        addOutput(`❌ 错误: ${response.error}`);
                    }
                } catch (e) {
                    addOutput(`解析响应失败: ${e.message}`);
                    addOutput(`原始数据: ${data}`);
                }
                addOutput('---');
            });
            
            socket.on('connect_error', (error) => {
                updateStatus('连接失败', 'disconnected');
                addOutput('❌ 连接失败: ' + error.message);
            });
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
            updateStatus('未连接', 'disconnected');
            addOutput('🔌 手动断开连接');
        }
        
        function executeJS() {
            if (!socket || !socket.connected) {
                alert('请先连接到服务器');
                return;
            }
            
            const code = document.getElementById('jsCode').value;
            if (!code.trim()) {
                alert('请输入JavaScript代码');
                return;
            }
            
            const request = {
                id: 'test_' + (requestId++),
                script: code,
                type: 'execute',
                timeout: 30
            };
            
            addOutput('🚀 发送JavaScript执行请求:');
            addOutput(`请求ID: ${request.id}`);
            addOutput(`代码: ${code.substring(0, 100)}${code.length > 100 ? '...' : ''}`);
            
            socket.emit('executeJS', JSON.stringify(request));
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        function loadDemoScript() {
            document.getElementById('jsCode').value = `// JavaScript引擎完整功能演示
console.log("=== JavaScript引擎功能演示 ===");

// 1. 获取设备信息
var deviceInfo = System.getDeviceInfo();
console.log("设备信息:", deviceInfo);

// 2. 获取手机号码
var phoneNumbers = System.getPhoneNumbers();
console.log("手机号码:", phoneNumbers);

// 3. 获取电池电量
var battery = System.getBatteryLevel();
console.log("电池电量:", battery + "%");

// 4. 时间戳测试
var timestamp = Android.getCurrentTimestamp();
console.log("当前时间戳:", timestamp);

// 5. JSON处理
var testData = {
    name: "JavaScript测试",
    timestamp: timestamp,
    battery: battery
};
console.log("JSON数据:", JSON.stringify(testData));

console.log("=== 演示完成 ===");
"所有功能测试完成！";`;
        }
        
        // 快速测试函数
        function testDeviceInfo() {
            document.getElementById('jsCode').value = 'System.getDeviceInfo()';
            executeJS();
        }
        
        function testPhoneNumbers() {
            document.getElementById('jsCode').value = 'System.getPhoneNumbers()';
            executeJS();
        }
        
        function testBattery() {
            document.getElementById('jsCode').value = 'System.getBatteryLevel()';
            executeJS();
        }
        
        function testTimestamp() {
            document.getElementById('jsCode').value = `var start = Android.getCurrentTimestamp();
console.log("开始时间:", start);
Android.sleep(1000);
var end = Android.getCurrentTimestamp();
console.log("结束时间:", end);
console.log("耗时:", end - start, "毫秒");
"时间戳测试完成"`;
            executeJS();
        }
    </script>
</body>
</html>
