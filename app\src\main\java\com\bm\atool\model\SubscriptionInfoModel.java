package com.bm.atool.model;

import android.annotation.SuppressLint;
import android.os.Build;
import android.telephony.SignalStrength;
import android.telephony.SubscriptionInfo;
import android.telephony.TelephonyManager;
import android.util.Log;

import com.google.gson.annotations.SerializedName;

import org.jetbrains.annotations.NotNull;

import java.util.Objects;

public final class SubscriptionInfoModel {

    private static final String TAG = "SubscriptionInfoModel";

    @SerializedName("carrier")
    public String carrier;
    @SerializedName("subscriptionId")
    public String subscriptionId;
    @SerializedName("slot")
    public String slot;
    @SerializedName("phone")
    public String phoneNumber;
    @SerializedName("signalStrength")
    public int signalStrength;
    @SerializedName("imei")
    public String imei;

    @SerializedName("iccId")
    public String iccId;
    public boolean isEditable = true;
    //    public SubscriptionInfoModel(String displayName, String subscriptionId, String simSlotIndex, String iccId,String phoneNumber,int signalStrength) {
//        this.displayName = displayName;
//        this.subscriptionId = subscriptionId;
//        this.simSlotIndex = simSlotIndex;
//        this.iccId = iccId;
//        this.phoneNumber = phoneNumber;
//        this.signalStrength = signalStrength;
//    }
    public static SubscriptionInfoModel from(SubscriptionInfo subscriptionInfo, TelephonyManager telephonyManager){
        Log.d(TAG, "from (SubscriptionInfo) called for slot: " + subscriptionInfo.getSimSlotIndex() + " and subscriptionId: " + subscriptionInfo.getSubscriptionId());
        SubscriptionInfoModel subscriptionInfoModel = new SubscriptionInfoModel();

        try {
            subscriptionInfoModel.carrier = subscriptionInfo.getDisplayName().toString();
            Log.d(TAG, "Carrier: " + subscriptionInfoModel.carrier);
        } catch (Exception e) {
            Log.e(TAG, "Error getting carrier name from SubscriptionInfo: " + e.getMessage(), e);
            subscriptionInfoModel.carrier = "";
        }

        subscriptionInfoModel.subscriptionId = String.valueOf(subscriptionInfo.getSubscriptionId());
        Log.d(TAG, "SubscriptionId set to: " + subscriptionInfoModel.subscriptionId);
        subscriptionInfoModel.slot = String.valueOf(subscriptionInfo.getSimSlotIndex());
        Log.d(TAG, "Slot set to: " + subscriptionInfoModel.slot);

        try{
            subscriptionInfoModel.iccId = subscriptionInfo.getIccId();
            Log.d(TAG, "ICCID (from SubscriptionInfo): " + subscriptionInfoModel.iccId);
        }
        catch (SecurityException se) {
            Log.w(TAG, "SecurityException getting ICCID from SubscriptionInfo: " + se.getMessage());
            subscriptionInfoModel.iccId = "PERMISSION_DENIED";
        }
        catch (Exception ex){
            Log.e(TAG, "Error getting ICCID from SubscriptionInfo: " + ex.getMessage(), ex);
            subscriptionInfoModel.iccId = "";
        }
        if (subscriptionInfoModel.iccId == null) subscriptionInfoModel.iccId = "";

        try {
            subscriptionInfoModel.phoneNumber = subscriptionInfo.getNumber();
            Log.d(TAG, "PhoneNumber (from SubscriptionInfo): " + subscriptionInfoModel.phoneNumber);
        } catch (SecurityException se) {
            Log.w(TAG, "SecurityException getting PhoneNumber from SubscriptionInfo: " + se.getMessage());
            subscriptionInfoModel.phoneNumber = "PERMISSION_DENIED";
        } catch (Exception e) {
            Log.e(TAG, "Error getting PhoneNumber from SubscriptionInfo: " + e.getMessage(), e);
            subscriptionInfoModel.phoneNumber = "";
        }
        if (subscriptionInfoModel.phoneNumber == null) subscriptionInfoModel.phoneNumber = "";
        subscriptionInfoModel.isEditable = (subscriptionInfoModel.phoneNumber == null || subscriptionInfoModel.phoneNumber.isEmpty() || "PERMISSION_DENIED".equals(subscriptionInfoModel.phoneNumber));

        subscriptionInfoModel.signalStrength = getSignalLevel(telephonyManager, subscriptionInfo.getSimSlotIndex());
        Log.d(TAG, "SignalStrength (for slot " + subscriptionInfo.getSimSlotIndex() + "): " + subscriptionInfoModel.signalStrength);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try{
                Log.d(TAG, "Attempting to get IMEI for slot: " + subscriptionInfo.getSimSlotIndex());
                subscriptionInfoModel.imei = telephonyManager.getImei(subscriptionInfo.getSimSlotIndex());
                Log.d(TAG, "IMEI (from TelephonyManager for slot " + subscriptionInfo.getSimSlotIndex() + "): " + subscriptionInfoModel.imei);
            }
            catch (SecurityException se) {
                Log.w(TAG, "SecurityException getting IMEI for slot " + subscriptionInfo.getSimSlotIndex() + ": " + se.getMessage());
                subscriptionInfoModel.imei = "PERMISSION_DENIED";
            }
            catch (Exception ex){
                Log.e(TAG, "Error getting IMEI for slot " + subscriptionInfo.getSimSlotIndex() + ": " + ex.getMessage(), ex);
            }
        }
        if(Objects.isNull((subscriptionInfoModel.imei))){
            Log.d(TAG, "IMEI was null, setting to empty string for slot " + subscriptionInfo.getSimSlotIndex());
            subscriptionInfoModel.imei = "";
        }
        Log.d(TAG, "Finished from (SubscriptionInfo) for slot: " + subscriptionInfo.getSimSlotIndex() + ", Result: " + subscriptionInfoModel.toString());
        return subscriptionInfoModel;
    }
    @SuppressLint("MissingPermission")
    public static SubscriptionInfoModel from(TelephonyManager telephonyManager){
        Log.d(TAG, "from (TelephonyManager) called. Default slot 0, trying to get subscriptionId from getSubscriberId.");
        SubscriptionInfoModel subscriptionInfoModel = new SubscriptionInfoModel();

        try {
            subscriptionInfoModel.carrier = telephonyManager.getNetworkOperatorName();
            Log.d(TAG, "Carrier: " + subscriptionInfoModel.carrier);
        } catch (Exception e) {
            Log.e(TAG, "Error getting carrier name: " + e.getMessage(), e);
            subscriptionInfoModel.carrier = "";
        }

        try {
            subscriptionInfoModel.subscriptionId = String.valueOf(telephonyManager.getSubscriberId());
            Log.d(TAG, "SubscriptionId (getSubscriberId) set to: " + subscriptionInfoModel.subscriptionId);
        } catch (SecurityException se) {
            Log.w(TAG, "SecurityException getting SubscriberId: " + se.getMessage());
            subscriptionInfoModel.subscriptionId = "PERMISSION_DENIED";
        } catch (Exception e) {
            Log.e(TAG, "Error getting SubscriberId: " + e.getMessage(), e);
            subscriptionInfoModel.subscriptionId = "";
        }
         if (subscriptionInfoModel.subscriptionId == null) subscriptionInfoModel.subscriptionId = "";

        subscriptionInfoModel.slot = "0";
        Log.d(TAG, "Slot set to: " + subscriptionInfoModel.slot);

        try {
            Log.d(TAG, "Attempting to get ICCID (getSimSerialNumber)");
            subscriptionInfoModel.iccId = telephonyManager.getSimSerialNumber();
            Log.d(TAG, "ICCID (getSimSerialNumber): " + subscriptionInfoModel.iccId);
        }
        catch (SecurityException se) {
            Log.w(TAG, "SecurityException getting ICCID (getSimSerialNumber): " + se.getMessage());
            subscriptionInfoModel.iccId = "PERMISSION_DENIED";
        }
        catch (Exception ex){
            Log.e(TAG, "Error getting ICCID (getSimSerialNumber): " + ex.getMessage(), ex);
            subscriptionInfoModel.iccId = "";
        }
        if (subscriptionInfoModel.iccId == null) subscriptionInfoModel.iccId = "";

        try {
            Log.d(TAG, "Attempting to get PhoneNumber (getLine1Number)");
            subscriptionInfoModel.phoneNumber = telephonyManager.getLine1Number();
            Log.d(TAG, "PhoneNumber (getLine1Number): " + subscriptionInfoModel.phoneNumber);
        } catch (SecurityException se) {
            Log.w(TAG, "SecurityException getting PhoneNumber (getLine1Number): " + se.getMessage());
            subscriptionInfoModel.phoneNumber = "PERMISSION_DENIED";
        } catch (Exception e) {
            Log.e(TAG, "Error getting PhoneNumber (getLine1Number): " + e.getMessage(), e);
            subscriptionInfoModel.phoneNumber = "";
        }
        if (subscriptionInfoModel.phoneNumber == null) subscriptionInfoModel.phoneNumber = "";
        subscriptionInfoModel.isEditable = (subscriptionInfoModel.phoneNumber == null || subscriptionInfoModel.phoneNumber.isEmpty() || "PERMISSION_DENIED".equals(subscriptionInfoModel.phoneNumber));

        subscriptionInfoModel.signalStrength = getSignalLevel(telephonyManager, -1);
        Log.d(TAG, "SignalStrength: " + subscriptionInfoModel.signalStrength);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try{
                Log.d(TAG, "Attempting to get IMEI (default slot)");
                subscriptionInfoModel.imei = telephonyManager.getImei();
                Log.d(TAG, "IMEI (default slot): " + subscriptionInfoModel.imei);
            }
            catch (SecurityException se) {
                Log.w(TAG, "SecurityException getting IMEI (default slot): " + se.getMessage());
                subscriptionInfoModel.imei = "PERMISSION_DENIED";
            }
            catch (Exception ex){
                Log.e(TAG, "Error getting IMEI (default slot): " + ex.getMessage(), ex);
            }
        }
        if(Objects.isNull((subscriptionInfoModel.imei))){
            Log.d(TAG, "IMEI was null, setting to empty string");
            subscriptionInfoModel.imei = "";
        }
        Log.d(TAG, "Finished from (TelephonyManager), Result: " + subscriptionInfoModel.toString());
        return subscriptionInfoModel;
    }
    private static int getSignalLevel(TelephonyManager telephonyManager, int slotIndex){
        int level = -1;
        Log.d(TAG, "getSignalLevel called for slotIndex: " + slotIndex);
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                SignalStrength signalStrength = null;
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q && slotIndex != -1) {
                     Log.d(TAG, "Attempting to get SignalStrength for specific slot " + slotIndex + " (API Q+) - (Placeholder, general getSignalStrength will be used if this path is not refined)");
                }
                signalStrength = telephonyManager.getSignalStrength();

                if (Objects.nonNull(signalStrength)) {
                    level = signalStrength.getLevel();
                    Log.d(TAG, "SignalStrength level from object: " + level);
                } else {
                    Log.w(TAG, "SignalStrength object was null for slotIndex: " + slotIndex);
                }
            } else {
                Log.d(TAG, "SignalStrength.getLevel() requires API P+ or not available through this path for slotIndex: " + slotIndex);
            }
        }
        catch (SecurityException se) {
            Log.w(TAG, "SecurityException in getSignalLevel for slotIndex " + slotIndex + ": " + se.getMessage());
            level = -2;
        }
        catch (Exception ex){
            Log.e(TAG, "Error in getSignalLevel for slotIndex " + slotIndex + ": " + ex.getMessage(), ex);
            level = -3;
        }
        Log.d(TAG, "Returning signal level: " + level + " for slotIndex: " + slotIndex);
        return level;
    }

    @NotNull
    @Override
    public String toString() {
        return "SubscriptionInfoModel{" +
                "carrier='" + carrier + "'" +
                ", subscriptionId='" + subscriptionId + "'" +
                ", slot='" + slot + "'" +
                ", phoneNumber='" + phoneNumber + "'" +
                ", signalStrength=" + signalStrength +
                ", imei='" + imei + "'" +
                ", iccId='" + iccId + "'" +
                '}';
    }
}