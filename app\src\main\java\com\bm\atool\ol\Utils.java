//package com.bm.atool.ol;
//
//import android.app.Activity;
//import android.app.ActivityManager;
//import android.content.Context;
//import android.content.Intent;
//import android.net.Uri;
//import android.os.Build;
//import android.provider.Settings;
//import android.text.TextUtils;
//import android.util.Log;
//import android.widget.Toast;
//
//import java.lang.reflect.Method;
//import java.util.*;
//
///**
// * @功能: 工具类
// * @User Lmy
// * @Creat 4/16/21 8:33 AM
// * @Compony 永远相信美好的事情即将发生
// */
//public class Utils {
//    public static Utils INSTANCE = new Utils();
//    public static Integer REQUEST_FLOAT_CODE=1001;
//    /**
//     * 跳转到设置页面申请打开无障碍辅助功能
//     */
//
////    /**
////     * 判断Service是否开启
////     *
////     */
////    public Boolean isServiceRunning(Context context, String ServiceName) {
////        if (TextUtils.isEmpty(ServiceName)) {
////            return false;
////        }
////        ActivityManager myManager = (ActivityManager)context.getSystemService(Context.ACTIVITY_SERVICE);
////        ArrayList<ActivityManager.RunningServiceInfo> runningServices =
////                (ArrayList<ActivityManager.RunningServiceInfo>)myManager.getRunningServices(1000);
////        for (ActivityManager.RunningServiceInfo service: runningServices) {
////            if (service.service.getClassName().equals(ServiceName)) {
////                return true;
////            }
////        }
////        return false;
////    }
//
//    /**
//     * 判断悬浮窗权限权限
//     */
////
////    private void checkSuspendedWindowPermission(Activity context) {
////        if (commonROMPermissionCheck(context)) {
////            block()
////        } else {
////            Toast.makeText(context, "请开启悬浮窗权限", Toast.LENGTH_SHORT).show()
////            context.startActivityForResult(Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
////                data = Uri.parse("package:${context.packageName}")
////            }, REQUEST_FLOAT_CODE)
////        }
////    }
//
//    /**
//     * 检查无障碍服务权限是否开启
//     */
////    public void checkAccessibilityPermission(Context context) {
////        if (isServiceRunning(context, MyAccessibilityService.class.getCanonicalName())) {
////            return;
////        }
////        accessibilityToSettingPage(context);
////    }
//
//}