<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="1dp"
    android:layout_marginBottom="3dp">

    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="1dp">
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_marginLeft="0dp"
            android:src="@mipmap/ic_launcher"
            app:srcCompat="@mipmap/ic_launcher_round" />
        <TextView
            android:id="@+id/txtFrom"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text=""
            android:paddingLeft="6dp"
            android:layout_centerVertical="true"
            android:textColor="@android:color/black" />
        <TextView
            android:id="@+id/txtTime"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text=""
            android:layout_centerVertical="true"
            android:textColor="@android:color/black" />
        <TextView
            android:id="@+id/txtStatus"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:text=""
            android:layout_centerVertical="true"
            android:textColor="@android:color/black" />
    </LinearLayout>

    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="1dp">

        <TextView
            android:id="@+id/txtContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:layout_marginLeft="48dp"
            android:layout_centerVertical="true"
            android:textColor="@android:color/black" />
    </LinearLayout>
</LinearLayout>