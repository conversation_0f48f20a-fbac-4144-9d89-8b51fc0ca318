package com.bm.atool.js;

import android.content.Context;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.*;

/**
 * JavaScript引擎集成测试
 * 测试基于 taoweiji/quickjs-android 的新实现
 */
@RunWith(AndroidJUnit4.class)
public class JavaScriptEngineInstrumentedTest {
    
    private Context context;
    private JavaScriptEngine jsEngine;
    
    @Before
    public void setUp() {
        context = ApplicationProvider.getApplicationContext();
        jsEngine = new JavaScriptEngine(context, null);
    }
    
    @After
    public void tearDown() {
        if (jsEngine != null) {
            jsEngine.close();
        }
    }
    
    @Test
    public void testEngineInitialization() {
        assertTrue("JavaScript引擎应该可用", jsEngine.isAvailable());
    }
    
    @Test
    public void testBasicScriptExecution() {
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("1 + 1");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("计算结果应该正确", "2", result.result);
        assertNull("不应该有错误", result.error);
    }
    
    @Test
    public void testStringScriptExecution() {
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("'Hello ' + 'World'");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("字符串拼接结果应该正确", "Hello World", result.result);
    }
    
    @Test
    public void testJSONScriptExecution() {
        String script = "JSON.stringify({name: 'test', value: 123})";
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript(script);
        
        assertTrue("脚本执行应该成功", result.success);
        assertTrue("结果应该包含JSON", result.result.toString().contains("\"name\":\"test\""));
        assertTrue("结果应该包含JSON", result.result.toString().contains("\"value\":123"));
    }
    
    @Test
    public void testErrorHandling() {
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("invalidFunction()");
        
        assertFalse("脚本执行应该失败", result.success);
        assertNotNull("应该有错误信息", result.error);
    }
    
    @Test
    public void testSyntaxError() {
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("var a = ;");
        
        assertFalse("语法错误脚本应该失败", result.success);
        assertNotNull("应该有错误信息", result.error);
    }
    
    @Test
    public void testFunctionExecution() {
        // 先定义函数
        JavaScriptEngine.ScriptResult defineResult = jsEngine.executeScript(
            "function add(a, b) { return a + b; }"
        );
        assertTrue("函数定义应该成功", defineResult.success);
        
        // 执行函数
        JavaScriptEngine.ScriptResult result = jsEngine.executeFunction("add", 5, 3);
        assertTrue("函数执行应该成功", result.success);
        assertEquals("函数结果应该正确", "8", result.result);
    }
    
    @Test
    public void testAndroidBridgeBinding() {
        // 测试Android对象是否可用
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof Android");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("Android对象应该存在", "object", result.result);
    }
    
    @Test
    public void testSMSBinding() {
        // 测试SMS对象是否可用
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof SMS");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("SMS对象应该存在", "object", result.result);
        
        // 测试SMS.send函数
        JavaScriptEngine.ScriptResult sendResult = jsEngine.executeScript("typeof SMS.send");
        assertTrue("SMS.send应该存在", sendResult.success);
        assertEquals("SMS.send应该是函数", "function", sendResult.result);
    }
    
    @Test
    public void testUSSDBinding() {
        // 测试USSD对象是否可用
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof USSD");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("USSD对象应该存在", "object", result.result);
        
        // 测试USSD.execute函数
        JavaScriptEngine.ScriptResult executeResult = jsEngine.executeScript("typeof USSD.execute");
        assertTrue("USSD.execute应该存在", executeResult.success);
        assertEquals("USSD.execute应该是函数", "function", executeResult.result);
    }
    
    @Test
    public void testSystemBinding() {
        // 测试System对象是否可用
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof System");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("System对象应该存在", "object", result.result);
        
        // 测试System.getDeviceInfo函数
        JavaScriptEngine.ScriptResult deviceInfoResult = jsEngine.executeScript("typeof System.getDeviceInfo");
        assertTrue("System.getDeviceInfo应该存在", deviceInfoResult.success);
        assertEquals("System.getDeviceInfo应该是函数", "function", deviceInfoResult.result);
    }
    
    @Test
    public void testConsoleBinding() {
        // 测试console对象是否可用
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof console");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("console对象应该存在", "object", result.result);
        
        // 测试console.log函数
        JavaScriptEngine.ScriptResult logResult = jsEngine.executeScript("typeof console.log");
        assertTrue("console.log应该存在", logResult.success);
        assertEquals("console.log应该是函数", "function", logResult.result);
    }
    
    @Test
    public void testConsoleLog() {
        // 测试console.log是否可用
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript("console.log('test'); 'success'");
        
        assertTrue("脚本执行应该成功", result.success);
        assertEquals("应该返回success", "success", result.result);
    }
    
    @Test
    public void testScriptTimeout() {
        // 测试脚本超时
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript(
            "var start = Date.now(); while(Date.now() - start < 2000) { /* 2秒循环 */ }", 
            1 // 1秒超时
        );
        
        assertFalse("超时脚本应该失败", result.success);
        assertNotNull("应该有错误信息", result.error);
    }
    
    @Test
    public void testEngineReset() {
        // 定义一个变量
        JavaScriptEngine.ScriptResult defineResult = jsEngine.executeScript("var testVar = 'defined'");
        assertTrue("变量定义应该成功", defineResult.success);
        
        // 验证变量存在
        JavaScriptEngine.ScriptResult checkResult = jsEngine.executeScript("testVar");
        assertTrue("变量检查应该成功", checkResult.success);
        assertEquals("变量值应该正确", "defined", checkResult.result);
        
        // 重置引擎
        jsEngine.reset();
        
        // 验证变量不存在
        JavaScriptEngine.ScriptResult afterResetResult = jsEngine.executeScript("typeof testVar");
        assertTrue("脚本执行应该成功", afterResetResult.success);
        assertEquals("变量应该未定义", "undefined", afterResetResult.result);
    }
    
    @Test
    public void testComplexScript() {
        String complexScript = 
            "function fibonacci(n) {" +
            "  if (n <= 1) return n;" +
            "  return fibonacci(n - 1) + fibonacci(n - 2);" +
            "}" +
            "fibonacci(10)";
        
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript(complexScript);
        
        assertTrue("复杂脚本应该成功", result.success);
        assertEquals("斐波那契数列结果应该正确", "55", result.result);
    }
    
    @Test
    public void testQuickJSSpecificFeatures() {
        // 测试 QuickJS 特有功能
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript(
            "var obj = {a: 1, b: 2}; Object.keys(obj).length"
        );
        
        assertTrue("QuickJS对象操作应该成功", result.success);
        assertEquals("对象键数量应该正确", "2", result.result);
    }
    
    @Test
    public void testMemoryManagement() {
        // 测试内存管理 - 创建大量对象
        StringBuilder script = new StringBuilder();
        script.append("var objects = []; ");
        script.append("for(var i = 0; i < 1000; i++) { ");
        script.append("  objects.push({id: i, data: 'test' + i}); ");
        script.append("} ");
        script.append("objects.length");
        
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript(script.toString());
        
        assertTrue("大量对象创建应该成功", result.success);
        assertEquals("对象数量应该正确", "1000", result.result);
    }
    
    @Test
    public void testAndroidBridgeIntegration() {
        // 测试Android桥接集成
        JavaScriptEngine.ScriptResult result = jsEngine.executeScript(
            "Android.getCurrentTimestamp()"
        );
        
        assertTrue("Android桥接调用应该成功", result.success);
        assertNotNull("时间戳不应该为空", result.result);
        assertTrue("时间戳应该是数字", result.result.toString().matches("\\d+"));
    }
}
