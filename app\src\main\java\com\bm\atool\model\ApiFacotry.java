package com.bm.atool.model;

import android.util.Log;

import com.bm.atool.Env;
import com.bm.atool.Sys;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.ConnectionPool;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class ApiFacotry {
    private final static String TAG = ApiFacotry.class.getSimpleName();
    private static SmsApi smsApi;

    public  static SmsApi getSmsApi(){
        if(Objects.isNull(smsApi)){
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new ServiceInterceptor())
                    .connectionPool(new ConnectionPool(10,5, TimeUnit.SECONDS))
                    .build();
            Retrofit retrofit = new Retrofit.Builder()
                    .baseUrl(Env.API_HOME)
                    .client(client)
                    .addConverterFactory(GsonConverterFactory.create())
//                .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
                    .build();
            smsApi = retrofit.create(SmsApi.class);
        }
        return smsApi;
    }
    public static void  init(){
    }
    private static class ServiceInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            String token = Sys.getToken();
            Log.d(TAG, "TOKEN: " + token);
            if(Objects.isNull(request.header("'x-auth-token")) && Objects.nonNull(token)){
                Log.d(TAG, "add TOKEN: " + token);
                request = request.newBuilder().addHeader("x-auth-token", token).build();
            }
            return chain.proceed(request);
        }

    }
}
