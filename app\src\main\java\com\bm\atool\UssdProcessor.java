package com.bm.atool;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.telephony.SubscriptionInfo;
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.util.Log;
import com.bm.atool.utils.PhoneUtils;

import androidx.core.app.ActivityCompat;

import com.bm.atool.model.UssdRequest;
import com.bm.atool.model.UssdResponse;
import com.google.gson.Gson;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicBoolean;

import io.socket.client.Socket;

public class UssdProcessor {
    private static final String TAG = "UssdProcessor";
    private static Map<String, Timer> pendingRequests = new HashMap<>();
    private static AtomicBoolean isProcessingUssd = new AtomicBoolean(false);

    public static void processUssdRequest(UssdRequest request, Socket socket) {
        Context context = Sys.app;

        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
            sendErrorResponse(request.id, "CALL_PHONE权限未授予", socket);
            return;
        }

        if (isProcessingUssd.getAndSet(true)) {
            sendErrorResponse(request.id, "另一个USSD请求正在处理中", socket);
            isProcessingUssd.set(false);
            return;
        }

        try {
            Timer timer = new Timer();
            pendingRequests.put(request.id, timer);
            
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    if (pendingRequests.containsKey(request.id)) {
                        pendingRequests.remove(request.id);
                        sendErrorResponse(request.id, "USSD请求超时", socket);
                        isProcessingUssd.set(false);
                    }
                }
            }, request.timeout * 1000);

            String ussdCode = request.ussdCode;
            if (!ussdCode.startsWith("tel:")) {
                ussdCode = "tel:" + ussdCode;
            }
            
            Uri uri = Uri.parse(ussdCode + Uri.encode("#"));

            Intent intent = new Intent(Intent.ACTION_CALL, uri);
            
            // 首先尝试通过手机号选择SIM卡
            boolean simSelected = false;
            if (request.targetPhone != null && !request.targetPhone.isEmpty()) {
                Log.d(TAG, "尝试使用指定手机号执行USSD: " + request.targetPhone);
                int subId = getSubscriptionIdForPhoneNumber(context, request.targetPhone);
                if (subId != -1) {
                    Log.d(TAG, "找到匹配的SIM卡，subscriptionId: " + subId);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        intent.putExtra("android.telecom.extra.PHONE_ACCOUNT_HANDLE", 
                                PhoneUtils.getPhoneAccountHandleFromSubscriptionId(context, subId));
                        simSelected = true;
                    }
                } else {
                    Log.w(TAG, "未找到与目标手机号码匹配的SIM卡: " + request.targetPhone);
                }
            }
            
            // 如果通过手机号没有选择到SIM卡，且提供了subscriptionId，则使用subscriptionId
            if (!simSelected && request.subscriptionId != null && !request.subscriptionId.isEmpty()) {
                int subId = Integer.parseInt(request.subscriptionId);
                Log.d(TAG, "使用subscriptionId执行USSD: " + subId);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    intent.putExtra("android.telecom.extra.PHONE_ACCOUNT_HANDLE", 
                            PhoneUtils.getPhoneAccountHandleFromSubscriptionId(context, subId));
                }
            }

            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            context.startActivity(intent);

            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (pendingRequests.containsKey(request.id)) {
                    TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                    String ussdMessage = "USSD响应已接收";

                    UssdResponse response = new UssdResponse();
                    response.id = request.id;
                    response.message = ussdMessage;
                    response.success = true;

                    if (socket != null && socket.connected()) {
                        socket.emit("u", new Gson().toJson(response));
                    }

                    pendingRequests.get(request.id).cancel();
                    pendingRequests.remove(request.id);
                    isProcessingUssd.set(false);
                }
            }, 5000);
            
        } catch (Exception e) {
            Log.e(TAG, "处理USSD请求时出错: " + e.getMessage());
            sendErrorResponse(request.id, "错误: " + e.getMessage(), socket);
            isProcessingUssd.set(false);
        }
    }

    private static int getSubscriptionIdForPhoneNumber(Context context, String phoneNumber) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
            Log.d(TAG, "当前系统版本不支持根据手机号确定SIM卡");
            return -1;
        }

        try {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "无法获取SIM卡信息：缺少READ_PHONE_STATE权限");
                return -1;
            }

            SubscriptionManager subscriptionManager = SubscriptionManager.from(context);
            List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();

            if (subscriptionInfos != null) {
                for (SubscriptionInfo info : subscriptionInfos) {
                    String number = info.getNumber();
                    if (number != null && !number.isEmpty()) {
                        // 规范化电话号码进行比较
                        String normalizedConfigNumber = normalizePhoneNumber(number);
                        String normalizedTargetNumber = normalizePhoneNumber(phoneNumber);
                        
                        Log.d(TAG, "比较SIM卡号码: " + normalizedConfigNumber + " 与目标号码: " + normalizedTargetNumber);
                        
                        if (normalizedConfigNumber.equals(normalizedTargetNumber) || 
                            normalizedConfigNumber.endsWith(normalizedTargetNumber) || 
                            normalizedTargetNumber.endsWith(normalizedConfigNumber)) {
                            return info.getSubscriptionId();
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取指定号码SIM卡时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return -1;
    }
    
    private static String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return "";
        }
        // 只保留数字，去掉所有非数字字符
        return phoneNumber.replaceAll("[^0-9]", "");
    }

    private static void sendErrorResponse(String id, String errorMessage, Socket socket) {
        UssdResponse response = new UssdResponse();
        response.id = id;
        response.success = false;
        response.error = errorMessage;

        if (socket != null && socket.connected()) {
            socket.emit("u", new Gson().toJson(response));
        }
    }
} 