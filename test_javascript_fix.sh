#!/bin/bash

# JavaScript引擎修复验证脚本
# 用于测试修复后的JavaScript引擎是否正常工作

echo "=========================================="
echo "JavaScript引擎修复验证脚本"
echo "=========================================="

# 检查项目结构
echo "1. 检查项目文件结构..."

if [ ! -f "app/src/main/java/com/bm/atool/js/AndroidBridge.java" ]; then
    echo "❌ AndroidBridge.java 文件不存在"
    exit 1
fi

if [ ! -f "app/src/main/java/com/bm/atool/js/JavaScriptEngine.java" ]; then
    echo "❌ JavaScriptEngine.java 文件不存在"
    exit 1
fi

echo "✅ 项目文件结构正常"

# 检查AndroidBridge是否实现了接口
echo "2. 检查AndroidBridge接口实现..."

if grep -q "interface AndroidBridgeInterface" app/src/main/java/com/bm/atool/js/AndroidBridge.java; then
    echo "✅ AndroidBridgeInterface 接口已定义"
else
    echo "❌ AndroidBridgeInterface 接口未找到"
    exit 1
fi

if grep -q "implements AndroidBridgeInterface" app/src/main/java/com/bm/atool/js/AndroidBridge.java; then
    echo "✅ AndroidBridge 已实现接口"
else
    echo "❌ AndroidBridge 未实现接口"
    exit 1
fi

# 检查JavaScriptEngine是否使用接口绑定
echo "3. 检查JavaScriptEngine接口绑定..."

if grep -q "AndroidBridgeInterface.class" app/src/main/java/com/bm/atool/js/JavaScriptEngine.java; then
    echo "✅ JavaScriptEngine 使用接口绑定"
else
    echo "❌ JavaScriptEngine 未使用接口绑定"
    exit 1
fi

# 检查测试文件是否存在
echo "4. 检查测试文件..."

if [ -f "app/src/test/java/com/bm/atool/js/JavaScriptEngineTest.java" ]; then
    echo "✅ 单元测试文件已创建"
else
    echo "⚠️  单元测试文件不存在"
fi

if [ -f "app/src/androidTest/java/com/bm/atool/js/JavaScriptEngineIntegrationTest.java" ]; then
    echo "✅ 集成测试文件已创建"
else
    echo "⚠️  集成测试文件不存在"
fi

if [ -f "app/src/main/java/com/bm/atool/js/JavaScriptEngineTestHelper.java" ]; then
    echo "✅ 测试辅助类已创建"
else
    echo "⚠️  测试辅助类不存在"
fi

# 编译检查
echo "5. 执行编译检查..."

if command -v ./gradlew &> /dev/null; then
    echo "正在执行Gradle编译检查..."
    ./gradlew compileDebugJavaWithJavac --quiet
    if [ $? -eq 0 ]; then
        echo "✅ 编译检查通过"
    else
        echo "❌ 编译检查失败"
        exit 1
    fi
else
    echo "⚠️  Gradle wrapper 不可用，跳过编译检查"
fi

echo ""
echo "=========================================="
echo "修复验证完成"
echo "=========================================="
echo ""
echo "修复摘要："
echo "1. ✅ 创建了 AndroidBridgeInterface 接口"
echo "2. ✅ AndroidBridge 实现了接口"
echo "3. ✅ JavaScriptEngine 使用接口绑定"
echo "4. ✅ 添加了完整的测试套件"
echo "5. ✅ 修改了 DebugFragment 使用测试辅助类"
echo ""
echo "问题修复说明："
echo "原始错误: 'Only interfaces can be bound. Received: class com.bm.atool.js.AndroidBridge'"
echo "修复方案: 创建接口并让AndroidBridge实现该接口，然后在QuickJS绑定中使用接口"
echo ""
echo "测试建议："
echo "1. 运行单元测试: ./gradlew test"
echo "2. 运行集成测试: ./gradlew connectedAndroidTest"
echo "3. 在应用中打开Debug页面，查看JavaScript引擎初始化状态"
echo "4. 尝试执行JavaScript代码验证功能"
echo ""
echo "如果问题仍然存在，请检查："
echo "- QuickJS库版本是否兼容"
echo "- Android API级别是否支持"
echo "- 设备是否支持QuickJS"
