<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2015 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android">

  <CheckedTextView
      android:id="@+id/design_menu_item_text"
      android:layout_width="0dp"
      android:layout_height="match_parent"
      android:layout_weight="1"
      android:drawablePadding="@dimen/design_navigation_icon_padding"
      android:gravity="center_vertical|start"
      android:maxLines="1"
      android:textAppearance="@style/TextAppearance.AppCompat.Body2"/>

  <ViewStub
      android:id="@+id/design_menu_item_action_area_stub"
      android:layout_width="wrap_content"
      android:layout_height="match_parent"
      android:inflatedId="@+id/design_menu_item_action_area"
      android:layout="@layout/design_menu_item_action_area"/>

</merge>
