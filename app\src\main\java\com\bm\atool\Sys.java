package com.bm.atool;

import static android.content.Context.RECEIVER_EXPORTED;

import android.app.ActivityManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;

import com.bm.atool.events.EventSource;
import com.bm.atool.events.SMSEvent;
import com.bm.atool.model.PermissionModel;
import com.bm.atool.model.SubscriptionInfoModel;
import com.bm.atool.service.BaseServiceConnection;
import com.bm.atool.service.PlayMusicService;
import com.bm.atool.service.SocketService;
import com.bm.atool.service.SocketServiceConnection;
import com.bm.atool.service.WatchDogService;
import com.bm.atool.ui.FloatingWindow;
import com.bm.atool.utils.PermissionUtils;
import com.bm.atool.utils.PhoneUtils;

import java.util.ArrayList;
import java.util.Objects;

public class Sys {

    private final static String TAG = Sys.class.getSimpleName();
    public static final String ACTION_DAEMON_ENABLED = "com.bm.atool.daemon.enabled";
    public static final String ACTION_CANCEL_JOB_ALARM_SUB = "com.bm.atool.CANCEL_JOB_ALARM_SUB";
    public static final String ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED = "ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED";
    public static final String ACTION_SOCKET_PONG = "ACTION_SOCKET_PONG";
    public static App app;
    public static Boolean watchDogEnabled = false;
    private static FloatingWindow floatingWindow = null;
    public  static ArrayList<SMSEvent> lastSMSList = new ArrayList<>();
    public  static ArrayList<SubscriptionInfoModel> phones = null;
    public  static  ArrayList<SubscriptionInfoModel> initPhone(Context context){
        return phones = PhoneUtils.getPhones(context);
    }

    public static  ArrayList<PermissionModel> permissionModels = null;
    public  static  ArrayList<PermissionModel> initPermissions(Context context){
        return permissionModels = PermissionUtils.getAllPermissions(context);
    }

    public  static void onPermissionChangeEvent(ArrayList<PermissionModel> event){
        permissionModels = event;
        permissionsEventSource.fire(event);
        phones = PhoneUtils.getPhones(app);
        phoneEventSource.fire(phones);
    }

    public static void checkUpdatePermissions(){
        if(Sys.permissionModels.stream().anyMatch(p->{
            if(p.fullName != null && p.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                    && p.granted != PermissionUtils.isIgnoringBatteryOptimizations(app)){
                return true;
            }
            if(p.fullName != null && p.fullName.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
                    && p.granted != PermissionUtils.canDrawOverlays(app)){
                return true;
            }
            if(p.name != null && p.name.equals("Accessibility")
                    && p.granted != PermissionUtils.isAccessibilitySettingsOn()){
                return true;
            }
            return false;
        })){
            onPermissionChangeEvent(PermissionUtils.getAllPermissions(app));
        }
    }

    public static SubscriptionInfoModel getPhoneInfo(String subscriptionId){
        if(Objects.isNull(phones)){
            return  null;
        }
        if(phones.size() == 1){
            return phones.get(0);
        }
        for (SubscriptionInfoModel phone: phones){
            if(phone.subscriptionId == subscriptionId){
                return phone;
            }
        }
        return  null;
    }

    public  static void onSmsEvent(SMSEvent event){
        if(lastSMSList.contains(event)){
            smsEventSource.fire((event));
            return;
        }
        if(lastSMSList.size() > 4){
            lastSMSList.remove(0);
        }
        lastSMSList.add(event);
        smsEventSource.fire((event));
    }
    public  static void onPhoneCHangeEvent(ArrayList<SubscriptionInfoModel> event){
        phones = event;
        phoneEventSource.fire((event));
    }

    public static EventSource<SMSEvent> smsEventSource = new EventSource<>();

    public static EventSource<ArrayList<SubscriptionInfoModel>> phoneEventSource = new EventSource<>();
    public static EventSource<ArrayList<PermissionModel>> permissionsEventSource = new EventSource<>();
    public static EventSource<LoginEvent> loginEventSource = new EventSource<>();
    public static EventSource<Boolean> watchDogEventSource = new EventSource<>();

    public static String getToken(){
        SharedPreferences sp = app.getSharedPreferences("loginToken", 0);
        String token = sp.getString("token","");
        return token;
    }

    public static String getUserName(){
        SharedPreferences sp = app.getSharedPreferences("loginToken", 0);
        String username = sp.getString("username","");
        return username;
    }

    public static void updateLogin(String username, String token) {
        SharedPreferences sp = app.getSharedPreferences("loginToken", 0);
        SharedPreferences.Editor editor = sp.edit();
        editor.putString("token", token);
        editor.putString("username",username);
        editor.commit();
        loginEventSource.fire(new LoginEvent(username, token));
    }
    public static final class  LoginEvent{
        public String username;
        public String token;
        public  LoginEvent(String username, String token){
            this.username = username;
            this.token = token;
        }
    }



    public static boolean isServiceRunning(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }
    public static void startServiceSafely(Context context, Class<?> i) {
        if(isServiceRunning(context, i)){
            Log.e(TAG, "startServiceSafely(ingore): " + i.getSimpleName());
            return;
        }
        if(i.equals(SocketService.class)){
            context.startService(new Intent(context,i));
            return;
        }
        try {
            if (Build.VERSION.SDK_INT >= 26){
                context.startForegroundService(new Intent(context,i));
            }else {
                context.startService(new Intent(context,i));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static void startServiceMayBind(@NonNull final Context context,
                                           @NonNull final Class<? extends Service> serviceClass,
                                           @NonNull BaseServiceConnection connection) {

//        if(serviceClass.equals(SocketService.class)){
//            if(!connection.mConnectedState){
//                startServiceSafely(context, serviceClass);
////                final Intent intent = new Intent(context, serviceClass);
////                context.bindService(intent, connection, Context.BIND_AUTO_CREATE);
//            }
//            return;
//        }

        if (!connection.mConnectedState) {
            Log.e(TAG,"startServiceMayBind: " + serviceClass.getSimpleName());
//            Log.d("wsh-daemon", "启动并绑定服务 ："+serviceClass.getSimpleName());
            final Intent intent = new Intent(context, serviceClass);
            startServiceSafely(context, serviceClass);
            context.bindService(intent, connection, Context.BIND_AUTO_CREATE);
        }
        else{
            Log.e(TAG,"startServiceMayBind(ingore): " + serviceClass.getSimpleName());
        }
    }

    public static void stopServiceMayBind(@NonNull final Context context,
                                          @NonNull final Class<? extends Service> serviceClass,
                                          @NonNull BaseServiceConnection connection) {

        Log.e(TAG,"stopServiceMayBind: " + serviceClass.getSimpleName());
//        if(serviceClass.equals(SocketService.class)){
//            if(connection.mConnectedState){
//                context.unbindService(connection);
//            }
//        }
        safelyUnbindService(context,connection);
//        context.stopService(new Intent(context, serviceClass));
        stopService(context, serviceClass);
    }

    public static void stopService(@NonNull final Context context,@NonNull final Class<? extends Service> serviceClass) {

        Log.e(TAG,"stopService@Sys: " + serviceClass.getSimpleName());
        context.stopService(new Intent(context, serviceClass));
    }

    public static void safelyUnbindService(Context context, BaseServiceConnection mConnection){
        try{
            if (mConnection.mConnectedState) {
                context.unbindService(mConnection);
            }
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    public static Intent registerReceiver(Context context, BroadcastReceiver receiver, IntentFilter filter) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return context.registerReceiver(receiver, filter,RECEIVER_EXPORTED);
        }
        else{
            return context.registerReceiver(receiver, filter);
        }
    }

    public static final int MINIMAL_WAKE_UP_INTERVAL = 60 * 1000; // 最小时间为 1 分钟
    public static int getWakeUpInterval(int sWakeUpInterval) {
        return Math.max(sWakeUpInterval, MINIMAL_WAKE_UP_INTERVAL);
    }

    private static final String SHARED_UTILS = "watch_process";

    private static final String WATCH_DOG_ENABLED = "watch_dog_enabled"; // 是否开始了一次保活（做为保活的判断依据）

    // 多进程时，尽量少用静态、单例 此处不得已
//    public static Class<? extends AbsWorkService> mWorkServiceClass;

    public static void initWatchDogEnabled(){
        watchDogEnabled = Sys.app.getSharedPreferences(SHARED_UTILS,0).getBoolean(WATCH_DOG_ENABLED, false);
    }
    public static  void updateWatchDogEnabled(boolean enabled){
        if(Sys.watchDogEnabled != enabled) {
            Sys.watchDogEnabled = enabled;
            watchDogEventSource.fire(enabled);
        }
    }
    public static boolean getWatchDogEnabled(){
        return Sys.app.getSharedPreferences(SHARED_UTILS,0).getBoolean(WATCH_DOG_ENABLED, false);
    }

    public static void start(View view){
        Log.e(TAG, "sys.start entered");
        SocketServiceConnection socketServiceConnection = SocketServiceConnection.getInstance(Sys.app);
        if(socketServiceConnection.mConnectedState && SocketService.isServiceRunningAndConnected()){
            Log.d(TAG, "sys.start: SocketService already connected and verified, returning.");
            return;
        }
        synchronized (Sys.class) {
            if (Objects.isNull(floatingWindow)) {
                Log.d(TAG, "sys.start: Sys.floatingWindow is null, creating new instance.");
                if (Sys.app == null) {
                    Log.e(TAG, "sys.start: Sys.app is NULL before creating FloatingWindow!");
                    return;
                }
                boolean canDrawOverlays = PermissionUtils.canDrawOverlays(Sys.app);
                boolean isAccessibilityEnabled = PermissionUtils.isAccessibilitySettingsOn();
                Log.d(TAG, "sys.start: Permissions check before showing Sys.floatingWindow - canDrawOverlays: " + canDrawOverlays + ", isAccessibilityEnabled: " + isAccessibilityEnabled);
                floatingWindow = new FloatingWindow(Sys.app);
                Log.d(TAG, "sys.start: Calling Sys.floatingWindow.show()");
                floatingWindow.show();
            } else {
                Log.d(TAG, "sys.start: Sys.floatingWindow already exists, calling show() on existing instance.");
                boolean canDrawOverlays = PermissionUtils.canDrawOverlays(Sys.app);
                boolean isAccessibilityEnabled = PermissionUtils.isAccessibilitySettingsOn();
                Log.d(TAG, "sys.start: Permissions check before re-showing Sys.floatingWindow - canDrawOverlays: " + canDrawOverlays + ", isAccessibilityEnabled: " + isAccessibilityEnabled);
                floatingWindow.show(); // Ensure show is called even if instance exists
            }
        }
        watchDogEnabled = true;
        updateWatchDogEnabled(true);
        Sys.app.getSharedPreferences(SHARED_UTILS,0).edit().putBoolean(WATCH_DOG_ENABLED, watchDogEnabled).apply();
        sendEnableWatchDogEnabledBroadcast(view.getContext(),true);
        view.postDelayed(new Runnable() {
            @Override
            public void run() {
                socketServiceConnection.EnsureSocketService();
                Sys.startServiceSafely(Sys.app, WatchDogService.class);
                Sys.startServiceSafely(Sys.app, PlayMusicService.class);
            }
        },1000);
    }
    public static void stop(){
        Log.e(TAG, "sys.stop entered");

        synchronized (Sys.class) {
            if (Objects.nonNull(floatingWindow)) {
                Log.d(TAG, "sys.stop: Closing Sys.floatingWindow");
                floatingWindow.close();
                floatingWindow = null;
            } else {
                Log.d(TAG, "sys.stop: Sys.floatingWindow was already null.");
            }
        }
        watchDogEnabled = false;
        updateWatchDogEnabled(false);
        Sys.app.getSharedPreferences(SHARED_UTILS,0).edit().putBoolean(WATCH_DOG_ENABLED, watchDogEnabled).apply();
        SocketServiceConnection.getInstance(Sys.app).close();
        sendEnableWatchDogEnabledBroadcast(Sys.app, false);
    }

    public static void exit(){
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                System.exit(0);
            }
        },2000);
    }
    private static void sendEnableWatchDogEnabledBroadcast(Context context, Boolean enabled) {
        Log.d(TAG, "sendStartWorkBroadcast...");
        Intent intent = new Intent(Sys.ACTION_DAEMON_ENABLED);
        intent.putExtra(NotificationCompat.CATEGORY_STATUS, enabled);
        context.sendBroadcast(intent);
    }

    public static Boolean isLogin(){
        String token = getToken();
        return Objects.nonNull((token)) && token.length() > 1;
    }
}
