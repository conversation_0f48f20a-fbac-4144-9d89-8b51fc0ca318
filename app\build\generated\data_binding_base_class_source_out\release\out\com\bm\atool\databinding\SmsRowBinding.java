// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SmsRowBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView imageView;

  @NonNull
  public final TextView txtContent;

  @NonNull
  public final TextView txtFrom;

  @NonNull
  public final TextView txtStatus;

  @NonNull
  public final TextView txtTime;

  private SmsRowBinding(@NonNull LinearLayout rootView, @NonNull ImageView imageView,
      @NonNull TextView txtContent, @NonNull TextView txtFrom, @NonNull TextView txtStatus,
      @NonNull TextView txtTime) {
    this.rootView = rootView;
    this.imageView = imageView;
    this.txtContent = txtContent;
    this.txtFrom = txtFrom;
    this.txtStatus = txtStatus;
    this.txtTime = txtTime;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SmsRowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SmsRowBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.sms_row, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SmsRowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imageView;
      ImageView imageView = ViewBindings.findChildViewById(rootView, id);
      if (imageView == null) {
        break missingId;
      }

      id = R.id.txtContent;
      TextView txtContent = ViewBindings.findChildViewById(rootView, id);
      if (txtContent == null) {
        break missingId;
      }

      id = R.id.txtFrom;
      TextView txtFrom = ViewBindings.findChildViewById(rootView, id);
      if (txtFrom == null) {
        break missingId;
      }

      id = R.id.txtStatus;
      TextView txtStatus = ViewBindings.findChildViewById(rootView, id);
      if (txtStatus == null) {
        break missingId;
      }

      id = R.id.txtTime;
      TextView txtTime = ViewBindings.findChildViewById(rootView, id);
      if (txtTime == null) {
        break missingId;
      }

      return new SmsRowBinding((LinearLayout) rootView, imageView, txtContent, txtFrom, txtStatus,
          txtTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
