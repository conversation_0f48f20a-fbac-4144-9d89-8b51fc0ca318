package com.bm.atool.ui.adapters;

import android.provider.Settings;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bm.atool.utils.PermissionUtils;
import com.bm.atool.R;
import com.bm.atool.model.PermissionModel;
import com.bm.atool.ui.IPermissionGrant;
import com.bm.atool.ui.SettingsFragment;

import java.util.ArrayList;
import java.util.Objects;

public class PermissionsAdapter extends RecyclerView.Adapter<PermissionsAdapter.ViewHolder>{

    private ArrayList<PermissionModel> dataSet;
    SettingsFragment settingsFragment;

    public PermissionsAdapter(ArrayList<PermissionModel> dataSet,SettingsFragment settingsFragment) {
        this.dataSet = dataSet;
        this.settingsFragment = settingsFragment;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.setting_row, parent, false);
        return new PermissionsAdapter.ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        PermissionModel permissionModel = this.dataSet.get(position);

        holder.txtName.setText(permissionModel.name);
        holder.imgStatus.setVisibility(permissionModel.granted ? View.VISIBLE : View.INVISIBLE);

        boolean isSpecialPermission = false;
        if ("Accessibility".equals(permissionModel.name) ||
            (permissionModel.fullName != null && Settings.ACTION_MANAGE_OVERLAY_PERMISSION.equals(permissionModel.fullName)) ||
            (permissionModel.fullName != null && Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS.equals(permissionModel.fullName))) {
            isSpecialPermission = true;
        }

        if (isSpecialPermission && !permissionModel.granted) {
            holder.btnGoSetting.setVisibility(View.VISIBLE);
            holder.btnGoSetting.setTag(permissionModel);
            holder.btnGoSetting.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(v.getId() == R.id.btnGoSetting){
                        if(permissionModel.name.equals("Accessibility")){
                            PermissionUtils.accessibilityToSettingPage(settingsFragment.getActivity());
                        } else if (permissionModel.fullName != null) {
                            ((IPermissionGrant)settingsFragment.getActivity()).grant(permissionModel.fullName);
                        }
                    }
                }
            });
        } else {
            holder.btnGoSetting.setVisibility(View.INVISIBLE);
            holder.btnGoSetting.setOnClickListener(null);
        }
    }

    @Override
    public int getItemCount() {
        if(Objects.isNull(this.dataSet)){
            return 0;
        }
        return this.dataSet.size();
    }
    
    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView txtName;
        ImageView imgStatus;
        Button btnGoSetting;

        public ViewHolder(View view) {
            super(view);
            txtName = (TextView) view.findViewById(R.id.txtName);
            btnGoSetting = (Button) view.findViewById(R.id.btnGoSetting);
            imgStatus = (ImageView) view.findViewById(R.id.imgStatus);
        }
    }
}