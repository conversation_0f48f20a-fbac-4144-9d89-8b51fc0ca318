package com.bm.atool;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;

import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.google.android.material.tabs.TabLayout;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import androidx.test.uiautomator.UiDevice;
import androidx.test.uiautomator.UiSelector;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@RunWith(AndroidJUnit4.class)
public class StressTest {
    private static final String TAG = "StressTest";
    private Context context;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());
    private SimpleDateFormat folderFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());

    // 统计变量
    private int totalOnline = 0;
    private int totalOffline = 0;
    private int totalPong = 0;
    private int totalDisconnectRounds = 0;
    private int groupOnline = 0;
    private int groupOffline = 0;
    private int groupPong = 0;
    private int groupDisconnectRounds = 0;

    private int round = 0;
    private int group = 0;
    private static final int TOTAL_ROUNDS = 1000;
    private static final int GROUP_SIZE = 100;
    private static final long TEST_DURATION_MS = 5 * 60 * 1000; // 5分钟

    // 日志
    private File groupDir;
    private File groupStatFile;
    private FileWriter groupStatWriter;
    private File totalStatFile;
    private FileWriter totalStatWriter;

    // Socket监控
    private AtomicBoolean isSocketConnected = new AtomicBoolean(false);
    private long lastPongTime = 0;
    private BroadcastReceiver pongReceiver;

    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();

                // 自动点击授权弹窗
        UiDevice device = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
        String[] allowTexts = {"允许", "始终允许", "同意", "确定", "始终允许访问此设备上的照片、媒体内容和文件"};
        for (String text : allowTexts) {
            try {
                if (device.findObject(new UiSelector().text(text)).exists()) {
                    device.findObject(new UiSelector().text(text)).click();
                    Thread.sleep(1000); // 等待授权完成
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }


        
        // 注册PONG广播接收器
        pongReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (Sys.ACTION_SOCKET_PONG.equals(intent.getAction())) {
                    lastPongTime = System.currentTimeMillis();
                    isSocketConnected.set(true);
                    groupPong++;
                    totalPong++;
                }
            }
        };
        IntentFilter filter = new IntentFilter(Sys.ACTION_SOCKET_PONG);
        context.registerReceiver(pongReceiver, filter);
    }

    @After
    public void tearDown() {
        try {
            context.unregisterReceiver(pongReceiver);
        } catch (Exception e) {
            Log.e(TAG, "注销广播接收器失败", e);
        }
    }

    @Test
    public void runBatchStressTest() throws InterruptedException, IOException {
        String rootFolderName = "stress_test_batch_" + folderFormat.format(new Date());
        File rootDir = new File(context.getExternalFilesDir(null), rootFolderName);
        if (!rootDir.exists()) rootDir.mkdirs();
        totalStatFile = new File(rootDir, "total_stat.txt");
        totalStatWriter = new FileWriter(totalStatFile, true);
        writeTotalStat("批量压力测试开始，总轮数: " + TOTAL_ROUNDS);

        for (round = 1; round <= TOTAL_ROUNDS; round++) {
            if ((round - 1) % GROUP_SIZE == 0) {
                // 新分组
                group = (round - 1) / GROUP_SIZE + 1;
                groupDir = new File(rootDir, "group_" + group);
                if (!groupDir.exists()) groupDir.mkdirs();
                groupStatFile = new File(groupDir, "group_stat.txt");
                groupStatWriter = new FileWriter(groupStatFile, true);
                groupOnline = 0;
                groupOffline = 0;
                groupPong = 0;
                groupDisconnectRounds = 0;
                writeGroupStat("第" + group + "组开始，轮次: " + (round) + "~" + (Math.min(round + GROUP_SIZE - 1, TOTAL_ROUNDS)));
            }

            // 单轮日志
            String roundLogName = "round_" + round + ".log";
            File roundLogFile = new File(groupDir, roundLogName);
            FileWriter roundLogWriter = new FileWriter(roundLogFile, true);
            writeRoundLog(roundLogWriter, "第" + round + "轮测试开始");

            // 执行一轮测试
            boolean disconnected = !runSingleStressTest(roundLogWriter);
            if (disconnected) groupDisconnectRounds++;
            if (disconnected) totalDisconnectRounds++;

            writeRoundLog(roundLogWriter, "第" + round + "轮测试结束");
            roundLogWriter.close();

            // 每100轮统计一次
            if (round % GROUP_SIZE == 0 || round == TOTAL_ROUNDS) {
                writeGroupStat("第" + group + "组统计：");
                writeGroupStat("- 在线状态记录次数: " + groupOnline);
                writeGroupStat("- 离线状态记录次数: " + groupOffline);
                writeGroupStat("- 收到PONG心跳次数: " + groupPong);
                writeGroupStat("- 出现掉线的轮数: " + groupDisconnectRounds);
                groupStatWriter.close();
            }
        }
        // 总统计
        writeTotalStat("\n总统计:");
        writeTotalStat("- 在线状态记录次数: " + totalOnline);
        writeTotalStat("- 离线状态记录次数: " + totalOffline);
        writeTotalStat("- 收到PONG心跳次数: " + totalPong);
        writeTotalStat("- 出现掉线的轮数: " + totalDisconnectRounds);
        totalStatWriter.close();
    }

    /**
     * 单轮测试，返回true=全程在线，false=有掉线
     */
    private boolean runSingleStressTest(FileWriter logWriter) throws InterruptedException {
        isSocketConnected.set(false);
        lastPongTime = 0;
        int onlineCount = 0;
        int offlineCount = 0;
        int pongCount = 0;
        boolean wasConnected = false;
        boolean disconnected = false;
        long startTime = System.currentTimeMillis();
        long lastLogTime = 0;

        // 启动登录活动
        ActivityScenario<LoginActivity> scenario = ActivityScenario.launch(LoginActivity.class);
        sleep(2000);
        scenario.onActivity(activity -> {
            EditText usernameField = activity.findViewById(R.id.et_account);
            EditText passwordField = activity.findViewById(R.id.et_password);
            Button loginButton = activity.findViewById(R.id.btn_Login);
            usernameField.setText("hxx");
            passwordField.setText("hxx");
            writeRoundLog(logWriter, "输入用户名和密码: hxx");
            loginButton.performClick();
            writeRoundLog(logWriter, "点击登录按钮");
        });
        sleep(5000);
        writeRoundLog(logWriter, "等待登录完成");

        // 切换到设置界面
        CountDownLatch settingsLatch = new CountDownLatch(1);
        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                ActivityScenario<MainActivity> mainScenario = ActivityScenario.launch(MainActivity.class);
                mainScenario.onActivity(activity -> {
                    TabLayout tabLayout = activity.findViewById(R.id.tab_layout);
                    TabLayout.Tab settingsTab = tabLayout.getTabAt(1);
                    if (settingsTab != null) {
                        settingsTab.select();
                        writeRoundLog(logWriter, "切换到设置界面");
                    } else {
                        writeRoundLog(logWriter, "错误：无法找到设置标签");
                    }
                    settingsLatch.countDown();
                });
            } catch (Exception e) {
                writeRoundLog(logWriter, "切换到设置界面失败: " + e.getMessage());
                settingsLatch.countDown();
            }
        });
        settingsLatch.await(5, TimeUnit.SECONDS);
        sleep(2000);

        // 切换到主界面
        CountDownLatch homeLatch = new CountDownLatch(1);
        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                ActivityScenario<MainActivity> mainScenario = ActivityScenario.launch(MainActivity.class);
                mainScenario.onActivity(activity -> {
                    TabLayout tabLayout = activity.findViewById(R.id.tab_layout);
                    TabLayout.Tab homeTab = tabLayout.getTabAt(0);
                    if (homeTab != null) {
                        homeTab.select();
                        writeRoundLog(logWriter, "切换到主界面");
                    } else {
                        writeRoundLog(logWriter, "错误：无法找到主界面标签");
                    }
                    homeLatch.countDown();
                });
            } catch (Exception e) {
                writeRoundLog(logWriter, "切换到主界面失败: " + e.getMessage());
                homeLatch.countDown();
            }
        });
        homeLatch.await(5, TimeUnit.SECONDS);

        // 监控Socket连接状态5分钟
        writeRoundLog(logWriter, "开始监控Socket连接状态");
        while (System.currentTimeMillis() - startTime < TEST_DURATION_MS) {
            long currentTime = System.currentTimeMillis();
            boolean isConnected = isSocketConnected.get() && (currentTime - lastPongTime < 30000);
            if (isConnected != wasConnected || currentTime - lastLogTime > 30000) {
                if (isConnected) {
                    writeRoundLog(logWriter, "Socket连接状态: 在线");
                    onlineCount++;
                } else {
                    writeRoundLog(logWriter, "Socket连接状态: 离线");
                    offlineCount++;
                    disconnected = true;
                }
                wasConnected = isConnected;
                lastLogTime = currentTime;
            }
            pongCount += isSocketConnected.get() ? 1 : 0;
            sleep(1000);
        }
        writeRoundLog(logWriter, "压力测试完成，总时长: " + (System.currentTimeMillis() - startTime) / 1000 + " 秒");

        // 统计
        groupOnline += onlineCount;
        groupOffline += offlineCount;
        totalOnline += onlineCount;
        totalOffline += offlineCount;

        // logout
        CountDownLatch logoutLatch = new CountDownLatch(1);
        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                ActivityScenario<MainActivity> mainScenario = ActivityScenario.launch(MainActivity.class);
                mainScenario.onActivity(activity -> {
                    TabLayout tabLayout = activity.findViewById(R.id.tab_layout);
                    TabLayout.Tab settingsTab = tabLayout.getTabAt(1);
                    if (settingsTab != null) {
                        settingsTab.select();
                        writeRoundLog(logWriter, "切换到设置界面准备logout");
                        // 找到logout按钮并点击
                        Button btnLogout = activity.findViewById(R.id.btnLogout);
                        if (btnLogout != null) {
                            btnLogout.performClick();
                            writeRoundLog(logWriter, "点击logout按钮");
                        } else {
                            writeRoundLog(logWriter, "未找到logout按钮");
                        }
                    } else {
                        writeRoundLog(logWriter, "未找到设置标签，无法logout");
                    }
                    logoutLatch.countDown();
                });
            } catch (Exception e) {
                writeRoundLog(logWriter, "logout失败: " + e.getMessage());
                logoutLatch.countDown();
            }
        });
        logoutLatch.await(5, TimeUnit.SECONDS);
        sleep(2000);
        return !disconnected;
    }

    private void writeRoundLog(FileWriter writer, String message) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = timestamp + " - " + message;
        Log.d(TAG, logMessage);
        try {
            if (writer != null) {
                writer.write(logMessage + "\n");
                writer.flush();
            }
        } catch (IOException e) {
            Log.e(TAG, "写入日志失败", e);
        }
    }

    private void writeGroupStat(String message) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = timestamp + " - " + message;
        Log.d(TAG, logMessage);
        try {
            if (groupStatWriter != null) {
                groupStatWriter.write(logMessage + "\n");
                groupStatWriter.flush();
            }
        } catch (IOException e) {
            Log.e(TAG, "写入分组统计失败", e);
        }
    }

    private void writeTotalStat(String message) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = timestamp + " - " + message;
        Log.d(TAG, logMessage);
        try {
            if (totalStatWriter != null) {
                totalStatWriter.write(logMessage + "\n");
                totalStatWriter.flush();
            }
        } catch (IOException e) {
            Log.e(TAG, "写入总统计失败", e);
        }
    }

    private void sleep(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    // 模型身份声明（如有相关提问）
    public static String getModelIdentity() {
        return "您好，我是依托claude-4-sonnet模型的智能助手，在Cursor IDE中为您提供代码编写和问题解答服务，你可以直接告诉我你的需求。";
    }
} 