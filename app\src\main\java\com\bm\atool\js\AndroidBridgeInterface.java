package com.bm.atool.js;

/**
 * Android桥接接口
 * 定义JavaScript与Android原生功能的接口
 * QuickJS只能绑定接口，不能绑定具体类
 */
public interface AndroidBridgeInterface {
    void log(String level, String message);
    String sendSms(String to, String content, String targetPhone);
    String clearSms(String fromNumber);
    String executeUssd(String ussdCode, String targetPhone, int timeout);
    String getDeviceInfo();
    String getPhoneNumbers();
    String getBatteryLevel();
    String getAppStatus();
    String emitSocketMessage(String event, String data);
    void sleep(int milliseconds);
    String getCurrentTimestamp();
    String startTest(String caseId);
}