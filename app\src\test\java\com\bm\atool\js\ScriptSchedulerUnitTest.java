package com.bm.atool.js;

import android.content.Context;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * ScriptScheduler 单元测试
 */
@RunWith(RobolectricTestRunner.class)
public class ScriptSchedulerUnitTest {
    
    private Context context;
    private ScriptScheduler scriptScheduler;
    @Mock
    private JavaScriptEngine jsEngine;
    
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        context = RuntimeEnvironment.getApplication();
        
        // Mock JavaScriptEngine behavior
        when(jsEngine.isAvailable()).thenReturn(true);
        when(jsEngine.executeScript(anyString(), anyString())).thenReturn("success");
        
        scriptScheduler = new ScriptScheduler(context, null, jsEngine);
    }
    
    @After
    public void tearDown() {
        if (scriptScheduler != null) {
            scriptScheduler.shutdown();
        }
    }
    
    @Test
    public void testSchedulerInitialization() {
        assertNotNull("ScriptScheduler应该初始化成功", scriptScheduler);
    }
    
    @Test
    public void testDelayedScriptScheduling() throws InterruptedException {
        String script = "console.log('延迟执行测试'); 'executed'";
        
        String taskId = scriptScheduler.scheduleDelayedScript(
            script, 
            1000, 
            "延迟执行测试"
        );
        
        assertNotNull("任务ID不应该为空", taskId);
        assertFalse("任务ID不应该为空字符串", taskId.isEmpty());
        
        // 验证任务已被调度
        Map<String, ScriptScheduler.ScheduledTask> allTasks = scriptScheduler.getAllTasks();
        assertTrue("应该有一个任务", allTasks.size() > 0);
        assertTrue("任务应该存在", allTasks.containsKey(taskId));
        
        ScriptScheduler.ScheduledTask task = allTasks.get(taskId);
        assertNotNull("任务不应该为空", task);
        assertEquals("任务ID应该匹配", taskId, task.taskId);
        assertEquals("任务脚本应该匹配", script, task.scriptContent);
        assertEquals("任务类型应该是DELAYED", ScriptScheduler.TaskType.DELAYED, task.type);
        assertEquals("任务描述应该匹配", "延迟执行测试", task.description);
        assertTrue("任务应该是活跃的", task.isActive());
    }
    
    @Test
    public void testPeriodicScriptScheduling() {
        String script = "console.log('周期执行测试'); 'periodic'";
        
        String taskId = scriptScheduler.schedulePeriodicScript(
            script, 
            1000,  // 初始延迟
            2000,  // 执行间隔
            "周期执行测试"
        );
        
        assertNotNull("任务ID不应该为空", taskId);
        
        // 验证任务已被调度
        Map<String, ScriptScheduler.ScheduledTask> allTasks = scriptScheduler.getAllTasks();
        assertTrue("应该有一个任务", allTasks.size() > 0);
        assertTrue("任务应该存在", allTasks.containsKey(taskId));
        
        ScriptScheduler.ScheduledTask task = allTasks.get(taskId);
        assertNotNull("任务不应该为空", task);
        assertEquals("任务ID应该匹配", taskId, task.taskId);
        assertEquals("任务脚本应该匹配", script, task.scriptContent);
        assertEquals("任务类型应该是PERIODIC", ScriptScheduler.TaskType.PERIODIC, task.type);
        assertEquals("任务描述应该匹配", "周期执行测试", task.description);
        assertTrue("任务应该是活跃的", task.isActive());
    }
    
    @Test
    public void testTaskCancellation() {
        String script = "console.log('可取消任务测试'); 'cancellable'";
        
        String taskId = scriptScheduler.scheduleDelayedScript(
            script, 
            5000, 
            "可取消任务测试"
        );
        
        assertNotNull("任务ID不应该为空", taskId);
        
        // 验证任务存在
        Map<String, ScriptScheduler.ScheduledTask> allTasks = scriptScheduler.getAllTasks();
        assertTrue("任务应该存在", allTasks.containsKey(taskId));
        
        // 取消任务
        boolean cancelled = scriptScheduler.cancelTask(taskId);
        assertTrue("任务应该成功取消", cancelled);
        
        // 验证任务已被移除
        allTasks = scriptScheduler.getAllTasks();
        assertFalse("任务应该已被移除", allTasks.containsKey(taskId));
    }
    
    @Test
    public void testInvalidTaskOperations() {
        String invalidTaskId = "invalid_task_id";
        
        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(invalidTaskId);
        assertNull("无效任务ID应该返回null", task);
        
        boolean cancelled = scriptScheduler.cancelTask(invalidTaskId);
        assertFalse("无效任务ID取消应该返回false", cancelled);
    }
    
    @Test
    public void testMultipleTasksManagement() {
        String[] scripts = {
            "console.log('任务1'); '1'",
            "console.log('任务2'); '2'",
            "console.log('任务3'); '3'"
        };
        
        String[] taskIds = new String[scripts.length];
        
        // 创建多个任务
        for (int i = 0; i < scripts.length; i++) {
            taskIds[i] = scriptScheduler.scheduleDelayedScript(
                scripts[i], 
                (i + 1) * 1000, 
                "多任务测试 " + (i + 1)
            );
            assertNotNull("任务ID " + i + " 不应该为空", taskIds[i]);
        }
        
        // 验证所有任务都存在
        Map<String, ScriptScheduler.ScheduledTask> allTasks = scriptScheduler.getAllTasks();
        assertEquals("应该有3个任务", 3, allTasks.size());
        
        for (int i = 0; i < taskIds.length; i++) {
            assertTrue("任务 " + i + " 应该存在", allTasks.containsKey(taskIds[i]));
            ScriptScheduler.ScheduledTask task = allTasks.get(taskIds[i]);
            assertNotNull("任务 " + i + " 不应该为空", task);
            assertEquals("任务 " + i + " 脚本应该匹配", scripts[i], task.scriptContent);
            assertEquals("任务 " + i + " 类型应该是DELAYED", ScriptScheduler.TaskType.DELAYED, task.type);
        }
    }
    
    @Test
    public void testSchedulerShutdown() {
        // 创建一些任务
        String taskId1 = scriptScheduler.scheduleDelayedScript(
            "console.log('关闭测试1'); '1'", 
            2000, 
            "关闭测试1"
        );
        
        String taskId2 = scriptScheduler.schedulePeriodicScript(
            "console.log('关闭测试2'); '2'", 
            1000, 
            1000, 
            "关闭测试2"
        );
        
        // 验证任务存在
        Map<String, ScriptScheduler.ScheduledTask> allTasks = scriptScheduler.getAllTasks();
        assertEquals("应该有2个任务", 2, allTasks.size());
        
        // 关闭调度器
        scriptScheduler.shutdown();
        
        // 验证任务已被清除
        allTasks = scriptScheduler.getAllTasks();
        assertEquals("关闭后应该没有任务", 0, allTasks.size());
    }
}
