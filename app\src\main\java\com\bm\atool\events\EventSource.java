package com.bm.atool.events;

import java.util.ArrayList;

public class EventSource<T> {
    private ArrayList<IEventListener<T>> listeners = new ArrayList<>();
    public void addEventListener(IEventListener<T> listener){
        this.listeners.add(listener);
    }
    public void removeEventListener(IEventListener<T> listener){
        this.listeners.remove(listener);
    }
    public void fire(T event){
        for(IEventListener<T>  listener: listeners ){
            listener.onEvent(event);
        }
    }
    public  interface IEventListener<T>{
        void onEvent(T event);
    }
}
