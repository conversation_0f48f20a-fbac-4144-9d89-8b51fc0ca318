package com.bm.atool.utils;

import static android.content.pm.PackageManager.PERMISSION_GRANTED;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.nfc.Tag;
import android.os.Build;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;

import com.bm.atool.Sys;
import com.bm.atool.model.PermissionModel;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class PermissionUtils {
    private static final String TAG = PermissionUtils.class.getSimpleName();
    
    public static final String PREF_FIRST_RUN = "first_run";
    
    public static  boolean isPermissionOk(Context context){
        return isPermissionOk(getAllPermissions(context));
    }
//    private static Thread accessibilityCheckingThread = null;
    public static  boolean isPermissionOk(List<PermissionModel> pms){
        for (PermissionModel pm: pms) {
            if(!pm.optional && !pm.granted){
                return false;
            }
        }
        return true;
    }
    public static  boolean isPermissionOk(Context context, String permission){
        int checkResult = ActivityCompat.checkSelfPermission(context, permission);
        Log.d(TAG,"isPermissionOk: Checking permission: " + permission + ", Result: " + (checkResult == PERMISSION_GRANTED ? "GRANTED" : "DENIED"));
        return checkResult == PERMISSION_GRANTED;
    }

    public static ArrayList<PermissionModel> getAllPermissions(Context context){
        ArrayList<PermissionModel> permissions = new ArrayList<>();
        permissions.add(createPermissionModel(context,"Foreground Service", Manifest.permission.FOREGROUND_SERVICE, false));
        permissions.add(createPermissionModel(context,"Notifications", Manifest.permission.POST_NOTIFICATIONS, false));

        permissions.add(createPermissionModel(context,"NotificationListener", "NotificationListener", false));
//        permissions.add(createPermissionModel(context,"VPN", Manifest.permission.BIND_VPN_SERVICE, false));
        permissions.add(createPermissionModel(context,"Phone State", Manifest.permission.READ_PHONE_STATE, false));
//        permissions.add(createPermissionModel(context,"PRIVILEGED Phone State", "android.permission.READ_PRIVILEGED_PHONE_STATE", false));


        permissions.add(createPermissionModel(context,"Read Phone Numbers", Manifest.permission.READ_PHONE_NUMBERS, false));
        permissions.add(createPermissionModel(context,"Receive SMS", Manifest.permission.RECEIVE_SMS, false));
        permissions.add(createPermissionModel(context,"Read SMS", Manifest.permission.READ_SMS, false));
        permissions.add(createPermissionModel(context,"Send SMS", Manifest.permission.SEND_SMS, false));
        permissions.add(createPermissionModel(context,"Write SMS", "android.permission.WRITE_SMS", false));
        permissions.add(createPermissionModel(context,"Accessibility", null, false));
        permissions.add(createPermissionModel(context,"Manage Overlay", Settings.ACTION_MANAGE_OVERLAY_PERMISSION, false));
        permissions.add(createPermissionModel(context,"Battery Optimizations", Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS, false));
//        permissions.add(createPermissionModel(context,"ACCESSIBILITY_SERVICE", Manifest.permission.BIND_ACCESSIBILITY_SERVICE, false));
        return permissions;
    }
    private  static PermissionModel createPermissionModel(Context context,String name, String permission, Boolean optional){
        Boolean grantedStatus;
        if("Accessibility".equals(name)){
            grantedStatus = isAccessibilitySettingsOn(context);
        } else if ("NotificationListener".equals(name)) {
            grantedStatus = isNotificationListenerEnabled(context);
        } else if(Settings.ACTION_MANAGE_OVERLAY_PERMISSION.equals(permission)){
            grantedStatus = canDrawOverlays(context);
        } else if(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS.equals(permission)){
            grantedStatus = isIgnoringBatteryOptimizations(context);
        } else {
            grantedStatus = isPermissionOk(context, permission);
        }

        Log.d(TAG, "createPermissionModel: Name: " + name + ", FullName: " + permission + ", Granted: " + grantedStatus);
        return new PermissionModel(name, permission, optional, grantedStatus);
    }

    public static boolean isAccessibilitySettingsOn() {
        return isAccessibilitySettingsOn(Sys.app);
    }
    
    public static boolean isAccessibilitySettingsOn(Context context) {
        if (context == null) {
            Log.e(TAG, "isAccessibilitySettingsOn: Context is null");
            return false;
        }
        
        int accessibilityEnabled = 0;
        try {
            accessibilityEnabled = Settings.Secure.getInt(context.getContentResolver(), Settings.Secure.ACCESSIBILITY_ENABLED);
            Log.d(TAG, "isAccessibilitySettingsOn: accessibilityEnabled = " + accessibilityEnabled);
        } catch (Settings.SettingNotFoundException e) {
            Log.e(TAG, "isAccessibilitySettingsOn: " + e.getMessage());
        }
        
        if (accessibilityEnabled != 1) {
            Log.d(TAG, "isAccessibilitySettingsOn: Accessibility is not enabled globally");
            return false;
        }
        
        String enabledServices = Settings.Secure.getString(context.getContentResolver(), 
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
        if (enabledServices == null) {
            Log.d(TAG, "isAccessibilitySettingsOn: No enabled accessibility services found");
            return false;
        }
        
        String packageName = context.getPackageName();
        String serviceId = packageName + "/com.bm.atool.service.ANTAccessibilityService";
        
        Log.d(TAG, "isAccessibilitySettingsOn: Checking if " + serviceId + " is in " + enabledServices);
        
        boolean isEnabled = enabledServices.toLowerCase().contains(serviceId.toLowerCase());
        Log.d(TAG, "isAccessibilitySettingsOn: Service is " + (isEnabled ? "enabled" : "not enabled"));
        return isEnabled;
    }

    public static Boolean canDrawOverlays(Context context) {
        if (context == null) {
            Log.e(TAG, "canDrawOverlays: Context is null");
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(context);
        }
        return true;
    }
    
    public static boolean isIgnoringBatteryOptimizations(Context context) {
        if (context == null) {
            Log.e(TAG, "isIgnoringBatteryOptimizations: Context is null");
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
                if (powerManager != null) {
                    return powerManager.isIgnoringBatteryOptimizations(context.getPackageName());
                }
            } catch (Exception e) {
                Log.e(TAG, "isIgnoringBatteryOptimizations error: " + e.getMessage());
            }
        }
        return false;
    }

    public static void accessibilityToSettingPage(Context context) {
        //开启辅助功能页面
        try {
            Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            e.printStackTrace();
        }
    }

    public static void manageOverlayToSettingPage(Context context) {
        Log.d(TAG, "manageOverlayToSettingPage: Attempting to open overlay settings for package: " + context.getPackageName());
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + context.getPackageName()));
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                Log.d(TAG, "manageOverlayToSettingPage: Intent launched successfully.");
            } catch (Exception e) {
                Log.e(TAG, "manageOverlayToSettingPage: Error launching intent: " + e.getMessage());
                // Fallback to general settings if specific intent fails
                Intent intent = new Intent(Settings.ACTION_SETTINGS);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
            }
        } else {
            Log.d(TAG, "manageOverlayToSettingPage: Device SDK < M, no specific overlay settings page.");
            // 对于低于 Android 6.0 的设备，可能需要其他权限处理或不执行此操作
            // 或者您可以选择向用户显示一个消息，说明此功能不适用于他们的设备
        }
    }

    public static void batteryOptimizationToSettingPage(Context context) {
        Log.d(TAG, "batteryOptimizationToSettingPage: Attempting to open battery optimization settings for package: " + context.getPackageName());
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                Log.d(TAG, "batteryOptimizationToSettingPage: Intent launched successfully.");
            } else {
                Log.d(TAG, "batteryOptimizationToSettingPage: Device SDK < M, opening general settings.");
                Intent intent = new Intent(Settings.ACTION_SETTINGS);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
            }
        } catch (Exception e) {
            Log.e(TAG, "batteryOptimizationToSettingPage: Error launching intent: " + e.getMessage());
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            e.printStackTrace();
        }
    }
    
    public static boolean isFirstRun(Context context) {
        return context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
                .getBoolean(PREF_FIRST_RUN, true);
    }
    
    public static void setFirstRunCompleted(Context context) {
        context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
                .edit()
                .putBoolean(PREF_FIRST_RUN, false)
                .apply();
    }
    
    public static void checkAndRequestPermissions(Context context) {
        ArrayList<PermissionModel> permissions = getAllPermissions(context);
        for (PermissionModel permission : permissions) {
            if (!permission.granted && !permission.optional) {
                if (permission.name.equals("Accessibility")) {
                    accessibilityToSettingPage(context);
                    return;
                } else if (permission.fullName.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)) {
                    manageOverlayToSettingPage(context);
                    return;
                } else if (permission.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)) {
                    batteryOptimizationToSettingPage(context);
                    return;
                } else if (permission.fullName != null) {
                    ActivityCompat.requestPermissions((android.app.Activity) context, 
                            new String[]{permission.fullName}, 1);
                    return;
                }
            }
        }
    }

    public static void notificationListenerToSettingPage(Context context) {
        try {
            Intent intent = new Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "notificationListenerToSettingPage: Error launching intent: " + e.getMessage());
            Intent intent = new Intent(Settings.ACTION_SETTINGS);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        }
    }

    public static boolean isNotificationListenerEnabled(Context context) {
        if (context == null) return false;
        String pkgName = context.getPackageName();
        final String flat = android.provider.Settings.Secure.getString(
                context.getContentResolver(),
                "enabled_notification_listeners");
        return flat != null && flat.contains(pkgName);
    }
}
