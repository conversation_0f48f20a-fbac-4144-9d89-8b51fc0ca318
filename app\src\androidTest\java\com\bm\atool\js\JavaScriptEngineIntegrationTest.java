package com.bm.atool.js;

import android.content.Context;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.*;

/**
 * JavaScript引擎集成测试
 * 在真实Android环境中测试JavaScript引擎功能
 */
@RunWith(AndroidJUnit4.class)
public class JavaScriptEngineIntegrationTest {
    
    private Context context;
    private JavaScriptEngine jsEngine;
    
    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
    }
    
    @After
    public void tearDown() {
        if (jsEngine != null) {
            jsEngine.close();
            jsEngine = null;
        }
    }
    
    @Test
    public void testJavaScriptEngineInitializationInRealEnvironment() {
        try {
            // 在真实Android环境中测试JavaScript引擎初始化
            jsEngine = new JavaScriptEngine(context, null);
            
            // 验证引擎是否成功初始化
            assertTrue("JavaScript引擎应该在真实环境中成功初始化", jsEngine.isAvailable());
            
            // 测试基本计算
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("2 + 3");
            assertTrue("基本计算应该成功", result.success);
            assertEquals("计算结果应该正确", "5", result.result);
            
        } catch (Exception e) {
            fail("JavaScript引擎在真实环境中初始化失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testAndroidBridgeInRealEnvironment() {
        try {
            jsEngine = new JavaScriptEngine(context, null);
            
            // 测试Android桥接功能
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("Android.getCurrentTimestamp()");
            assertTrue("获取时间戳应该成功", result.success);
            assertNotNull("时间戳不应该为空", result.result);
            
            // 测试设备信息获取
            result = jsEngine.executeScript("Android.getDeviceInfo()");
            assertTrue("获取设备信息应该成功", result.success);
            assertNotNull("设备信息不应该为空", result.result);
            assertTrue("设备信息应该包含JSON数据", result.result.toString().contains("{"));
            
        } catch (Exception e) {
            fail("Android桥接在真实环境中测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testComplexJavaScriptExecution() {
        try {
            jsEngine = new JavaScriptEngine(context, null);
            
            // 测试复杂的JavaScript代码执行
            String complexScript = 
                "function fibonacci(n) {" +
                "  if (n <= 1) return n;" +
                "  return fibonacci(n - 1) + fibonacci(n - 2);" +
                "}" +
                "fibonacci(10)";
            
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript(complexScript);
            assertTrue("复杂脚本执行应该成功", result.success);
            assertEquals("斐波那契数列计算结果应该正确", "55", result.result);
            
        } catch (Exception e) {
            fail("复杂JavaScript执行测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testJavaScriptEngineReset() {
        try {
            jsEngine = new JavaScriptEngine(context, null);
            
            // 设置一个变量
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("var testVar = 'hello'; testVar");
            assertTrue("设置变量应该成功", result.success);
            assertEquals("变量值应该正确", "hello", result.result);
            
            // 重置引擎
            jsEngine.reset();
            
            // 验证引擎仍然可用
            assertTrue("重置后引擎应该仍然可用", jsEngine.isAvailable());
            
            // 验证变量已被清除
            result = jsEngine.executeScript("typeof testVar");
            assertTrue("检查变量类型应该成功", result.success);
            assertEquals("变量应该已被清除", "undefined", result.result);
            
        } catch (Exception e) {
            fail("JavaScript引擎重置测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testConsoleLogging() {
        try {
            jsEngine = new JavaScriptEngine(context, null);
            
            // 测试console.log功能
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript(
                "console.log('测试日志消息'); " +
                "console.error('测试错误消息'); " +
                "console.warn('测试警告消息'); " +
                "'logging_test_complete'"
            );
            
            assertTrue("console日志测试应该成功", result.success);
            assertEquals("返回值应该正确", "logging_test_complete", result.result);
            
        } catch (Exception e) {
            fail("Console日志测试失败: " + e.getMessage());
        }
    }
}
