package com.bm.atool.service;

import android.annotation.TargetApi;
import android.app.job.JobParameters;
import android.app.job.JobService;
import android.os.Build;
import android.util.Log;

import com.bm.atool.Sys;

/**
 * Android 5.0+ 使用的 JobScheduler.
 * 运行在 :watch 子进程中.
 *
 */
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
public class JobSchedulerService extends JobService {

    @Override
    public boolean onStartJob(JobParameters params) {
        Log.d("JobSchedulerService", "JobSchedulerService  onStartJob...");
        Sys.startServiceSafely(this, WatchDogService.class);
        return false;
    }

    @Override
    public boolean onStopJob(JobParameters params) {
        Log.d("JobSchedulerService", "JobSchedulerService  onStopJob...");
        return false;
    }
}