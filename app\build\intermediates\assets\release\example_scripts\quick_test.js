/**
 * 快速功能验证脚本
 * 用于快速检查所有核心功能是否正常工作
 */

console.log("=== Android QuickJS 集成快速验证 ===");
console.log("开始时间:", new Date().toLocaleString());

// 测试结果记录
var testResults = {
    engine: { name: "JavaScript引擎", passed: false, details: "" },
    android: { name: "Android桥接", passed: false, details: "" },
    sms: { name: "SMS功能", passed: false, details: "" },
    ussd: { name: "USSD功能", passed: false, details: "" },
    system: { name: "系统功能", passed: false, details: "" },
    console: { name: "控制台功能", passed: false, details: "" },
    json: { name: "JSON处理", passed: false, details: "" },
    error: { name: "错误处理", passed: false, details: "" }
};

// 测试计数器
var totalTests = 0;
var passedTests = 0;

// 测试辅助函数
function runTest(testName, testFunction) {
    totalTests++;
    console.log("\n--- 测试: " + testName + " ---");
    
    try {
        var result = testFunction();
        if (result.success) {
            testResults[testName].passed = true;
            testResults[testName].details = result.message || "测试通过";
            passedTests++;
            console.log("✓ " + testName + ": 通过");
        } else {
            testResults[testName].details = result.message || "测试失败";
            console.log("✗ " + testName + ": 失败 - " + result.message);
        }
    } catch (error) {
        testResults[testName].details = "异常: " + error.toString();
        console.log("✗ " + testName + ": 异常 - " + error.toString());
    }
}

// 1. JavaScript引擎基础测试
runTest("engine", function() {
    var mathResult = 2 + 3;
    var stringResult = "Hello" + " " + "World";
    var arrayResult = [1, 2, 3].length;
    var objectResult = {a: 1, b: 2};
    
    if (mathResult === 5 && stringResult === "Hello World" && 
        arrayResult === 3 && objectResult.a === 1) {
        return { success: true, message: "基础运算正常" };
    } else {
        return { success: false, message: "基础运算异常" };
    }
});

// 2. Android桥接测试
runTest("android", function() {
    if (typeof Android === "object") {
        var timestamp = Android.getCurrentTimestamp();
        if (timestamp && timestamp.length > 0) {
            return { success: true, message: "桥接正常，时间戳: " + timestamp };
        } else {
            return { success: false, message: "桥接存在但方法调用失败" };
        }
    } else {
        return { success: false, message: "Android对象不存在" };
    }
});

// 3. SMS功能测试
runTest("sms", function() {
    if (typeof SMS === "object") {
        if (typeof SMS.send === "function" && typeof SMS.clear === "function") {
            return { success: true, message: "SMS对象和方法存在" };
        } else {
            return { success: false, message: "SMS方法不完整" };
        }
    } else {
        return { success: false, message: "SMS对象不存在" };
    }
});

// 4. USSD功能测试
runTest("ussd", function() {
    if (typeof USSD === "object") {
        if (typeof USSD.execute === "function") {
            return { success: true, message: "USSD对象和方法存在" };
        } else {
            return { success: false, message: "USSD.execute方法不存在" };
        }
    } else {
        return { success: false, message: "USSD对象不存在" };
    }
});

// 5. 系统功能测试
runTest("system", function() {
    if (typeof System === "object") {
        var methods = ["getDeviceInfo", "getPhoneNumbers", "getBatteryLevel", "getAppStatus"];
        var missingMethods = [];
        
        for (var i = 0; i < methods.length; i++) {
            if (typeof System[methods[i]] !== "function") {
                missingMethods.push(methods[i]);
            }
        }
        
        if (missingMethods.length === 0) {
            // 尝试调用一个方法
            var deviceInfo = System.getDeviceInfo();
            if (deviceInfo) {
                return { success: true, message: "系统功能正常，设备信息获取成功" };
            } else {
                return { success: false, message: "系统方法存在但调用失败" };
            }
        } else {
            return { success: false, message: "缺少方法: " + missingMethods.join(", ") };
        }
    } else {
        return { success: false, message: "System对象不存在" };
    }
});

// 6. 控制台功能测试
runTest("console", function() {
    if (typeof console === "object") {
        if (typeof console.log === "function") {
            console.log("控制台测试消息");
            return { success: true, message: "控制台功能正常" };
        } else {
            return { success: false, message: "console.log方法不存在" };
        }
    } else {
        return { success: false, message: "console对象不存在" };
    }
});

// 7. JSON处理测试
runTest("json", function() {
    var testObj = { name: "test", value: 123, array: [1, 2, 3] };
    var jsonString = JSON.stringify(testObj);
    var parsedObj = JSON.parse(jsonString);
    
    if (parsedObj.name === "test" && parsedObj.value === 123 && parsedObj.array.length === 3) {
        return { success: true, message: "JSON序列化和反序列化正常" };
    } else {
        return { success: false, message: "JSON处理异常" };
    }
});

// 8. 错误处理测试
runTest("error", function() {
    try {
        // 故意触发错误
        var result = nonExistentFunction();
        return { success: false, message: "错误处理失效，应该抛出异常" };
    } catch (error) {
        if (error.toString().indexOf("nonExistentFunction") >= 0) {
            return { success: true, message: "错误处理正常" };
        } else {
            return { success: false, message: "错误信息不正确: " + error.toString() };
        }
    }
});

// 生成测试报告
console.log("\n" + "=".repeat(50));
console.log("测试报告");
console.log("=".repeat(50));
console.log("总测试数: " + totalTests);
console.log("通过测试: " + passedTests);
console.log("失败测试: " + (totalTests - passedTests));
console.log("成功率: " + Math.round(passedTests / totalTests * 100) + "%");

console.log("\n详细结果:");
for (var testName in testResults) {
    var test = testResults[testName];
    var status = test.passed ? "✓" : "✗";
    console.log(status + " " + test.name + ": " + test.details);
}

// 总体评估
console.log("\n" + "=".repeat(50));
if (passedTests === totalTests) {
    console.log("🎉 恭喜！所有功能验证通过！");
    console.log("系统已准备就绪，可以正常使用。");
} else if (passedTests >= totalTests * 0.8) {
    console.log("⚠️ 大部分功能正常，但有部分问题需要关注。");
    console.log("建议检查失败的测试项目。");
} else {
    console.log("❌ 发现多个问题，需要进行修复。");
    console.log("请检查系统配置和权限设置。");
}

console.log("\n结束时间:", new Date().toLocaleString());
console.log("=== 快速验证完成 ===");

// 返回测试结果供外部使用
testResults;
