//package com.bm.atool.ol;
//
//import static java.util.Objects.isNull;
//
//import android.accessibilityservice.AccessibilityService;
//import android.graphics.PixelFormat;
//import android.os.Build;
//import android.util.DisplayMetrics;
//import android.util.Log;
//import android.view.Gravity;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.WindowManager;
//import android.view.accessibility.AccessibilityEvent;
//import android.widget.ImageView;
//
//import androidx.annotation.NonNull;
//import androidx.lifecycle.Lifecycle;
//import androidx.lifecycle.LifecycleOwner;
//import androidx.lifecycle.LifecycleRegistry;
//
//import com.bm.atool.R;
//
//import java.util.Objects;
//
//public class MyAccessibilityService2 extends AccessibilityService implements LifecycleOwner {
//
////    private WindowManager windowManager;
//    private View floatRootView;
//    private LifecycleRegistry mLifecycleRegistry = new LifecycleRegistry(this);
//
//    private final static String TAG = MyAccessibilityService2.class.getSimpleName();
//
//    @Override
//    public void onCreate() {
//        super.onCreate();
//        try{
//            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE);
////            showWindow();
////            initObserve();
////            ViewModleMain.INSTANCE.isShowWindow.postValue(true);
////            ViewModleMain.INSTANCE.isShowWindow.setValue(true);
//        }
//        catch (Exception ex){
//            ex.printStackTrace();
//        }
//    }
//    private void initObserve() {
//        ViewModleMain.INSTANCE.isShowWindow.observeForever( it -> {
//            if (it) {
//                showWindow();
//            } else {
//                if (!isNull(floatRootView)) {
//                    WindowManager windowManager = (WindowManager)getSystemService(WINDOW_SERVICE);
//                    if (!isNull(floatRootView.getWindowToken())) {
//                        if (!isNull(windowManager)) {
//                            windowManager.removeView(floatRootView);
//                            floatRootView = null;
//                        }
//                    }
//                }
//            }
//        });
//    }
//    private void showWindow() {
//        if(Objects.nonNull(floatRootView)){
//            return;
//        }
//        WindowManager windowManager = (WindowManager)getSystemService(WINDOW_SERVICE);
//        DisplayMetrics outMetrics = new DisplayMetrics();
//        windowManager.getDefaultDisplay().getMetrics(outMetrics);
//        WindowManager.LayoutParams layoutParam = new WindowManager.LayoutParams();
//
//        //显示的位置
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            layoutParam.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY;
//            //刘海屏延伸到刘海里面
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
//                layoutParam.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
//            }
//        } else {
//            layoutParam.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
//        }
//        layoutParam.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
//        layoutParam.width = WindowManager.LayoutParams.WRAP_CONTENT;
//        layoutParam.height = WindowManager.LayoutParams.WRAP_CONTENT;
//        layoutParam.format = PixelFormat.TRANSPARENT;
//        layoutParam.gravity = Gravity.RIGHT;
//
//        floatRootView = LayoutInflater.from(this).inflate(R.layout.activity_float_item, null);
//        floatRootView.setOnTouchListener(new ItemViewTouchListener(layoutParam, windowManager));
//        ImageView iconLogo = floatRootView.findViewById(R.id.iconLogo);
////        iconLogo.setOnClickListener(new View.OnClickListener() {
////            @Override
////            public void onClick(View v) {
////                Intent intent = new Intent(MyAccessibilityService.this, MainActivity.class);
////                intent.addCategory(Intent.CATEGORY_DEFAULT);
////                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
////                startActivity(intent);
////            }
////        });
//        windowManager.addView(floatRootView, layoutParam);
//}
//
//    @Override
//    public void onAccessibilityEvent(AccessibilityEvent event) {
//        Log.d(TAG,"AccessibilityEvent:"  + event.toString());
////        event.setChecked(true);
//
//    }
//
//    @Override
//    public void onInterrupt() {
//        Log.d(TAG,"onInterrupt" );
//    }
//
//    @NonNull
//    @Override
//    public Lifecycle getLifecycle() {
//        return this.mLifecycleRegistry;
//    }
//
//    @Override
//    public void onDestroy() {
//        super.onDestroy();
//    }
//}
