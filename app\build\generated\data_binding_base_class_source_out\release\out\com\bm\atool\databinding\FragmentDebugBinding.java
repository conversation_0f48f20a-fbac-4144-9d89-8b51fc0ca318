// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDebugBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnClearJS;

  @NonNull
  public final Button btnExecuteJS;

  @NonNull
  public final Button btnStopSocket;

  @NonNull
  public final Button btnTestBattery;

  @NonNull
  public final Button btnTestDeviceInfo;

  @NonNull
  public final Button btnTestPhones;

  @NonNull
  public final TextView debugTitle;

  @NonNull
  public final EditText etJavaScript;

  @NonNull
  public final TextView tvJSResult;

  private FragmentDebugBinding(@NonNull ScrollView rootView, @NonNull Button btnClearJS,
      @NonNull Button btnExecuteJS, @NonNull Button btnStopSocket, @NonNull Button btnTestBattery,
      @NonNull Button btnTestDeviceInfo, @NonNull Button btnTestPhones,
      @NonNull TextView debugTitle, @NonNull EditText etJavaScript, @NonNull TextView tvJSResult) {
    this.rootView = rootView;
    this.btnClearJS = btnClearJS;
    this.btnExecuteJS = btnExecuteJS;
    this.btnStopSocket = btnStopSocket;
    this.btnTestBattery = btnTestBattery;
    this.btnTestDeviceInfo = btnTestDeviceInfo;
    this.btnTestPhones = btnTestPhones;
    this.debugTitle = debugTitle;
    this.etJavaScript = etJavaScript;
    this.tvJSResult = tvJSResult;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDebugBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDebugBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_debug, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDebugBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClearJS;
      Button btnClearJS = ViewBindings.findChildViewById(rootView, id);
      if (btnClearJS == null) {
        break missingId;
      }

      id = R.id.btnExecuteJS;
      Button btnExecuteJS = ViewBindings.findChildViewById(rootView, id);
      if (btnExecuteJS == null) {
        break missingId;
      }

      id = R.id.btnStopSocket;
      Button btnStopSocket = ViewBindings.findChildViewById(rootView, id);
      if (btnStopSocket == null) {
        break missingId;
      }

      id = R.id.btnTestBattery;
      Button btnTestBattery = ViewBindings.findChildViewById(rootView, id);
      if (btnTestBattery == null) {
        break missingId;
      }

      id = R.id.btnTestDeviceInfo;
      Button btnTestDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (btnTestDeviceInfo == null) {
        break missingId;
      }

      id = R.id.btnTestPhones;
      Button btnTestPhones = ViewBindings.findChildViewById(rootView, id);
      if (btnTestPhones == null) {
        break missingId;
      }

      id = R.id.debugTitle;
      TextView debugTitle = ViewBindings.findChildViewById(rootView, id);
      if (debugTitle == null) {
        break missingId;
      }

      id = R.id.etJavaScript;
      EditText etJavaScript = ViewBindings.findChildViewById(rootView, id);
      if (etJavaScript == null) {
        break missingId;
      }

      id = R.id.tvJSResult;
      TextView tvJSResult = ViewBindings.findChildViewById(rootView, id);
      if (tvJSResult == null) {
        break missingId;
      }

      return new FragmentDebugBinding((ScrollView) rootView, btnClearJS, btnExecuteJS,
          btnStopSocket, btnTestBattery, btnTestDeviceInfo, btnTestPhones, debugTitle, etJavaScript,
          tvJSResult);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
