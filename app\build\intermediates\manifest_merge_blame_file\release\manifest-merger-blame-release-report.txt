1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bm.atool"
4    android:versionCode="1"
5    android:versionName="1.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:6:5-8:36
12        android:name="android.hardware.telephony"
12-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-50
13        android:required="false" />
13-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:8:9-33
14
15    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
15-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:10:5-11:47
15-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:10:22-83
16    <uses-permission android:name="android.permission.RECEIVE_SMS" />
16-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-70
16-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-67
17    <uses-permission android:name="android.permission.READ_SMS" />
17-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
17-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
18    <uses-permission android:name="android.permission.SEND_SMS" />
18-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-67
18-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-64
19    <uses-permission android:name="android.permission.WRITE_SMS" />
19-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-68
19-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-65
20    <uses-permission android:name="android.permission.INTERNET" />
20-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
20-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
21    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
21-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-75
21-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-72
22    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
22-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-76
22-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-77
23-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-74
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-68
24-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:22-65
25
26    <permission android:name="android.permission.DEVICE_POWER" />
26-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:21:5-22:51
26-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:21:17-63
27
28    <uses-permission android:name="android.permission.BIND_JOB_SERVICE" />
28-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:23:5-24:47
28-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
29-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
30    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
30-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-79
30-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:27:5-76
31-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:27:22-73
32    <uses-permission android:name="android.permission.INTERNET" />
32-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
32-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-80
33-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-77
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
34-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
35    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES" />
35-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-77
35-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-74
36    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
36-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-79
36-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-76
37    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
37-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:33:5-81
37-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:33:22-78
38    <uses-permission android:name="android.permission.INTERNET" />
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-92
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-89
40    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-95
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-92
41    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
41-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-77
41-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-75
42    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
42-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:38:5-39:47
42-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:40:5-80
43-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:40:22-78
44
45    <permission
45-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:41:5-192:19
52        android:name="com.bm.atool.App"
52-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:51:9-28
53        android:allowBackup="true"
53-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:42:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:9-65
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:44:9-54
58        android:icon="@mipmap/ic_launcher"
58-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:45:9-43
59        android:label="@string/app_name"
59-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:9-41
60        android:roundIcon="@mipmap/ic_launcher_round"
60-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:9-54
61        android:supportsRtl="true"
61-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:9-35
62        android:theme="@style/Theme.AndroidTool"
62-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:49:9-49
63        android:usesCleartextTraffic="true" >
63-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:50:9-44
64        <activity
64-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:9-55:40
65            android:name="com.bm.atool.LoginActivity"
65-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:13-42
66            android:exported="false" />
66-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:55:13-37
67        <activity
67-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:56:9-69:20
68            android:name="com.bm.atool.MainActivity"
68-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:57:13-41
69            android:excludeFromRecents="true"
69-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:63:13-46
70            android:exported="true"
70-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:58:13-36
71            android:label="@string/app_name"
71-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:59:13-45
72            android:launchMode="singleTask"
72-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:61:13-44
73            android:taskAffinity=""
73-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:62:13-36
74            android:theme="@style/Theme.AndroidTool" >
74-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:60:13-53
75            <intent-filter>
75-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:64:13-68:29
76                <action android:name="android.intent.action.MAIN" />
76-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:65:17-69
76-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:65:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:67:17-77
78-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:67:27-74
79            </intent-filter>
80        </activity>
81        <activity
81-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:70:9-72:39
82            android:name="com.bm.atool.service.singlepixel.SinglePixelActivity"
82-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:71:13-68
83            android:exported="true" />
83-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-36
84
85        <receiver
85-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:74:9-80:20
86            android:name="com.bm.atool.receivers.SimChangedReceiver"
86-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-57
87            android:exported="true" >
87-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-36
88            <intent-filter>
88-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-79:29
89                <action android:name="android.intent.action.SIM_STATE_CHANGED" />
89-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:78:17-81
89-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:78:25-79
90            </intent-filter>
91        </receiver>
92        <receiver
92-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:81:9-87:20
93            android:name="com.bm.atool.receivers.SmsReceiver"
93-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:81:19-56
94            android:exported="true"
94-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:82:13-36
95            android:permission="android.permission.BROADCAST_SMS" >
95-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:83:13-66
96            <intent-filter>
96-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:84:13-86:29
97                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
97-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:85:17-81
97-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:85:25-79
98            </intent-filter>
99        </receiver>
100        <receiver
100-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:89:9-102:20
101            android:name="com.bm.atool.receivers.WakeUpReceiver"
101-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-53
102            android:exported="true"
102-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:92:13-36
103            android:process=":watch" >
103-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-37
104            <intent-filter>
104-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:93:13-101:29
105                <action android:name="android.intent.action.USER_PRESENT" />
105-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:94:17-76
105-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:94:25-74
106                <action android:name="android.intent.action.BOOT_COMPLETED" />
106-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:17-79
106-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:25-76
107                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
107-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:17-79
107-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:25-76
108                <action android:name="android.intent.action.USER_PRESENT" />
108-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:94:17-76
108-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:94:25-74
109                <action android:name="android.intent.action.MEDIA_MOUNTED" />
109-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:17-78
109-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:25-75
110                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
110-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:17-87
110-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:25-84
111                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
111-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:100:17-90
111-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:100:25-87
112            </intent-filter>
113        </receiver>
114        <receiver
114-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:103:9-130:20
115            android:name="com.bm.atool.receivers.WakeUpAutoStartReceiver"
115-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:104:13-62
116            android:exported="true"
116-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-36
117            android:process=":watch" >
117-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-37
118
119            <!-- 手机启动 -->
120            <intent-filter>
120-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:13-111:29
121                <action android:name="android.intent.action.BOOT_COMPLETED" />
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:17-79
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:25-76
122                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:17-79
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:25-76
123            </intent-filter>
124            <!-- 软件安装卸载 -->
125            <intent-filter>
125-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:13-117:29
126                <action android:name="android.intent.action.PACKAGE_ADDED" />
126-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-77
126-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-75
127                <action android:name="android.intent.action.PACKAGE_REMOVED" />
127-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:115:17-79
127-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:115:25-77
128
129                <data android:scheme="package" />
129-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:116:17-49
129-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:116:23-47
130            </intent-filter>
131            <!-- 网络监听 -->
132            <intent-filter>
132-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-123:29
133                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
133-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:17-79
133-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:25-76
134                <action android:name="android.net.wifi.WIFI_STATE_CJANGED" />
134-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:121:17-77
134-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:121:25-75
135                <action android:name="android.net.wifi.STATE_CHANGE" />
135-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:122:17-71
135-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:122:25-69
136            </intent-filter>
137            <!-- 文件挂载 -->
138            <intent-filter>
138-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:125:13-129:29
139                <action android:name="android.intent.action.MEDIA_EJECT" />
139-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:126:17-75
139-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:126:25-73
140                <action android:name="android.intent.action.MEDIA_MOUNTED" />
140-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:17-78
140-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:25-75
141
142                <data android:scheme="file" />
142-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:116:17-49
142-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:116:23-47
143            </intent-filter>
144        </receiver>
145
146        <!-- 守护进程 watch -->
147        <service
147-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:133:9-138:43
148            android:name="com.bm.atool.service.JobSchedulerService"
148-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:134:13-56
149            android:enabled="true"
149-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:13-35
150            android:exported="true"
150-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:137:13-36
151            android:permission="android.permission.BIND_JOB_SERVICE"
151-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:135:13-69
152            android:process=":watch_job" />
152-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:138:13-41
153        <service
153-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140:9-145:43
154            android:name="com.bm.atool.service.WatchDogService"
154-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:141:13-52
155            android:enabled="true"
155-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:143:13-35
156            android:exported="true"
156-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:144:13-36
157            android:foregroundServiceType="mediaPlayback"
157-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:142:13-58
158            android:process=":watch_dog" />
158-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:145:13-41
159        <service
159-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:147:9-149:46
160            android:name="com.bm.atool.service.PlayMusicService"
160-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:147:18-58
161            android:foregroundServiceType="mediaPlayback"
161-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:148:13-58
162            android:process=":watch_player" />
162-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-44
163        <service
163-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:150:9-163:19
164            android:name="com.bm.atool.service.ANTAccessibilityService"
164-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:151:13-60
165            android:enabled="true"
165-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:152:13-35
166            android:exported="true"
166-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:153:13-36
167            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
167-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:155:13-79
168            android:process=":accessibility" >
168-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:154:13-45
169            <intent-filter>
169-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:156:13-158:29
170                <action android:name="android.accessibilityservice.AccessibilityService" />
170-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:157:17-92
170-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:157:25-89
171            </intent-filter>
172
173            <meta-data
173-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:160:13-162:54
174                android:name="android.accessibilityservice"
174-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:161:17-60
175                android:resource="@xml/allocation" />
175-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:162:17-51
176        </service>
177        <service
177-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:164:9-180:19
178            android:name="com.bm.atool.service.SocketService"
178-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:165:13-50
179            android:exported="false"
179-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:166:13-37
180            android:label="SocketService"
180-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:167:13-42
181            android:permission="android.permission.BIND_VPN_SERVICE"
181-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-69
182            android:process=":socket" >
182-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-38
183            <intent-filter>
183-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-172:29
184                <action android:name="android.net.VpnService" />
184-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:17-65
184-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:25-62
185            </intent-filter>
186
187            <meta-data
187-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:173:13-175:39
188                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
188-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:174:17-73
189                android:value="true" />
189-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:175:17-37
190
191            <property
191-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:176:13-178:38
192                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
192-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:177:17-76
193                android:value="vpn" />
193-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:178:17-36
194        </service>
195        <service
195-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:182:9-190:19
196            android:name="com.bm.atool.service.NotificationService"
196-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-56
197            android:exported="true"
197-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:186:13-36
198            android:label="@string/app_name"
198-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:184:13-45
199            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
199-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:185:13-87
200            <intent-filter>
200-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:187:13-189:29
201                <action android:name="android.service.notification.NotificationListenerService" />
201-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:188:17-99
201-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:188:25-96
202            </intent-filter>
203        </service>
204
205        <provider
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
206            android:name="androidx.startup.InitializationProvider"
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
207            android:authorities="com.bm.atool.androidx-startup"
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
208            android:exported="false" >
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
209            <meta-data
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.emoji2.text.EmojiCompatInitializer"
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
211                android:value="androidx.startup" />
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
212            <meta-data
212-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
213-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
214                android:value="androidx.startup" />
214-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
215            <meta-data
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
216                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
217                android:value="androidx.startup" />
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
218        </provider>
219
220        <uses-library
220-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
221            android:name="androidx.window.extensions"
221-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
222            android:required="false" />
222-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
223        <uses-library
223-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
224            android:name="androidx.window.sidecar"
224-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
225            android:required="false" />
225-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
226
227        <receiver
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
228            android:name="androidx.profileinstaller.ProfileInstallReceiver"
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
229            android:directBootAware="false"
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
230            android:enabled="true"
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
231            android:exported="true"
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
232            android:permission="android.permission.DUMP" >
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
234                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
237                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
240                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
243                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
244            </intent-filter>
245        </receiver>
246    </application>
247
248</manifest>
