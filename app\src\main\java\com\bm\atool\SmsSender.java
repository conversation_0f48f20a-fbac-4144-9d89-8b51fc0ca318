package com.bm.atool;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Build;
import android.telephony.SmsManager;
import android.telephony.SubscriptionInfo;
import android.telephony.SubscriptionManager;
import android.util.Log;

import androidx.core.app.ActivityCompat;


import com.bm.atool.model.SendSmsRequest;
import com.bm.atool.receivers.SmsSendingReceiver;

import java.lang.reflect.Method;
import java.util.List;


public class SmsSender {

    private static final String TAG = "SmsSender";
    public static final String SMS_SENT = "SMS_SENT";
    public static final String DELIVERED = "SMS_DELIVERED";
//    public static void b(Context context) {
//        Cursor a = C0154fr.a(context).a(Long.MAX_VALUE);
//        if (a != null) {
//            try {
//                if (a.moveToFirst()) {
//                    ((AlarmManager) context.getSystemService("alarm")).set(AlarmManager.RTC, a.getLong(a.getColumnIndexOrThrow(Telephony.MmsSms.PendingMessages.DUE_TIME)), PendingIntent.getService(context, 0, new Intent("android.intent.action.ACTION_ONALARM", null, context, SocketService.class), PendingIntent.FLAG_IMMUTABLE));
//                }
//            } finally {
//                a.close();
//            }
//        }
//    }

    public static void sendSMS(String from, String to, String content) {
        SendSmsRequest request = new SendSmsRequest();
        request.id = System.currentTimeMillis();
        request.from = from;
        request.to = to;
        request.content = content;
        sendSMS(request);
    }

    public static void sendSMS(SendSmsRequest sendSmsRequest) {
        Context context = Sys.app;
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "无法发送短信：缺少READ_PHONE_STATE权限");
            return;
        }

        String action = SMS_SENT + '-' + String.valueOf(sendSmsRequest.id);
        Integer sendFlags = (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R)? PendingIntent.FLAG_IMMUTABLE : PendingIntent.FLAG_ONE_SHOT;
        Intent intent = new Intent(action);
        intent.putExtra("smsId", String.valueOf(sendSmsRequest.id));
        PendingIntent sentPI = PendingIntent.getBroadcast(context, 0, intent, sendFlags);
        Sys.registerReceiver(Sys.app, new SmsSendingReceiver(), new IntentFilter(action));

        SmsManager smsManager = null;

        try {
            // 如果提供了特定的手机号(卡槽)，尝试获取对应的SmsManager
            if (sendSmsRequest.targetPhone != null && !sendSmsRequest.targetPhone.isEmpty()) {
                Log.d(TAG, "尝试使用指定手机号发送短信: " + sendSmsRequest.targetPhone);
                smsManager = getSmsManagerForPhoneNumber(context, sendSmsRequest.targetPhone);
                if (smsManager != null) {
                    Log.d(TAG, "成功获取指定手机号的SmsManager");
                } else {
                    Log.w(TAG, "无法获取指定手机号的SmsManager，将尝试使用subscriptionId");
                }
            }
            
            // 如果通过手机号未获取到SmsManager，且提供了subscriptionId，则使用subscriptionId
            if (smsManager == null && sendSmsRequest.subscriptionId != null && !sendSmsRequest.subscriptionId.isEmpty()) {
                int subId = Integer.parseInt(sendSmsRequest.subscriptionId);
                Log.d(TAG, "尝试使用subscriptionId发送短信: " + subId);
                smsManager = SmsManager.getSmsManagerForSubscriptionId(subId);
            }
        } catch (Exception ex) {
            Log.e(TAG, "获取SmsManager时出错: " + ex.getMessage());
            ex.printStackTrace();
        }

        // 如果以上方法都失败，则使用默认SmsManager
        if (smsManager == null) {
            Log.d(TAG, "使用默认SmsManager发送短信");
            smsManager = SmsManager.getDefault();
        }

        try {
            Class<? extends SmsManager> smsManagerClass = smsManager.getClass();
            Method sendTextMessage = smsManagerClass.getDeclaredMethod("sendTextMessage", String.class, String.class, String.class, PendingIntent.class, PendingIntent.class);
            sendTextMessage.invoke(smsManager, sendSmsRequest.to, null, sendSmsRequest.content, sentPI, null);
        } catch (Exception e2) {
            Log.e(TAG, "反射调用sendTextMessage失败，使用标准方法: " + e2.getMessage());
            e2.printStackTrace();
            smsManager.sendTextMessage(sendSmsRequest.to, null, sendSmsRequest.content, sentPI, null);
        }
    }

    private static SmsManager getSmsManagerForPhoneNumber(Context context, String phoneNumber) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
            Log.d(TAG, "当前系统版本不支持指定手机号发送短信，使用默认SmsManager");
            return null;
        }

        try {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "无法获取SIM卡信息：缺少READ_PHONE_STATE权限");
                return null;
            }

            SubscriptionManager subscriptionManager = SubscriptionManager.from(context);
            List<SubscriptionInfo> subscriptionInfos = subscriptionManager.getActiveSubscriptionInfoList();

            if (subscriptionInfos != null) {
                for (SubscriptionInfo info : subscriptionInfos) {
                    String number = info.getNumber();
                    if (number != null && !number.isEmpty()) {
                        // 规范化电话号码进行比较
                        String normalizedConfigNumber = normalizePhoneNumber(number);
                        String normalizedTargetNumber = normalizePhoneNumber(phoneNumber);
                        
                        Log.d(TAG, "比较SIM卡号码: " + normalizedConfigNumber + " 与目标号码: " + normalizedTargetNumber);
                        
                        if (normalizedConfigNumber.equals(normalizedTargetNumber) || 
                            normalizedConfigNumber.endsWith(normalizedTargetNumber) || 
                            normalizedTargetNumber.endsWith(normalizedConfigNumber)) {
                            Log.d(TAG, "找到匹配的SIM卡，subscriptionId: " + info.getSubscriptionId());
                            return SmsManager.getSmsManagerForSubscriptionId(info.getSubscriptionId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取指定号码SIM卡时出错: " + e.getMessage());
            e.printStackTrace();
        }

        Log.w(TAG, "未找到匹配的SIM卡号码: " + phoneNumber);
        return null;
    }

    private static String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return "";
        }
        // 只保留数字，去掉所有非数字字符
        return phoneNumber.replaceAll("[^0-9]", "");
    }
}
