package com.bm.atool;

import android.app.Activity;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;

import com.bm.atool.js.JavaScriptEngine;

import io.socket.client.Socket;

public class JavaScriptTestActivity extends Activity {
    private static final String TAG = "JavaScriptTestActivity";
    private JavaScriptEngine jsEngine;
    private TextView resultTextView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        resultTextView = new TextView(this);
        resultTextView.setTextSize(16);
        setContentView(resultTextView);
        
        // 初始化JavaScript引擎
        initJavaScriptEngine();
        
        // 运行测试
        runJavaScriptTests();
    }
    
    private void initJavaScriptEngine() {
        try {
            jsEngine = new JavaScriptEngine(this, null);
            Log.d(TAG, "JavaScript引擎初始化成功: " + jsEngine.isAvailable());
        } catch (Exception e) {
            Log.e(TAG, "JavaScript引擎初始化失败", e);
        }
    }
    
    private void runJavaScriptTests() {
        if (jsEngine == null || !jsEngine.isAvailable()) {
            resultTextView.setText("JavaScript引擎不可用");
            return;
        }
        
        StringBuilder result = new StringBuilder();
        result.append("JavaScript引擎测试结果:\n\n");
        
        // 测试基本计算
        try {
            JavaScriptEngine.ScriptResult calcResult = jsEngine.executeScript("1 + 1");
            result.append("1 + 1 = ").append(calcResult.result).append("\n");
        } catch (Exception e) {
            result.append("计算测试失败: ").append(e.getMessage()).append("\n");
        }
        
        // 测试字符串操作
        try {
            JavaScriptEngine.ScriptResult stringResult = jsEngine.executeScript("'Hello ' + 'World'");
            result.append("字符串拼接: ").append(stringResult.result).append("\n");
        } catch (Exception e) {
            result.append("字符串测试失败: ").append(e.getMessage()).append("\n");
        }
        
        // 测试函数定义和调用
        try {
            // 定义函数
            jsEngine.executeScript("function add(a, b) { return a + b; }");
            // 调用函数
            JavaScriptEngine.ScriptResult functionResult = jsEngine.executeFunction("add", 5, 3);
            result.append("函数调用 add(5, 3) = ").append(functionResult.result).append("\n");
        } catch (Exception e) {
            result.append("函数测试失败: ").append(e.getMessage()).append("\n");
        }
        
        // 测试复杂脚本
        try {
            String fibonacciScript = 
                "function fibonacci(n) {" +
                "  if (n <= 1) return n;" +
                "  return fibonacci(n - 1) + fibonacci(n - 2);" +
                "}" +
                "fibonacci(10)";
            JavaScriptEngine.ScriptResult complexResult = jsEngine.executeScript(fibonacciScript);
            result.append("斐波那契数列 fibonacci(10) = ").append(complexResult.result).append("\n");
        } catch (Exception e) {
            result.append("复杂脚本测试失败: ").append(e.getMessage()).append("\n");
        }
        
        // 测试Android桥接
        try {
            JavaScriptEngine.ScriptResult androidResult = jsEngine.executeScript("typeof Android");
            result.append("Android对象类型: ").append(androidResult.result).append("\n");
        } catch (Exception e) {
            result.append("Android桥接测试失败: ").append(e.getMessage()).append("\n");
        }
        
        resultTextView.setText(result.toString());
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (jsEngine != null) {
            jsEngine.close();
        }
    }
}