# JavaScript引擎修复说明

## 问题描述

在Android应用中，JavaScript引擎执行时出现以下错误：

### 第一个错误（已修复）
```
java.lang.UnsupportedOperationException: Only interfaces can be bound. Received: class com.bm.atool.js.AndroidBridge
	at app.cash.quickjs.QuickJs.set(QuickJs.java:90)
	at com.bm.atool.js.JavaScriptEngine.setupNativeBindings(JavaScriptEngine.java:65)
```

### 第二个错误（已修复）
```
java.lang.IllegalArgumentException: Unsupported Java type long
	at app.cash.quickjs.QuickJs.set(Native Method)
	at app.cash.quickjs.QuickJs.set(QuickJs.java:104)
	at com.bm.atool.js.JavaScriptEngine.setupNativeBindings(JavaScriptEngine.java:65)
```

### 第三个错误（已修复）
```
app.cash.quickjs.QuickJsException: stack overflow
	at app.cash.quickjs.QuickJs.evaluate(Native Method)
	at app.cash.quickjs.QuickJs.evaluate(QuickJs.java:75)
	at com.bm.atool.js.JavaScriptEngine.lambda$executeScript$0$com-bm-atool-js-JavaScriptEngine(JavaScriptEngine.java:134)
```

## 错误原因

1. **接口绑定问题**：QuickJS库的`set`方法只能绑定接口（interface），而不能直接绑定类（class）
2. **数据类型不支持**：QuickJS不支持Java的`long`类型，只支持基本类型如`int`、`double`、`String`、`boolean`等
3. **线程安全问题**：QuickJS不是线程安全的，在多线程环境中使用可能导致stack overflow
4. **实例管理问题**：QuickJS实例的创建和销毁需要正确管理，避免资源泄漏

## 修复方案

### 1. 创建接口定义

在`AndroidBridge.java`中添加了`AndroidBridgeInterface`接口，只使用QuickJS支持的数据类型：

```java
interface AndroidBridgeInterface {
    void log(String level, String message);
    String sendSms(String to, String content, String targetPhone);
    String clearSms(String fromNumber);
    String executeUssd(String ussdCode, String targetPhone, int timeout);
    String getDeviceInfo();
    String getPhoneNumbers();
    String getBatteryLevel();
    String getAppStatus();
    String emitSocketMessage(String event, String data);
    void sleep(int milliseconds);
    String getCurrentTimestamp(); // 改为String类型，避免long类型不支持问题
}
```

### 5. 线程安全修复

在`JavaScriptEngine.java`中添加了线程安全保护：

```java
public ScriptResult executeScript(String script, int timeoutSeconds) {
    // 使用synchronized确保线程安全
    synchronized (this) {
        Future<ScriptResult> future = executorService.submit(() -> {
            synchronized (this) {
                // 再次检查引擎状态
                if (quickJs == null) {
                    return new ScriptResult(false, null, "JavaScript引擎在执行过程中被关闭");
                }
                Object result = quickJs.evaluate(script);
                // ...
            }
        });
        // ...
    }
}
```

### 6. 实例管理优化

改进了QuickJS实例的创建和管理：

```java
private void initializeEngine() {
    // 确保之前的实例已经关闭
    if (quickJs != null) {
        try {
            quickJs.close();
        } catch (Exception e) {
            Log.w(TAG, "关闭旧QuickJS实例时出错", e);
        }
        quickJs = null;
    }

    // 创建新的QuickJS实例
    quickJs = QuickJs.create();

    // 先测试基本功能
    Object testResult = quickJs.evaluate("1 + 1");
    Log.d(TAG, "QuickJS基本测试结果: " + testResult);

    // 如果基本测试成功，再设置绑定
    setupNativeBindings();
}
```
```

### 2. 实现接口

修改`AndroidBridge`类实现接口：

```java
public class AndroidBridge implements AndroidBridgeInterface {
    // 现有的实现代码保持不变
}
```

### 3. 修改数据类型

将不支持的`long`类型改为`String`类型：

```java
// 原代码
public long getCurrentTimestamp() {
    return System.currentTimeMillis();
}

// 修复后
public String getCurrentTimestamp() {
    return String.valueOf(System.currentTimeMillis());
}
```

### 4. 修改绑定代码

在`JavaScriptEngine.java`中修改绑定代码：

```java
// 原代码
quickJs.set("Android", AndroidBridge.class, new AndroidBridge(context, socket));

// 修复后
quickJs.set("Android", AndroidBridgeInterface.class, new AndroidBridge(context, socket));
```

## 测试方案

### 1. 单元测试

创建了`JavaScriptEngineTest.java`，包含以下测试：
- JavaScript引擎初始化测试
- Android桥接绑定测试
- JavaScript函数执行测试
- 系统API测试
- 错误处理测试

### 2. 集成测试

创建了`JavaScriptEngineIntegrationTest.java`，在真实Android环境中测试：
- 引擎初始化
- Android桥接功能
- 复杂JavaScript执行
- 引擎重置功能
- Console日志功能

### 3. 测试辅助类

创建了`JavaScriptEngineTestHelper.java`，提供：
- 引擎初始化测试
- Android桥接测试
- 系统API测试
- 完整测试套件

### 4. QuickJS兼容性测试

创建了`QuickJSCompatibilityTest.java`，专门测试：
- QuickJS支持的数据类型
- 接口绑定功能
- 基本JavaScript执行

### 5. 运行时验证

修改了`DebugFragment.java`，在初始化时：
1. 先运行QuickJS兼容性测试
2. 再运行完整的JavaScript引擎测试
3. 最后创建引擎实例

## 验证步骤

### 1. 编译验证

```bash
./gradlew compileDebugJavaWithJavac
```

### 2. 运行单元测试

```bash
./gradlew test
```

### 3. 运行集成测试

```bash
./gradlew connectedAndroidTest
```

### 4. 应用内验证

1. 启动应用
2. 进入Debug页面
3. 查看JavaScript引擎初始化状态
4. 执行测试JavaScript代码

### 5. 使用验证脚本

```bash
chmod +x test_javascript_fix.sh
./test_javascript_fix.sh
```

## 测试用例

### 基本功能测试

```javascript
// 数学计算
var sum = numbers.reduce(function(a, b) { return a + b; }, 0);

// 控制台输出
console.log("测试消息");

// 获取时间戳
var timestamp = Android.getCurrentTimestamp();

// 获取设备信息
var deviceInfo = System.getDeviceInfo();
```

### 系统API测试

```javascript
// 获取手机号码
var phones = System.getPhoneNumbers();

// 获取电池电量
var battery = System.getBatteryLevel();

// 获取应用状态
var status = Android.getAppStatus();
```

## 预期结果

修复后，JavaScript引擎应该能够：

1. ✅ 成功初始化，不再抛出绑定异常
2. ✅ 正常执行JavaScript代码
3. ✅ 调用Android原生功能
4. ✅ 使用console.log等内置功能
5. ✅ 访问System、SMS、USSD等API

## 故障排除

如果修复后仍有问题，请检查：

1. **QuickJS版本兼容性**
   - 确认使用的QuickJS版本支持接口绑定
   - 检查build.gradle中的依赖版本

2. **Android API级别**
   - 确认目标设备的Android版本支持QuickJS
   - 检查minSdkVersion设置

3. **权限问题**
   - 确认应用有必要的权限（如读取手机状态等）

4. **内存限制**
   - 在低内存设备上可能需要调整JavaScript引擎配置

## 相关文件

- `app/src/main/java/com/bm/atool/js/AndroidBridge.java` - 修复的桥接类
- `app/src/main/java/com/bm/atool/js/JavaScriptEngine.java` - 修复的引擎类
- `app/src/test/java/com/bm/atool/js/JavaScriptEngineTest.java` - 单元测试
- `app/src/androidTest/java/com/bm/atool/js/JavaScriptEngineIntegrationTest.java` - 集成测试
- `app/src/main/java/com/bm/atool/js/JavaScriptEngineTestHelper.java` - 测试辅助类
- `app/src/main/java/com/bm/atool/ui/DebugFragment.java` - 修改的调试界面
- `test_javascript_fix.sh` - 验证脚本
