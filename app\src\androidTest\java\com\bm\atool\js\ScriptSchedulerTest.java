package com.bm.atool.js;

import android.content.Context;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * ScriptScheduler 功能测试
 */
@RunWith(AndroidJUnit4.class)
public class ScriptSchedulerTest {

    private Context context;
    private ScriptScheduler scriptScheduler;
    private JavaScriptEngine jsEngine;

    @Before
    public void setUp() {
        context = ApplicationProvider.getApplicationContext();
        jsEngine = new JavaScriptEngine(context, null);
        scriptScheduler = new ScriptScheduler(context, null, jsEngine);
    }
    
    @After
    public void tearDown() {
        if (scriptScheduler != null) {
            scriptScheduler.shutdown();
        }
        if (jsEngine != null) {
            jsEngine.close();
        }
    }
    
    @Test
    public void testSchedulerInitialization() {
        assertNotNull("ScriptScheduler应该初始化成功", scriptScheduler);
    }
    
    @Test
    public void testDelayedScriptExecution() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        AtomicInteger executionCount = new AtomicInteger(0);

        // 创建一个会设置标志的脚本
        String script = "console.log('延迟执行测试'); 'executed'";

        // 调度延迟执行（2秒后执行）
        String taskId = scriptScheduler.scheduleDelayedScript(
            script,
            2000,
            "延迟执行测试"
        );

        assertNotNull("任务ID不应该为空", taskId);
        assertFalse("任务ID不应该为空字符串", taskId.isEmpty());

        // 等待执行完成
        Thread.sleep(3000);

        // 验证任务状态
        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskId);
        if (task != null) {
            assertTrue("任务应该已完成", task.future.isDone());
            assertTrue("执行次数应该大于0", task.executionCount > 0);
        }
    }
    
    @Test
    public void testPeriodicScriptExecution() throws InterruptedException {
        AtomicInteger executionCount = new AtomicInteger(0);

        // 创建一个计数脚本
        String script = "console.log('周期执行测试 #' + Date.now()); 'periodic'";

        // 调度周期执行（初始延迟1秒，每2秒执行一次）
        String taskId = scriptScheduler.schedulePeriodicScript(
            script,
            1000,  // 初始延迟
            2000,  // 执行间隔
            "周期执行测试"
        );

        assertNotNull("任务ID不应该为空", taskId);

        // 等待几次执行
        Thread.sleep(7000); // 等待7秒，应该执行3-4次

        // 验证任务状态
        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskId);
        assertNotNull("任务信息不应该为空", task);
        assertTrue("任务应该正在运行", task.isActive());
        assertTrue("执行次数应该大于0", task.executionCount > 0);

        // 取消任务
        boolean cancelled = scriptScheduler.cancelTask(taskId);
        assertTrue("任务应该成功取消", cancelled);

        // 验证取消后的状态
        Thread.sleep(1000);
        task = scriptScheduler.getAllTasks().get(taskId);
        assertNull("任务应该已被移除", task);
    }
    
    @Test
    public void testTaskCancellation() throws InterruptedException {
        // 创建一个长时间运行的脚本
        String script = "console.log('可取消任务测试'); 'cancellable'";

        // 调度延迟执行（5秒后执行）
        String taskId = scriptScheduler.scheduleDelayedScript(
            script,
            5000,
            "可取消任务测试"
        );

        assertNotNull("任务ID不应该为空", taskId);

        // 等待1秒后取消
        Thread.sleep(1000);
        boolean cancelled = scriptScheduler.cancelTask(taskId);
        assertTrue("任务应该成功取消", cancelled);

        // 验证任务状态
        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskId);
        assertNull("任务应该已被移除", task);
    }
    
    @Test
    public void testMultipleTasksManagement() throws InterruptedException {
        String[] scripts = {
            "console.log('任务1'); '1'",
            "console.log('任务2'); '2'",
            "console.log('任务3'); '3'"
        };
        
        String[] taskIds = new String[scripts.length];
        
        // 创建多个任务
        for (int i = 0; i < scripts.length; i++) {
            taskIds[i] = scriptScheduler.scheduleDelayedScript(
                scripts[i], 
                (i + 1) * 1000, // 1秒、2秒、3秒后执行
                "多任务测试 " + (i + 1)
            );
            assertNotNull("任务ID " + i + " 不应该为空", taskIds[i]);
        }
        
        // 等待所有任务执行完成
        Thread.sleep(5000);
        
        // 验证所有任务状态
        for (int i = 0; i < taskIds.length; i++) {
            ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskIds[i]);
            if (task != null) {
                assertTrue("任务 " + i + " 应该已完成", task.future.isDone());
                assertTrue("任务 " + i + " 执行次数应该大于0", task.executionCount > 0);
            }
        }

        // 获取所有任务列表
        Map<String, ScriptScheduler.ScheduledTask> allTasks = scriptScheduler.getAllTasks();
        assertTrue("应该有至少3个任务", allTasks.size() >= 3);
    }
    
    @Test
    public void testTaskInfoRetrieval() {
        String script = "console.log('任务信息测试'); 'info'";
        String description = "任务信息获取测试";

        String taskId = scriptScheduler.scheduleDelayedScript(script, 3000, description);

        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskId);
        assertNotNull("任务信息不应该为空", task);
        assertEquals("任务ID应该匹配", taskId, task.taskId);
        assertEquals("任务描述应该匹配", description, task.description);
        assertEquals("任务脚本应该匹配", script, task.scriptContent);
        assertEquals("任务类型应该是DELAYED", ScriptScheduler.TaskType.DELAYED, task.type);
        assertEquals("初始执行次数应该为0", 0, task.executionCount);
        assertTrue("创建时间应该大于0", task.createdTime > 0);
    }
    
    @Test
    public void testInvalidTaskOperations() {
        // 测试无效任务ID操作
        String invalidTaskId = "invalid_task_id";

        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(invalidTaskId);
        assertNull("无效任务ID应该返回null", task);

        boolean cancelled = scriptScheduler.cancelTask(invalidTaskId);
        assertFalse("无效任务ID取消应该返回false", cancelled);
    }
    
    @Test
    public void testScriptExecutionError() throws InterruptedException {
        // 创建一个会出错的脚本
        String errorScript = "throw new Error('测试错误'); 'error'";

        String taskId = scriptScheduler.scheduleDelayedScript(
            errorScript,
            1000,
            "错误处理测试"
        );

        // 等待执行完成
        Thread.sleep(2000);

        // 验证任务状态
        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskId);
        assertNotNull("任务信息不应该为空", task);
        assertTrue("任务应该已完成", task.future.isDone());
        // 注意：当前实现中错误处理可能不会增加errorCount，这里只验证任务完成
        assertTrue("执行次数应该大于0", task.executionCount >= 0);
    }
    
    @Test
    public void testSchedulerShutdown() throws InterruptedException {
        // 创建一些任务
        String taskId1 = scriptScheduler.scheduleDelayedScript(
            "console.log('关闭测试1'); '1'", 
            2000, 
            "关闭测试1"
        );
        
        String taskId2 = scriptScheduler.schedulePeriodicScript(
            "console.log('关闭测试2'); '2'", 
            1000, 
            1000, 
            "关闭测试2"
        );
        
        // 等待一段时间
        Thread.sleep(1500);
        
        // 关闭调度器
        scriptScheduler.shutdown();
        
        // 验证任务状态
        ScriptScheduler.ScheduledTask task1 = scriptScheduler.getAllTasks().get(taskId1);
        ScriptScheduler.ScheduledTask task2 = scriptScheduler.getAllTasks().get(taskId2);

        // 关闭后任务应该被移除
        assertNull("任务1应该被移除", task1);
        assertNull("任务2应该被移除", task2);
    }
    
    @Test
    public void testTaskExecutionTimeout() throws InterruptedException {
        // 创建一个可能超时的脚本（无限循环）
        String timeoutScript = "while(true) { /* 无限循环 */ } 'timeout'";
        
        String taskId = scriptScheduler.scheduleDelayedScript(
            timeoutScript, 
            1000, 
            "超时测试"
        );
        
        // 等待足够长的时间
        Thread.sleep(5000);
        
        // 验证任务状态
        ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskId);
        assertNotNull("任务信息不应该为空", task);

        // 任务应该已完成（可能因为超时或其他原因）
        assertTrue("任务应该已完成", task.future.isDone());
    }
    
    @Test
    public void testConcurrentTaskExecution() throws InterruptedException {
        int taskCount = 5;
        String[] taskIds = new String[taskCount];
        CountDownLatch latch = new CountDownLatch(taskCount);
        
        // 创建多个并发任务
        for (int i = 0; i < taskCount; i++) {
            final int taskNum = i;
            String script = "console.log('并发任务 " + taskNum + "'); '" + taskNum + "'";
            
            taskIds[i] = scriptScheduler.scheduleDelayedScript(
                script, 
                1000, // 所有任务1秒后同时执行
                "并发测试 " + taskNum
            );
        }
        
        // 等待所有任务执行完成
        Thread.sleep(3000);
        
        // 验证所有任务都成功执行
        int completedCount = 0;
        for (String taskId : taskIds) {
            ScriptScheduler.ScheduledTask task = scriptScheduler.getAllTasks().get(taskId);
            assertNotNull("任务信息不应该为空", task);
            if (task.future.isDone()) {
                completedCount++;
            }
        }

        assertEquals("所有任务都应该完成", taskCount, completedCount);
    }
}
