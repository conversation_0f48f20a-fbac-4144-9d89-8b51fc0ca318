package com.bm.atool.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import androidx.core.app.NotificationCompat;

import com.bm.atool.Sys;

public class AccessibilityReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {

        if(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED.equals(intent.getAction())){
//            Sys.updateAccessibilityEnabled(intent.getBooleanExtra(NotificationCompat.CATEGORY_STATUS, false));
            Sys.checkUpdatePermissions();
        }
    }
}
