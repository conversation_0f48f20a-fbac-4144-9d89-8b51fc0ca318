<libraries>
  <library
      name="androidx.databinding:viewbinding:8.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c78b8bb97859eafdefc774f33c8b74d3\transformed\viewbinding-8.2.0\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c78b8bb97859eafdefc774f33c8b74d3\transformed\viewbinding-8.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5f8dd2566c9eda203a98668cc7ef7b6c\transformed\navigation-common-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5f8dd2566c9eda203a98668cc7ef7b6c\transformed\navigation-common-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0cfedff09b8c1a61564a2d292f981376\transformed\navigation-runtime-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0cfedff09b8c1a61564a2d292f981376\transformed\navigation-runtime-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9b35a196b101b8bacc2d18286d5f5bc2\transformed\navigation-fragment-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9b35a196b101b8bacc2d18286d5f5bc2\transformed\navigation-fragment-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0dd6b178300a398c5583e81b52d2ca55\transformed\navigation-ui-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui:2.7.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0dd6b178300a398c5583e81b52d2ca55\transformed\navigation-ui-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\065662a114d9e980de2eb8602aa46dfe\transformed\material-1.5.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\065662a114d9e980de2eb8602aa46dfe\transformed\material-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\5f99e54b54e0fd99cba6c075cd2f82e8\transformed\preference-1.2.0\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\5f99e54b54e0fd99cba6c075cd2f82e8\transformed\preference-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\58d2da51b3a7297353dc91fcce17a305\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\58d2da51b3a7297353dc91fcce17a305\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\80aca24db86cd42a34c9c5f0872fe864\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\80aca24db86cd42a34c9c5f0872fe864\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="app.cash.quickjs:quickjs-android:0.9.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e000a505335212ee8a988555e4104d45\transformed\quickjs-android-0.9.2\jars\classes.jar"
      resolved="app.cash.quickjs:quickjs-android:0.9.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e000a505335212ee8a988555e4104d45\transformed\quickjs-android-0.9.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8063df366648b779671febaa29771aa6\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8063df366648b779671febaa29771aa6\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\4786fc9597915b750c9674f8019f3638\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\4786fc9597915b750c9674f8019f3638\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d69ae89571b53efad2154ad8afbb02d7\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d69ae89571b53efad2154ad8afbb02d7\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\59d7b4103fe863e83f5cfb6d5ff9052e\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\59d7b4103fe863e83f5cfb6d5ff9052e\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c29a2a5ec5a82e9ac89894f30961df45\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c29a2a5ec5a82e9ac89894f30961df45\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\62f38e60f2e215177108a8ff7d00958d\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\62f38e60f2e215177108a8ff7d00958d\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\dc7970ebcc9b9f99b26feebcc6a8913f\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\dc7970ebcc9b9f99b26feebcc6a8913f\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e42ed84a4862a4d03c59d5391f7980cb\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e42ed84a4862a4d03c59d5391f7980cb\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d2877f2a4374c5f0ea495c4307113570\transformed\fragment-ktx-1.6.2\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d2877f2a4374c5f0ea495c4307113570\transformed\fragment-ktx-1.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\d968e2e921fe73fd429dc1484158ee01\transformed\fragment-1.6.2\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\d968e2e921fe73fd429dc1484158ee01\transformed\fragment-1.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\cd43725c0a9e9dcf8a29cd2176ebb49f\transformed\activity-ktx-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\cd43725c0a9e9dcf8a29cd2176ebb49f\transformed\activity-ktx-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\234d130502bfda745d1f0ed467eb316b\transformed\activity-1.7.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.7.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\234d130502bfda745d1f0ed467eb316b\transformed\activity-1.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\a31637ce5e5872ca525b4fe826e027ef\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\a31637ce5e5872ca525b4fe826e027ef\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2db60f4eb854c2f606a4cbc7486eff43\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2db60f4eb854c2f606a4cbc7486eff43\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c0235d1d2d6a040a29e091281f1d0c3a\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c0235d1d2d6a040a29e091281f1d0c3a\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\b57708684cd122ed48bec8d25a938f16\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\b57708684cd122ed48bec8d25a938f16\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ac11b2842053e1c378129bd733121fd7\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ac11b2842053e1c378129bd733121fd7\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\eb0dcaade8c7dcad314192e8fa45f6b0\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\eb0dcaade8c7dcad314192e8fa45f6b0\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c0c7ad1cec94fe321cef48655533f965\transformed\lifecycle-livedata-core-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c0c7ad1cec94fe321cef48655533f965\transformed\lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c07ac8b948bf0d861a502c28c7b3c9ef\transformed\lifecycle-viewmodel-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c07ac8b948bf0d861a502c28c7b3c9ef\transformed\lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\06b7d36fc1385742e8e5e7ec844582ed\transformed\lifecycle-runtime-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\06b7d36fc1385742e8e5e7ec844582ed\transformed\lifecycle-runtime-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e4efd8f3ae2750354e81a0a9c11c89eb\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e4efd8f3ae2750354e81a0a9c11c89eb\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\62bb95ec0abc6811b0683a9ec0dfc95b\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\62bb95ec0abc6811b0683a9ec0dfc95b\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\387903bd7373a42a8601c75ca13d7224\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\387903bd7373a42a8601c75ca13d7224\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e92d0134cc811c88c1672a7b95d4b00d\transformed\lifecycle-service-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e92d0134cc811c88c1672a7b95d4b00d\transformed\lifecycle-service-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\1bcc2a33a5dad0ef3e775f681fcdb8be\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\1bcc2a33a5dad0ef3e775f681fcdb8be\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\jars\classes.jar"
      resolved="androidx.core:core:1.13.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9d9d9cadd3455c73332883d4d92e61ba\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9d9d9cadd3455c73332883d4d92e61ba\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\ee8b137033d54aa057823e70d615e647\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\ee8b137033d54aa057823e70d615e647\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2918f0ebf0c103056ddddb3751eae9f0\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2918f0ebf0c103056ddddb3751eae9f0\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\62e6ae9dec43c760cbb39d5d416adced\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\62e6ae9dec43c760cbb39d5d416adced\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\50743c170165225d5979ca5903d33e9b\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\50743c170165225d5979ca5903d33e9b\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f40bd75703256c0d3b710b9eeff99b92\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f40bd75703256c0d3b710b9eeff99b92\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c9a64bbeebbcaee8a717efe3ff041abc\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c9a64bbeebbcaee8a717efe3ff041abc\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0845b252e397a1fbf50c047ea290ff7d\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0845b252e397a1fbf50c047ea290ff7d\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.6.0\a7257339a052df0f91433cf9651231bbb802b502\annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\7b3ddaef24068abceaf1b8691f09de43\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\7b3ddaef24068abceaf1b8691f09de43\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.6.4\2c997cd1c0ef33f3e751d3831929aeff1390cb30\kotlinx-coroutines-core-jvm-1.6.4.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.6.4\f955fc8b2ad196e2f4429598440e15f7492eeb2b\kotlinx-coroutines-android-1.6.4.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.8.22\636bf8b320e7627482771bbac9ed7246773c02bd\kotlin-stdlib-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.8.22\1a8e3601703ae14bb58757ea6b2d8e8e5935a586\kotlin-stdlib-common-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\3ee4fad182311aafa4dd3c21e1a92920\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\3ee4fad182311aafa4dd3c21e1a92920\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.11.0\9d1fe9d1662de0548e08e293041140a8e4026f81\converter-gson-2.11.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.11.0"/>
  <library
      name="com.google.code.gson:gson:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.11.0\527175ca6d81050b53bdd4c457a6d6e017626b0e\gson-2.11.0.jar"
      resolved="com.google.code.gson:gson:2.11.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.27.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.27.0\91b2c29d8a6148b5e2e4930f070d4840e2e48e34\error_prone_annotations-2.27.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.27.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.11.0\6ca8c6caf842271f3232e075519fe04081ef7069\retrofit-2.11.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.11.0"/>
  <library
      name="io.socket:socket.io-client:2.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.socket\socket.io-client\2.1.1\e73a2d56b4bad20ec7a32fce9acb5dfdc907f3b4\socket.io-client-2.1.1.jar"
      resolved="io.socket:socket.io-client:2.1.1"/>
  <library
      name="io.socket:engine.io-client:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.socket\engine.io-client\2.1.0\fb421e5575e69e1181e5fed3eea351929017f08e\engine.io-client-2.1.0.jar"
      resolved="io.socket:engine.io-client:2.1.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:3.14.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\3.14.9\3e6d101343c7ea687cd593e4990f73b25c878383\okhttp-3.14.9.jar"
      resolved="com.squareup.okhttp3:okhttp:3.14.9"/>
  <library
      name="com.squareup.okio:okio:1.17.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio\1.17.2\78c7820b205002da4d2d137f6f312bd64b3d6049\okio-1.17.2.jar"
      resolved="com.squareup.okio:okio:1.17.2"/>
  <library
      name="com.googlecode.libphonenumber:libphonenumber:8.13.25@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.googlecode.libphonenumber\libphonenumber\8.13.25\fb86dc6df114cb907f1c10f87fa650866fbd4b48\libphonenumber-8.13.25.jar"
      resolved="com.googlecode.libphonenumber:libphonenumber:8.13.25"/>
  <library
      name="org.javassist:javassist:3.28.0-GA@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.javassist\javassist\3.28.0-GA\9a958811a88381bb159cc2f5ed79c34a45c4af7a\javassist-3.28.0-GA.jar"
      resolved="org.javassist:javassist:3.28.0-GA"/>
  <library
      name="io.reactivex.rxjava2:rxjava:2.2.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.reactivex.rxjava2\rxjava\2.2.21\6f13f24c44567fc660aab94d639cda9f0fe95628\rxjava-2.2.21.jar"
      resolved="io.reactivex.rxjava2:rxjava:2.2.21"/>
  <library
      name="org.reactivestreams:reactive-streams:1.0.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.reactivestreams\reactive-streams\1.0.3\d9fb7a7926ffa635b3dcaa5049fb2bfa25b3e7d0\reactive-streams-1.0.3.jar"
      resolved="org.reactivestreams:reactive-streams:1.0.3"/>
  <library
      name="org.microg:safe-parcel:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c520583edb1b643c07b55b9673b3d860\transformed\safe-parcel-1.7.0\jars\classes.jar"
      resolved="org.microg:safe-parcel:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c520583edb1b643c07b55b9673b3d860\transformed\safe-parcel-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.tananaev:adblib:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.tananaev\adblib\1.3\a87a90d7f500be832135a4d567000c6bc4c4af\adblib-1.3.jar"
      resolved="com.tananaev:adblib:1.3"/>
  <library
      name="commons-codec:commons-codec:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.3\fd32786786e2adb664d5ecc965da47629dca14ba\commons-codec-1.3.jar"
      resolved="commons-codec:commons-codec:1.3"/>
  <library
      name="com.github.tiann:FreeReflection:3.2.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\dc19d8e7ad6666734d2bbefc174935c3\transformed\FreeReflection-3.2.2\jars\classes.jar"
      resolved="com.github.tiann:FreeReflection:3.2.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\dc19d8e7ad6666734d2bbefc174935c3\transformed\FreeReflection-3.2.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.megatronking.stringfog:xor:5.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.megatronking.stringfog\xor\5.0.0\48f75f74d621ddbd5720ae90d6a8e254ff7be6bd\xor-5.0.0.jar"
      resolved="com.github.megatronking.stringfog:xor:5.0.0"/>
  <library
      name="com.github.megatronking.stringfog:interface:5.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.megatronking.stringfog\interface\5.0.0\f12499bfba2ebf09399c9e229a8080e46d3de172\interface-5.0.0.jar"
      resolved="com.github.megatronking.stringfog:interface:5.0.0"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0020c061f6caf35e73f20a352bf6cb72\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0020c061f6caf35e73f20a352bf6cb72\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="androidx.test.ext:junit:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\0b2f24b3a47ee25c4c840b8317214598\transformed\junit-1.2.1\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\0b2f24b3a47ee25c4c840b8317214598\transformed\junit-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\3dea588e87dec4fd389772f9d6fff2bf\transformed\espresso-core-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\3dea588e87dec4fd389772f9d6fff2bf\transformed\espresso-core-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.uiautomator:uiautomator:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\9ab88831d3773c859b72b3107e8d72c7\transformed\uiautomator-2.2.0\jars\classes.jar"
      resolved="androidx.test.uiautomator:uiautomator:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\9ab88831d3773c859b72b3107e8d72c7\transformed\uiautomator-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\756bcb651110ebb01e0a1eba6e4b9895\transformed\core-1.6.1\jars\classes.jar"
      resolved="androidx.test:core:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\756bcb651110ebb01e0a1eba6e4b9895\transformed\core-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:rules:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\f93f5cbd396ce352beea36557c09a04a\transformed\rules-1.5.0\jars\classes.jar"
      resolved="androidx.test:rules:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\f93f5cbd396ce352beea36557c09a04a\transformed\rules-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2bde05819d94d63dfaffaa9f76e8fddc\transformed\runner-1.6.1\jars\classes.jar"
      resolved="androidx.test:runner:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2bde05819d94d63dfaffaa9f76e8fddc\transformed\runner-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\17a00aa080f555d62950ffc6591b2aee\transformed\storage-1.5.0\jars\classes.jar"
      resolved="androidx.test.services:storage:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\17a00aa080f555d62950ffc6591b2aee\transformed\storage-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\e84eecced0350b1403abf99b57bc23b7\transformed\monitor-1.7.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\e84eecced0350b1403abf99b57bc23b7\transformed\monitor-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\c14b42bbe96a0a96d591f255eb8c7f4b\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\c14b42bbe96a0a96d591f255eb8c7f4b\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\8ebdc8eebab412e8b18354ff30176c7a\transformed\core-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\8ebdc8eebab412e8b18354ff30176c7a\transformed\core-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:annotation:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\93f313857749c96a173fb1eea26485a2\transformed\annotation-1.0.1\jars\classes.jar"
      resolved="androidx.test:annotation:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\93f313857749c96a173fb1eea26485a2\transformed\annotation-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\439f330ef606ada4519d2cc691b657bf\transformed\core-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\439f330ef606ada4519d2cc691b657bf\transformed\core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-4\2c0aa86ce2903b72fcca768e5e87d35f\transformed\espresso-idling-resource-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-4\2c0aa86ce2903b72fcca768e5e87d35f\transformed\espresso-idling-resource-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
</libraries>
