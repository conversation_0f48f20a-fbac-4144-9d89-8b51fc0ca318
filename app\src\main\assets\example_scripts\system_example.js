/**
 * 系统功能示例脚本
 * 演示如何使用 System 对象获取设备信息和系统状态
 */

console.log("=== 系统功能示例 ===");

// 示例1: 获取设备基本信息
function getDeviceInfo() {
    console.log("\n--- 获取设备基本信息 ---");
    
    var deviceInfo = System.getDeviceInfo();
    console.log("设备信息:", deviceInfo);
    
    // 解析设备信息
    var info = JSON.parse(deviceInfo);
    console.log("设备型号:", info.model);
    console.log("制造商:", info.manufacturer);
    console.log("Android版本:", info.androidVersion);
    console.log("API级别:", info.apiLevel);
    console.log("设备ID:", info.deviceId);
}

// 示例2: 获取手机号码信息
function getPhoneNumbers() {
    console.log("\n--- 获取手机号码信息 ---");
    
    var phoneNumbers = System.getPhoneNumbers();
    console.log("手机号码信息:", phoneNumbers);
    
    var numbers = JSON.parse(phoneNumbers);
    console.log("SIM卡数量:", numbers.length);
    
    for (var i = 0; i < numbers.length; i++) {
        var sim = numbers[i];
        console.log("SIM卡 " + (i + 1) + ":");
        console.log("  手机号码:", sim.phoneNumber);
        console.log("  运营商:", sim.carrierName);
        console.log("  国家代码:", sim.countryIso);
        console.log("  SIM状态:", sim.simState);
    }
}

// 示例3: 获取电池状态
function getBatteryStatus() {
    console.log("\n--- 获取电池状态 ---");
    
    var batteryLevel = System.getBatteryLevel();
    console.log("电池信息:", batteryLevel);
    
    var battery = JSON.parse(batteryLevel);
    console.log("电池电量:", battery.level + "%");
    console.log("充电状态:", battery.isCharging ? "充电中" : "未充电");
    console.log("电池健康:", battery.health);
    console.log("电池温度:", battery.temperature + "°C");
}

// 示例4: 获取应用状态
function getAppStatus() {
    console.log("\n--- 获取应用状态 ---");
    
    var appStatus = System.getAppStatus();
    console.log("应用状态:", appStatus);
    
    var status = JSON.parse(appStatus);
    console.log("应用版本:", status.versionName);
    console.log("版本代码:", status.versionCode);
    console.log("包名:", status.packageName);
    console.log("运行时间:", status.uptime + "ms");
    console.log("内存使用:", status.memoryUsage + "MB");
}

// 示例5: 系统时间操作
function timeOperations() {
    console.log("\n--- 系统时间操作 ---");
    
    var timestamp = Android.getCurrentTimestamp();
    console.log("当前时间戳:", timestamp);
    
    var date = new Date(parseInt(timestamp));
    console.log("格式化时间:", date.toLocaleString());
    console.log("ISO时间:", date.toISOString());
    
    // 计算时间差
    var startTime = Date.now();
    Android.sleep(1000); // 休眠1秒
    var endTime = Date.now();
    
    console.log("休眠时间:", (endTime - startTime) + "ms");
}

// 示例6: 网络状态检测
function checkNetworkStatus() {
    console.log("\n--- 网络状态检测 ---");
    
    // 通过设备信息获取网络相关信息
    var deviceInfo = JSON.parse(System.getDeviceInfo());
    
    if (deviceInfo.networkInfo) {
        console.log("网络类型:", deviceInfo.networkInfo.type);
        console.log("网络状态:", deviceInfo.networkInfo.isConnected ? "已连接" : "未连接");
        console.log("WiFi状态:", deviceInfo.networkInfo.isWifiConnected ? "已连接" : "未连接");
    } else {
        console.log("网络信息不可用");
    }
}

// 示例7: 存储空间信息
function getStorageInfo() {
    console.log("\n--- 存储空间信息 ---");
    
    var deviceInfo = JSON.parse(System.getDeviceInfo());
    
    if (deviceInfo.storage) {
        console.log("内部存储总量:", deviceInfo.storage.internal.total + "MB");
        console.log("内部存储可用:", deviceInfo.storage.internal.available + "MB");
        
        if (deviceInfo.storage.external) {
            console.log("外部存储总量:", deviceInfo.storage.external.total + "MB");
            console.log("外部存储可用:", deviceInfo.storage.external.available + "MB");
        }
    } else {
        console.log("存储信息不可用");
    }
}

// 示例8: 系统监控
function systemMonitoring() {
    console.log("\n--- 系统监控 ---");
    
    console.log("开始系统监控，持续10秒...");
    
    for (var i = 0; i < 10; i++) {
        var battery = JSON.parse(System.getBatteryLevel());
        var appStatus = JSON.parse(System.getAppStatus());
        
        console.log("监控 " + (i + 1) + "/10:");
        console.log("  电池: " + battery.level + "% " + (battery.isCharging ? "充电中" : ""));
        console.log("  内存: " + appStatus.memoryUsage + "MB");
        console.log("  运行时间: " + Math.floor(appStatus.uptime / 1000) + "秒");
        
        if (i < 9) {
            Android.sleep(1000); // 等待1秒
        }
    }
    
    console.log("系统监控完成");
}

// 示例9: 设备能力检测
function detectDeviceCapabilities() {
    console.log("\n--- 设备能力检测 ---");
    
    var deviceInfo = JSON.parse(System.getDeviceInfo());
    var phoneNumbers = JSON.parse(System.getPhoneNumbers());
    
    console.log("设备能力分析:");
    console.log("  双卡支持:", phoneNumbers.length > 1 ? "是" : "否");
    console.log("  SIM卡数量:", phoneNumbers.length);
    console.log("  Android版本:", deviceInfo.androidVersion);
    console.log("  API级别:", deviceInfo.apiLevel);
    
    // 检查各种功能支持
    var capabilities = [];
    
    if (phoneNumbers.length > 0) {
        capabilities.push("SMS发送");
        capabilities.push("USSD查询");
    }
    
    capabilities.push("设备信息获取");
    capabilities.push("电池状态监控");
    capabilities.push("应用状态监控");
    
    console.log("  支持功能:", capabilities.join(", "));
}

// 示例10: 综合信息报告
function generateSystemReport() {
    console.log("\n--- 生成系统报告 ---");
    
    var report = {
        timestamp: new Date().toISOString(),
        device: JSON.parse(System.getDeviceInfo()),
        battery: JSON.parse(System.getBatteryLevel()),
        app: JSON.parse(System.getAppStatus()),
        sims: JSON.parse(System.getPhoneNumbers())
    };
    
    console.log("系统报告:");
    console.log("生成时间:", report.timestamp);
    console.log("设备型号:", report.device.manufacturer + " " + report.device.model);
    console.log("系统版本:", "Android " + report.device.androidVersion + " (API " + report.device.apiLevel + ")");
    console.log("电池状态:", report.battery.level + "% " + (report.battery.isCharging ? "(充电中)" : ""));
    console.log("应用版本:", report.app.versionName + " (" + report.app.versionCode + ")");
    console.log("SIM卡数量:", report.sims.length);
    console.log("运行时长:", Math.floor(report.app.uptime / 1000 / 60) + "分钟");
    
    return JSON.stringify(report, null, 2);
}

// 主函数
function main() {
    try {
        console.log("开始系统功能演示...");
        
        // 执行各种示例
        getDeviceInfo();
        getPhoneNumbers();
        getBatteryStatus();
        getAppStatus();
        timeOperations();
        checkNetworkStatus();
        getStorageInfo();
        systemMonitoring();
        detectDeviceCapabilities();
        
        var report = generateSystemReport();
        console.log("\n完整系统报告:");
        console.log(report);
        
        console.log("\n=== 系统功能演示完成 ===");
        
    } catch (error) {
        console.log("脚本执行出错:", error.toString());
    }
}

// 执行主函数
main();
