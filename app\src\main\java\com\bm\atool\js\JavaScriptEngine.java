package com.bm.atool.js;

import android.content.Context;
import android.util.Log;
import app.cash.quickjs.JSContext;
import app.cash.quickjs.QuickJs;
import io.socket.client.Socket;

/**
 * JavaScript 引擎类
 * 基于 app.cash.quickjs 实现
 * 提供 JavaScript 脚本执行和 Android 系统交互功能
 */
public class JavaScriptEngine {
    private static final String TAG = "JavaScriptEngine";

    /**
     * JavaScript 执行结果类
     */
    public static class ScriptResult {
        public final Object result;
        public final boolean success;
        public final String error;

        public ScriptResult(Object result) {
            this.result = result;
            this.success = true;
            this.error = null;
        }
        
        public ScriptResult(Object result, boolean success, String error) {
            this.result = result;
            this.success = success;
            this.error = error;
        }

        @Override
        public String toString() {
            return String.valueOf(result);
        }
    }
    
    // Android 上下文和 Socket 连接
    private final Context context;
    private final Socket socket;
    
    // QuickJS 相关对象
    private QuickJs quickJs;
    private JSContext jsContext;
    
    // 引擎状态
    private volatile boolean available = false;
    
    /**
     * 构造函数
     * @param context Android 上下文
     * @param socket Socket 连接（可选）
     */
    public JavaScriptEngine(Context context, Socket socket) {
        this.context = context;
        this.socket = socket;
        
        // 初始化引擎
        initializeEngine();
    }
    
    /**
     * 初始化 JavaScript 引擎
     */
    private void initializeEngine() {
        try {
            Log.d(TAG, "正在初始化 QuickJS 引擎...");
            
            // 创建 QuickJS 运行时和上下文
            quickJs = QuickJs.createRuntime();
            jsContext = quickJs.createContext();

            Log.d(TAG, "QuickJS 引擎初始化成功");

            // 绑定 Android 桥接对象
            bindAndroidBridge();
            
            available = true;
            Log.d(TAG, "JavaScript 引擎初始化完成");
            
        } catch (Exception e) {
            Log.e(TAG, "JavaScript 引擎初始化失败", e);
            available = false;
            cleanup();
        }
    }
    
    /**
     * 绑定 Android 桥接对象
     */
    private void bindAndroidBridge() {
        try {
            // 创建 Android 桥接对象
            AndroidBridge androidBridge = new AndroidBridge(context, socket);
            
            // 设置全局 Android 对象
            jsContext.set("Android", AndroidBridgeInterface.class, androidBridge);

            // 设置 console 对象
            jsContext.evaluate("var console = { " +
                    "log: function(msg) { Android.log('INFO', msg); }, " +
                    "warn: function(msg) { Android.log('WARN', msg); }, " +
                    "error: function(msg) { Android.log('ERROR', msg); }, " +
                    "debug: function(msg) { Android.log('DEBUG', msg); } " +
                    "};");
            
            Log.d(TAG, "Android 桥接对象绑定成功");
            
        } catch (Exception e) {
            Log.e(TAG, "绑定 Android 桥接对象失败", e);
            throw e;
        }
    }
    
    /**
     * 执行 JavaScript 脚本
     * @param script 脚本内容
     * @param filename 文件名（用于调试）
     * @return 执行结果
     */
    public ScriptResult executeScript(String script, String filename) {
        if (!available) {
            return new ScriptResult(null, false, "JavaScript 引擎不可用");
        }
        
        try {
            Log.d(TAG, "执行脚本: " + filename);
            Object result = jsContext.evaluate(script, filename);
            return new ScriptResult(result);
            
        } catch (Exception e) {
            Log.e(TAG, "脚本执行失败: " + filename, e);
            return new ScriptResult(null, false, e.getMessage());
        }
    }

    /**
     * 执行 JavaScript 函数
     * @param functionName 函数名
     * @param args 参数列表
     * @return 执行结果
     */
    /**
     * 执行 JavaScript 脚本
     * @param script 脚本内容
     * @return 执行结果
     */
    public ScriptResult executeScript(String script) {
        return executeScript(script, "<script>");
    }
    
    /**
     * 执行 JavaScript 脚本（带超时）
     * @param script 脚本内容
     * @param timeout 超时时间（秒）
     * @return 执行结果
     */
    public ScriptResult executeScript(String script, int timeout) {
        // 简单实现：忽略超时参数，直接执行脚本
        // 在实际应用中，可能需要实现真正的超时机制
        return executeScript(script, "<script>");
    }
    
    /**
     * 执行 JavaScript 函数
     * @param functionName 函数名
     * @param args 参数列表
     * @return 执行结果
     */
    public ScriptResult executeFunction(String functionName, Object... args) {
        if (!available) {
            return new ScriptResult(null, false, "JavaScript 引擎不可用");
        }

        try {
            Log.d(TAG, "执行函数: " + functionName);
            // Construct the function call string. This is a basic implementation.
            // For more complex scenarios, a more robust method might be needed.
            StringBuilder scriptBuilder = new StringBuilder();
            scriptBuilder.append(functionName).append("(");
            for (int i = 0; i < args.length; i++) {
                // Basic handling for string arguments, needs more robust quoting for complex strings
                if (args[i] instanceof String) {
                    scriptBuilder.append("'").append(args[i]).append("'");
                } else {
                    scriptBuilder.append(args[i]);
                }
                if (i < args.length - 1) {
                    scriptBuilder.append(", ");
                }
            }
            scriptBuilder.append(")");
            
            Object result = jsContext.evaluate(scriptBuilder.toString(), functionName + "()");
            return new ScriptResult(result);

        } catch (Exception e) {
            Log.e(TAG, "函数执行失败: " + functionName, e);
            return new ScriptResult(null, false, e.getMessage());
        }
    }

    /**
     * 重置 JavaScript 引擎
     */
    public void reset() {
        Log.d(TAG, "重置 JavaScript 引擎...");
        cleanup(); // Close the current engine
        initializeEngine(); // Re-initialize
        Log.d(TAG, "JavaScript 引擎重置完成");
    }
    
    /**
     * 执行 JavaScript 脚本（无返回值）
     * @param script 脚本内容
     * @param filename 文件名（用于调试）
     */
    public void executeVoidScript(String script, String filename) {
        executeScript(script, filename);
    }
    
    /**
     * 检查引擎是否可用
     * @return 是否可用
     */
    public boolean isAvailable() {
        return available;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        try {
            if (jsContext != null) {
                jsContext.close();
                jsContext = null;
            }
            if (quickJs != null) {
                quickJs.close();
                quickJs = null;
            }
            available = false;
            Log.d(TAG, "JavaScript 引擎资源清理完成");
            
        } catch (Exception e) {
            Log.e(TAG, "清理 JavaScript 引擎资源时出错", e);
        }
    }
    
    /**
     * 获取上下文
     * @return Android 上下文
     */
    public Context getContext() {
        return context;
    }
    
    /**
     * 获取 Socket 连接
     * @return Socket 连接
     */
    public Socket getSocket() {
        return socket;
    }
    
    /**
     * 关闭引擎（兼容旧 API）
     */
    public void close() {
        cleanup();
    }
}
