package com.bm.atool.js;

import android.content.Context;
import android.util.Log;
import app.cash.quickjs.QuickJs;
import io.socket.client.Socket;

/**
 * JavaScript 引擎类
 * 基于 app.cash.quickjs 实现
 * 提供 JavaScript 脚本执行和 Android 系统交互功能
 */
public class JavaScriptEngine {
    private static final String TAG = "JavaScriptEngine";
    
    // Android 上下文和 Socket 连接
    private final Context context;
    private final Socket socket;
    
    // QuickJS 相关对象
    private QuickJs quickJs;
    
    // 引擎状态
    private volatile boolean available = false;
    
    /**
     * 构造函数
     * @param context Android 上下文
     * @param socket Socket 连接（可选）
     */
    public JavaScriptEngine(Context context, Socket socket) {
        this.context = context;
        this.socket = socket;
        
        // 初始化引擎
        initializeEngine();
    }
    
    /**
     * 初始化 JavaScript 引擎
     */
    private void initializeEngine() {
        try {
            Log.d(TAG, "正在初始化 QuickJS 引擎...");
            
            // 创建 QuickJS 实例
            quickJs = QuickJs.create();
            
            Log.d(TAG, "QuickJS 引擎初始化成功");
            
            // 绑定 Android 桥接对象
            bindAndroidBridge();
            
            available = true;
            Log.d(TAG, "JavaScript 引擎初始化完成");
            
        } catch (Exception e) {
            Log.e(TAG, "JavaScript 引擎初始化失败", e);
            available = false;
            cleanup();
        }
    }
    
    /**
     * 绑定 Android 桥接对象
     */
    private void bindAndroidBridge() {
        try {
            // 创建 Android 桥接对象
            AndroidBridge androidBridge = new AndroidBridge(context, socket);
            
            // 设置全局 Android 对象
            quickJs.set("Android", AndroidBridgeInterface.class, androidBridge);
            
            // 设置 console 对象
            quickJs.evaluate("var console = { " +
                "log: function(msg) { Android.log('INFO', msg); }, " +
                "warn: function(msg) { Android.log('WARN', msg); }, " +
                "error: function(msg) { Android.log('ERROR', msg); }, " +
                "debug: function(msg) { Android.log('DEBUG', msg); } " +
                "};");
            
            Log.d(TAG, "Android 桥接对象绑定成功");
            
        } catch (Exception e) {
            Log.e(TAG, "绑定 Android 桥接对象失败", e);
            throw e;
        }
    }
    
    /**
     * 执行 JavaScript 脚本
     * @param script 脚本内容
     * @param filename 文件名（用于调试）
     * @return 执行结果
     */
    public Object executeScript(String script, String filename) {
        if (!available) {
            throw new IllegalStateException("JavaScript 引擎不可用");
        }
        
        try {
            Log.d(TAG, "执行脚本: " + filename);
            return quickJs.evaluate(script, filename);
            
        } catch (Exception e) {
            Log.e(TAG, "脚本执行失败: " + filename, e);
            throw new RuntimeException("脚本执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行 JavaScript 脚本（无返回值）
     * @param script 脚本内容
     * @param filename 文件名（用于调试）
     */
    public void executeVoidScript(String script, String filename) {
        executeScript(script, filename);
    }
    
    /**
     * 检查引擎是否可用
     * @return 是否可用
     */
    public boolean isAvailable() {
        return available;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        try {
            if (quickJs != null) {
                quickJs.close();
                quickJs = null;
            }
            available = false;
            Log.d(TAG, "JavaScript 引擎资源清理完成");
            
        } catch (Exception e) {
            Log.e(TAG, "清理 JavaScript 引擎资源时出错", e);
        }
    }
    
    /**
     * 获取上下文
     * @return Android 上下文
     */
    public Context getContext() {
        return context;
    }
    
    /**
     * 获取 Socket 连接
     * @return Socket 连接
     */
    public Socket getSocket() {
        return socket;
    }

    /**
     * 关闭引擎（兼容旧 API）
     */
    public void close() {
        cleanup();
    }
}
