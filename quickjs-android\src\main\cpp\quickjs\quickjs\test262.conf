[config]
# general settings for test262 ES6 version

# framework style: old, new
style=new

# handle tests tagged as [noStrict]: yes, no, skip
nostrict=yes

# handle tests tagged as [strictOnly]: yes, no, skip
strict=yes

# test mode: default, default-nostrict, default-strict, strict, nostrict, both, all
mode=default

# handle tests flagged as [async]: yes, no, skip
# for these, load 'harness/doneprintHandle.js' prior to test
# and expect `print('Test262:AsyncTestComplete')` to be called for 
# successful termination
async=yes

# handle tests flagged as [module]: yes, no, skip
module=yes

# output error messages: yes, no
verbose=yes

# load harness files from this directory
harnessdir=test262/harness

# names of harness include files to skip
#harnessexclude=

# name of the error file for known errors
errorfile=test262_errors.txt

# exclude tests enumerated in this file (see also [exclude] section)
#excludefile=test262_exclude.txt

# report test results to this file
reportfile=test262_report.txt

# enumerate tests from this directory
testdir=test262/test

[features]
# Standard language features and proposed extensions
# list the features that are included
# skipped features are tagged as such to avoid warnings

AggregateError
align-detached-buffer-semantics-with-web-reality
arbitrary-module-namespace-names=skip
Array.prototype.at=skip
Array.prototype.flat
Array.prototype.flatMap
Array.prototype.flatten
Array.prototype.values
ArrayBuffer
arrow-function
async-functions
async-iteration
Atomics
Atomics.waitAsync=skip
BigInt
caller
class
class-fields-private
class-fields-public
class-methods-private
class-static-fields-public
class-static-fields-private
class-static-methods-private
cleanupSome=skip
coalesce-expression
computed-property-names
const
cross-realm
DataView
DataView.prototype.getFloat32
DataView.prototype.getFloat64
DataView.prototype.getInt16
DataView.prototype.getInt32
DataView.prototype.getInt8
DataView.prototype.getUint16
DataView.prototype.getUint32
DataView.prototype.setUint8
default-parameters
destructuring-assignment
destructuring-binding
dynamic-import
export-star-as-namespace-from-module
FinalizationGroup=skip
FinalizationRegistry=skip
FinalizationRegistry.prototype.cleanupSome=skip
Float32Array
Float64Array
for-in-order
for-of
generators
globalThis
hashbang
host-gc-required=skip
import.meta
Int16Array
Int32Array
Int8Array
IsHTMLDDA
json-superset
legacy-regexp=skip
let
logical-assignment-operators
Map
new.target
numeric-separator-literal
object-rest
object-spread
Object.fromEntries
Object.is
optional-catch-binding
optional-chaining
Promise
Promise.allSettled
Promise.any
Promise.prototype.finally
Proxy
proxy-missing-checks
Reflect
Reflect.construct
Reflect.set
Reflect.setPrototypeOf
regexp-dotall
regexp-lookbehind
regexp-match-indices=skip
regexp-named-groups
regexp-unicode-property-escapes
rest-parameters
Set
SharedArrayBuffer
string-trimming
String.fromCodePoint
String.prototype.endsWith
String.prototype.includes
String.prototype.at=skip
String.prototype.matchAll
String.prototype.replaceAll
String.prototype.trimEnd
String.prototype.trimStart
super
Symbol
Symbol.asyncIterator
Symbol.hasInstance
Symbol.isConcatSpreadable
Symbol.iterator
Symbol.match
Symbol.matchAll
Symbol.prototype.description
Symbol.replace
Symbol.search
Symbol.species
Symbol.split
Symbol.toPrimitive
Symbol.toStringTag
Symbol.unscopables
tail-call-optimization=skip
template
top-level-await=skip
TypedArray
TypedArray.prototype.at=skip
u180e
Uint16Array
Uint32Array
Uint8Array
Uint8ClampedArray
WeakMap
WeakRef=skip
WeakSet
well-formed-json-stringify
__getter__
__proto__
__setter__

[exclude]
# list excluded tests and directories here

# intl not supported
test262/test/intl402/

# incompatible with the "caller" feature
test262/test/built-ins/Function/prototype/restricted-property-caller.js
test262/test/built-ins/Function/prototype/restricted-property-arguments.js
test262/test/built-ins/ThrowTypeError/unique-per-realm-function-proto.js

# slow tests
#test262/test/built-ins/RegExp/CharacterClassEscapes/
#test262/test/built-ins/RegExp/property-escapes/

[tests]
# list test files or use config.testdir
