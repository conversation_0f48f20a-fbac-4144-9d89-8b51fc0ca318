package com.bm.atool.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * JavaScript执行响应模型
 */
public class JavaScriptResponse implements Serializable {
    @SerializedName("id")
    public String id;
    
    @SerializedName("success")
    public boolean success;
    
    @SerializedName("result")
    public String result;
    
    @SerializedName("error")
    public String error;
    
    @SerializedName("executionTime")
    public long executionTime; // 执行时间（毫秒）
    
    @SerializedName("timestamp")
    public long timestamp = System.currentTimeMillis();
}
