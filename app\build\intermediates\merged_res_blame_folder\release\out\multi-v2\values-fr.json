{"logs": [{"outputFile": "com.bm.atool.app-mergeReleaseResources-43:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,419,549,634,700,797,880,946,1048,1123,1179,1258,1318,1372,1494,1553,1615,1669,1751,1886,1978,2062,2176,2255,2336,2429,2496,2562,2642,2723,2826,2899,2977,3050,3122,3215,3287,3379,3471,3545,3629,3721,3778,3844,3927,4014,4076,4140,4203,4305,4403,4500,4601", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "233,316,414,544,629,695,792,875,941,1043,1118,1174,1253,1313,1367,1489,1548,1610,1664,1746,1881,1973,2057,2171,2250,2331,2424,2491,2557,2637,2718,2821,2894,2972,3045,3117,3210,3282,3374,3466,3540,3624,3716,3773,3839,3922,4009,4071,4135,4198,4300,4398,4495,4596,4685"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3030,3840,3938,4068,4223,4386,4483,4566,4632,4734,4809,4865,4944,5004,5058,5180,5239,5301,5355,5437,5572,5664,5748,5862,5941,6022,6115,6182,6248,6328,6409,6512,6585,6663,6736,6808,6901,6973,7065,7157,7231,7315,7407,7464,7530,7613,7700,7762,7826,7889,7991,8089,8186,8594", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,71,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "283,3108,3933,4063,4148,4284,4478,4561,4627,4729,4804,4860,4939,4999,5053,5175,5234,5296,5350,5432,5567,5659,5743,5857,5936,6017,6110,6177,6243,6323,6404,6507,6580,6658,6731,6803,6896,6968,7060,7152,7226,7310,7402,7459,7525,7608,7695,7757,7821,7884,7986,8084,8181,8282,8678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,399,514,624,706,812,942,1020,1096,1187,1280,1378,1473,1573,1666,1759,1854,1945,2036,2122,2232,2343,2446,2557,2665,2772,2931,8825", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "394,509,619,701,807,937,1015,1091,1182,1275,1373,1468,1568,1661,1754,1849,1940,2031,2117,2227,2338,2441,2552,2660,2767,2926,3025,8907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4153,4289,8516,8683,9013,9182,9268", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "4218,4381,8589,8820,9177,9263,9343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3113,3211,3313,3412,3514,3618,3722,8912", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3206,3308,3407,3509,3613,3717,3835,9008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,125", "endOffsets": "153,279"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8287,8390", "endColumns": "102,125", "endOffsets": "8385,8511"}}]}]}