/**
 * SMS 功能示例脚本
 * 演示如何使用 SMS 对象发送短信和清理短信
 */

console.log("=== SMS 功能示例 ===");

// 获取设备信息
var deviceInfo = System.getDeviceInfo();
console.log("设备信息:", deviceInfo);

// 获取手机号码列表
var phoneNumbers = System.getPhoneNumbers();
console.log("可用手机号码:", phoneNumbers);

// 示例1: 发送简单短信
function sendSimpleSMS() {
    console.log("\n--- 发送简单短信 ---");
    
    var result = SMS.send("1234567890", "Hello from JavaScript!", null);
    console.log("发送结果:", result);
    
    if (result.success) {
        console.log("短信发送成功!");
        console.log("消息ID:", result.messageId);
    } else {
        console.log("短信发送失败:", result.error);
    }
}

// 示例2: 使用指定SIM卡发送短信
function sendSMSWithSpecificSIM() {
    console.log("\n--- 使用指定SIM卡发送短信 ---");
    
    var phoneNumbers = JSON.parse(System.getPhoneNumbers());
    if (phoneNumbers.length > 0) {
        var targetPhone = phoneNumbers[0].phoneNumber;
        console.log("使用SIM卡:", targetPhone);
        
        var result = SMS.send("1234567890", "Hello from SIM: " + targetPhone, targetPhone);
        console.log("发送结果:", result);
    } else {
        console.log("没有可用的SIM卡");
    }
}

// 示例3: 批量发送短信
function sendBatchSMS() {
    console.log("\n--- 批量发送短信 ---");
    
    var contacts = [
        "1234567890",
        "0987654321",
        "1111111111"
    ];
    
    var message = "这是一条批量发送的测试短信 - " + new Date().toLocaleString();
    
    for (var i = 0; i < contacts.length; i++) {
        var contact = contacts[i];
        console.log("发送给:", contact);
        
        var result = SMS.send(contact, message, null);
        if (result.success) {
            console.log("✓ 发送成功");
        } else {
            console.log("✗ 发送失败:", result.error);
        }
        
        // 延迟1秒避免发送过快
        Android.sleep(1000);
    }
}

// 示例4: 清理指定号码的短信
function clearSMSFromNumber() {
    console.log("\n--- 清理指定号码的短信 ---");
    
    var fromNumber = "1234567890";
    var result = SMS.clear(fromNumber);
    console.log("清理结果:", result);
    
    if (result.success) {
        console.log("成功清理来自", fromNumber, "的短信");
        console.log("清理数量:", result.count);
    } else {
        console.log("清理失败:", result.error);
    }
}

// 示例5: 发送包含特殊字符的短信
function sendSMSWithSpecialChars() {
    console.log("\n--- 发送包含特殊字符的短信 ---");
    
    var message = "测试消息 🚀\n包含换行符和emoji\n时间: " + new Date().toISOString();
    var result = SMS.send("1234567890", message, null);
    
    console.log("发送结果:", result);
}

// 示例6: 错误处理演示
function demonstrateErrorHandling() {
    console.log("\n--- 错误处理演示 ---");
    
    // 尝试发送到无效号码
    var result1 = SMS.send("", "测试消息", null);
    console.log("空号码发送结果:", result1);
    
    // 尝试发送空消息
    var result2 = SMS.send("1234567890", "", null);
    console.log("空消息发送结果:", result2);
    
    // 尝试使用无效SIM卡
    var result3 = SMS.send("1234567890", "测试消息", "invalid_sim");
    console.log("无效SIM卡发送结果:", result3);
}

// 主函数
function main() {
    try {
        console.log("开始SMS功能演示...");
        
        // 执行各种示例
        sendSimpleSMS();
        sendSMSWithSpecificSIM();
        sendBatchSMS();
        clearSMSFromNumber();
        sendSMSWithSpecialChars();
        demonstrateErrorHandling();
        
        console.log("\n=== SMS 功能演示完成 ===");
        
    } catch (error) {
        console.log("脚本执行出错:", error.toString());
    }
}

// 执行主函数
main();
