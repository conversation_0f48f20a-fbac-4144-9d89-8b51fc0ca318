package com.bm.atool.js;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * JavaScript引擎修复验证测试
 * 验证接口绑定修复是否正确
 */
public class JavaScriptEngineFixTest {

    @Test
    public void testAndroidBridgeInterfaceExists() {
        // 测试AndroidBridgeInterface接口是否存在
        try {
            Class<?> interfaceClass = AndroidBridgeInterface.class;
            assertNotNull("AndroidBridgeInterface接口应该存在", interfaceClass);
            assertTrue("AndroidBridgeInterface应该是接口", interfaceClass.isInterface());
            
            // 检查接口方法
            assertNotNull("log方法应该存在", interfaceClass.getMethod("log", String.class, String.class));
            assertNotNull("getCurrentTimestamp方法应该存在", interfaceClass.getMethod("getCurrentTimestamp"));
            assertNotNull("getDeviceInfo方法应该存在", interfaceClass.getMethod("getDeviceInfo"));
            assertNotNull("sendSms方法应该存在", interfaceClass.getMethod("sendSms", String.class, String.class, String.class));
            
        } catch (Exception e) {
            fail("AndroidBridgeInterface接口测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testAndroidBridgeImplementsInterface() {
        // 测试AndroidBridge是否实现了接口
        try {
            AndroidBridge bridge = new AndroidBridge(null, null);
            assertNotNull("AndroidBridge应该可以创建", bridge);
            
            // 验证是否实现了接口
            assertTrue("AndroidBridge应该实现AndroidBridgeInterface", 
                bridge instanceof AndroidBridgeInterface);
            
            // 测试接口方法是否可调用
            String timestamp = bridge.getCurrentTimestamp();
            assertNotNull("时间戳不应该为空", timestamp);
            assertTrue("时间戳应该是数字", timestamp.matches("\\d+"));
            assertTrue("时间戳应该大于0", Long.parseLong(timestamp) > 0);
            
            // 测试log方法
            bridge.log("INFO", "测试日志");
            
        } catch (Exception e) {
            fail("AndroidBridge实现测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testInterfaceMethodSignatures() {
        // 测试接口方法签名是否正确
        try {
            Class<?> interfaceClass = AndroidBridgeInterface.class;
            
            // 检查log方法签名
            java.lang.reflect.Method logMethod = interfaceClass.getMethod("log", String.class, String.class);
            assertEquals("log方法返回类型应该是void", void.class, logMethod.getReturnType());

            // 检查getCurrentTimestamp方法签名
            java.lang.reflect.Method timestampMethod = interfaceClass.getMethod("getCurrentTimestamp");
            assertEquals("getCurrentTimestamp方法返回类型应该是String", String.class, timestampMethod.getReturnType());

            // 检查sendSms方法签名
            java.lang.reflect.Method smsMethod = interfaceClass.getMethod("sendSms", String.class, String.class, String.class);
            assertEquals("sendSms方法返回类型应该是String", String.class, smsMethod.getReturnType());
            
        } catch (Exception e) {
            fail("接口方法签名测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testQuickJSCompatibleTypes() {
        // 测试所有方法都使用QuickJS兼容的类型
        try {
            AndroidBridge bridge = new AndroidBridge(null, null);
            
            // 测试String类型返回值
            String timestamp = bridge.getCurrentTimestamp();
            assertTrue("时间戳应该是String类型", timestamp instanceof String);
            
            String deviceInfo = bridge.getDeviceInfo();
            assertTrue("设备信息应该是String类型", deviceInfo instanceof String);
            
            String phoneNumbers = bridge.getPhoneNumbers();
            assertTrue("手机号码应该是String类型", phoneNumbers instanceof String);
            
            String batteryLevel = bridge.getBatteryLevel();
            assertTrue("电池电量应该是String类型", batteryLevel instanceof String);
            
            String appStatus = bridge.getAppStatus();
            assertTrue("应用状态应该是String类型", appStatus instanceof String);
            
        } catch (Exception e) {
            fail("QuickJS兼容类型测试失败: " + e.getMessage());
        }
    }
}
