// JavaScript引擎集成测试脚本
// 这个脚本可以通过WebSocket发送到Android应用进行测试

// 测试基本JavaScript功能
console.log("=== JavaScript引擎集成测试 ===");

// 1. 基本计算测试
var result1 = 1 + 2 * 3;
console.log("基本计算测试: 1 + 2 * 3 = " + result1);

// 2. 字符串操作测试
var str = "Hello, QuickJS!";
console.log("字符串测试: " + str.toUpperCase());

// 3. 数组操作测试
var arr = [1, 2, 3, 4, 5];
var sum = arr.reduce(function(a, b) { return a + b; }, 0);
console.log("数组求和测试: " + sum);

// 4. 对象操作测试
var obj = {
    name: "Android Tool",
    version: "2.0",
    features: ["SMS", "USSD", "JavaScript"]
};
console.log("对象测试: " + JSON.stringify(obj));

// 5. 函数定义和调用测试
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2);
}

var fib10 = fibonacci(10);
console.log("斐波那契数列第10项: " + fib10);

// 6. 异常处理测试
try {
    var invalidJson = JSON.parse("invalid json");
} catch (e) {
    console.log("异常处理测试: 捕获到异常 - " + e.message);
}

// 7. Android API测试（如果可用）
if (typeof Android !== 'undefined') {
    console.log("Android API可用，开始测试...");
    
    try {
        var timestamp = Android.getCurrentTimestamp();
        console.log("当前时间戳: " + timestamp);
    } catch (e) {
        console.error("时间戳获取失败: " + e.message);
    }
    
    try {
        var deviceInfo = System.getDeviceInfo();
        console.log("设备信息: " + deviceInfo);
    } catch (e) {
        console.error("设备信息获取失败: " + e.message);
    }
} else {
    console.log("Android API不可用（可能在非Android环境中运行）");
}

console.log("=== 测试完成 ===");

// 返回测试结果
"JavaScript引擎集成测试完成！所有基本功能正常。";
