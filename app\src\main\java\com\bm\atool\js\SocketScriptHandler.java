package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;

import org.json.JSONObject;

import io.socket.client.Socket;
import io.socket.emitter.Emitter;

/**
 * Socket脚本处理器
 * 处理通过Socket接收的脚本执行请求
 */
public class SocketScriptHandler {
    private static final String TAG = "SocketScriptHandler";
    
    private Context context;
    private Socket socket;
    private JavaScriptEngine jsEngine;
    private ScriptManager scriptManager;
    private ScriptDownloader scriptDownloader;
    private ScriptScheduler scriptScheduler;
    private Gson gson;
    
    public SocketScriptHandler(Context context, Socket socket, JavaScriptEngine jsEngine) {
        this.context = context;
        this.socket = socket;
        this.jsEngine = jsEngine;
        this.scriptManager = new ScriptManager(context);
        this.scriptDownloader = new ScriptDownloader(context, socket);
        this.scriptScheduler = new ScriptScheduler(context, socket, jsEngine);
        this.gson = new Gson();
        
        setupSocketListeners();
    }
    
    /**
     * 设置Socket监听器
     */
    private void setupSocketListeners() {
        if (socket == null) {
            Log.w(TAG, "Socket为空，无法设置监听器");
            return;
        }
        
        // 监听脚本执行请求
        socket.on("execute_script", onExecuteScript);
        
        // 监听脚本下载响应
        socket.on("script_response", onScriptResponse);
        
        // 监听延迟脚本执行请求
        socket.on("schedule_script", onScheduleScript);
        
        // 监听取消任务请求
        socket.on("cancel_task", onCancelTask);
        
        // 监听脚本管理请求
        socket.on("script_management", onScriptManagement);
        
        Log.i(TAG, "Socket脚本监听器已设置");
    }
    
    /**
     * 处理脚本执行请求
     */
    private Emitter.Listener onExecuteScript = new Emitter.Listener() {
        @Override
        public void call(Object... args) {
            try {
                if (args.length == 0) {
                    Log.w(TAG, "收到空的脚本执行请求");
                    return;
                }
                
                String requestData = args[0].toString();
                Log.d(TAG, "收到脚本执行请求: " + requestData);
                
                ScriptExecuteRequest request = gson.fromJson(requestData, ScriptExecuteRequest.class);
                if (request == null) {
                    sendErrorResponse("script_execute_response", "请求数据格式错误");
                    return;
                }
                
                // 执行脚本
                Object result;
                try {
                    if (request.scriptContent != null && !request.scriptContent.isEmpty()) {
                        // 直接执行脚本内容
                        result = jsEngine.executeScript(request.scriptContent, "socket_script.js");
                    } else if (request.scriptName != null && !request.scriptName.isEmpty()) {
                        // 执行已保存的脚本
                        String scriptContent = scriptManager.loadScript(request.scriptName);
                        if (scriptContent == null) {
                            sendErrorResponse("script_execute_response", "找不到脚本: " + request.scriptName);
                            return;
                        }
                        result = jsEngine.executeScript(scriptContent, request.scriptName + ".js");
                    } else {
                        sendErrorResponse("script_execute_response", "缺少脚本内容或脚本名称");
                        return;
                    }
                } catch (Exception e) {
                    sendErrorResponse("script_execute_response", "脚本执行失败: " + e.getMessage());
                    return;
                }
                
                // 发送执行结果
                ScriptExecuteResponse response = new ScriptExecuteResponse();
                response.requestId = request.requestId;
                response.success = true;
                response.result = result != null ? result.toString() : null;
                response.error = null;
                response.timestamp = System.currentTimeMillis();
                
                socket.emit("script_execute_response", gson.toJson(response));
                
                Log.i(TAG, "脚本执行完成，结果已发送: " + request.requestId);
                
            } catch (Exception e) {
                Log.e(TAG, "处理脚本执行请求失败", e);
                sendErrorResponse("script_execute_response", e.getMessage());
            }
        }
    };
    
    /**
     * 处理脚本下载响应
     */
    private Emitter.Listener onScriptResponse = new Emitter.Listener() {
        @Override
        public void call(Object... args) {
            try {
                if (args.length == 0) {
                    Log.w(TAG, "收到空的脚本响应");
                    return;
                }
                
                String responseData = args[0].toString();
                Log.d(TAG, "收到脚本下载响应: " + responseData.substring(0, Math.min(responseData.length(), 200)) + "...");
                
                ScriptDownloader.DownloadResult result = scriptDownloader.handleSocketScript(responseData);
                
                // 发送处理结果确认
                ScriptDownloadConfirm confirm = new ScriptDownloadConfirm();
                confirm.success = result.success;
                confirm.message = result.message;
                confirm.timestamp = System.currentTimeMillis();
                
                socket.emit("script_download_confirm", gson.toJson(confirm));
                
                Log.i(TAG, "脚本下载处理完成: " + result.message);
                
            } catch (Exception e) {
                Log.e(TAG, "处理脚本下载响应失败", e);
            }
        }
    };
    
    /**
     * 处理脚本调度请求
     */
    private Emitter.Listener onScheduleScript = new Emitter.Listener() {
        @Override
        public void call(Object... args) {
            try {
                if (args.length == 0) {
                    Log.w(TAG, "收到空的脚本调度请求");
                    return;
                }
                
                String requestData = args[0].toString();
                Log.d(TAG, "收到脚本调度请求: " + requestData);
                
                ScriptScheduleRequest request = gson.fromJson(requestData, ScriptScheduleRequest.class);
                if (request == null) {
                    sendErrorResponse("script_schedule_response", "请求数据格式错误");
                    return;
                }
                
                String taskId = null;
                
                // 根据调度类型执行
                if ("delayed".equals(request.type)) {
                    if (request.scriptContent != null && !request.scriptContent.isEmpty()) {
                        taskId = scriptScheduler.scheduleDelayedScript(request.scriptContent, request.delayMillis, request.description);
                    } else if (request.scriptName != null && !request.scriptName.isEmpty()) {
                        taskId = scriptScheduler.scheduleDelayedSavedScript(request.scriptName, request.delayMillis);
                    }
                } else if ("periodic".equals(request.type)) {
                    if (request.scriptContent != null && !request.scriptContent.isEmpty()) {
                        taskId = scriptScheduler.schedulePeriodicScript(request.scriptContent, request.delayMillis, request.periodMillis, request.description);
                    } else if (request.scriptName != null && !request.scriptName.isEmpty()) {
                        taskId = scriptScheduler.schedulePeriodicSavedScript(request.scriptName, request.delayMillis, request.periodMillis);
                    }
                }
                
                // 发送调度结果
                ScriptScheduleResponse response = new ScriptScheduleResponse();
                response.requestId = request.requestId;
                response.success = taskId != null;
                response.taskId = taskId;
                response.message = taskId != null ? "脚本调度成功" : "脚本调度失败";
                response.timestamp = System.currentTimeMillis();
                
                socket.emit("script_schedule_response", gson.toJson(response));
                
                Log.i(TAG, "脚本调度处理完成: " + response.message + ", 任务ID: " + taskId);
                
            } catch (Exception e) {
                Log.e(TAG, "处理脚本调度请求失败", e);
                sendErrorResponse("script_schedule_response", e.getMessage());
            }
        }
    };
    
    /**
     * 处理取消任务请求
     */
    private Emitter.Listener onCancelTask = new Emitter.Listener() {
        @Override
        public void call(Object... args) {
            try {
                if (args.length == 0) {
                    Log.w(TAG, "收到空的取消任务请求");
                    return;
                }
                
                String requestData = args[0].toString();
                Log.d(TAG, "收到取消任务请求: " + requestData);
                
                JSONObject request = new JSONObject(requestData);
                String taskId = request.optString("taskId");
                String requestId = request.optString("requestId");
                
                boolean cancelled = scriptScheduler.cancelTask(taskId);
                
                // 发送取消结果
                JSONObject response = new JSONObject();
                response.put("requestId", requestId);
                response.put("success", cancelled);
                response.put("taskId", taskId);
                response.put("message", cancelled ? "任务已取消" : "任务取消失败");
                response.put("timestamp", System.currentTimeMillis());
                
                socket.emit("cancel_task_response", response.toString());
                
                Log.i(TAG, "任务取消处理完成: " + taskId + ", 成功=" + cancelled);
                
            } catch (Exception e) {
                Log.e(TAG, "处理取消任务请求失败", e);
            }
        }
    };
    
    /**
     * 处理脚本管理请求
     */
    private Emitter.Listener onScriptManagement = new Emitter.Listener() {
        @Override
        public void call(Object... args) {
            try {
                if (args.length == 0) {
                    Log.w(TAG, "收到空的脚本管理请求");
                    return;
                }
                
                String requestData = args[0].toString();
                Log.d(TAG, "收到脚本管理请求: " + requestData);
                
                JSONObject request = new JSONObject(requestData);
                String action = request.optString("action");
                String requestId = request.optString("requestId");
                
                JSONObject response = new JSONObject();
                response.put("requestId", requestId);
                response.put("action", action);
                response.put("timestamp", System.currentTimeMillis());
                
                switch (action) {
                    case "list_scripts":
                        response.put("success", true);
                        response.put("scripts", gson.toJson(scriptManager.getAllScripts()));
                        break;
                    case "list_tasks":
                        response.put("success", true);
                        response.put("tasks", gson.toJson(scriptScheduler.getAllTasks()));
                        break;
                    case "cleanup_tasks":
                        scriptScheduler.cleanupCompletedTasks();
                        response.put("success", true);
                        response.put("message", "已清理完成的任务");
                        break;
                    default:
                        response.put("success", false);
                        response.put("message", "未知的管理操作: " + action);
                        break;
                }
                
                socket.emit("script_management_response", response.toString());
                
                Log.i(TAG, "脚本管理请求处理完成: " + action);
                
            } catch (Exception e) {
                Log.e(TAG, "处理脚本管理请求失败", e);
            }
        }
    };
    
    /**
     * 发送错误响应
     */
    private void sendErrorResponse(String event, String error) {
        try {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("error", error);
            response.put("timestamp", System.currentTimeMillis());
            
            socket.emit(event, response.toString());
        } catch (Exception e) {
            Log.e(TAG, "发送错误响应失败", e);
        }
    }
    
    /**
     * 关闭处理器
     */
    public void close() {
        try {
            if (socket != null) {
                socket.off("execute_script", onExecuteScript);
                socket.off("script_response", onScriptResponse);
                socket.off("schedule_script", onScheduleScript);
                socket.off("cancel_task", onCancelTask);
                socket.off("script_management", onScriptManagement);
            }
            
            if (scriptDownloader != null) {
                scriptDownloader.close();
            }
            
            if (scriptScheduler != null) {
                scriptScheduler.shutdown();
            }
            
            Log.i(TAG, "Socket脚本处理器已关闭");
        } catch (Exception e) {
            Log.e(TAG, "关闭Socket脚本处理器失败", e);
        }
    }
    
    // 请求和响应数据类
    public static class ScriptExecuteRequest {
        public String requestId;
        public String scriptContent;
        public String scriptName;
        public int timeout;
    }
    
    public static class ScriptExecuteResponse {
        public String requestId;
        public boolean success;
        public String result;
        public String error;
        public long timestamp;
    }
    
    public static class ScriptScheduleRequest {
        public String requestId;
        public String type; // "delayed" or "periodic"
        public String scriptContent;
        public String scriptName;
        public String description;
        public long delayMillis;
        public long periodMillis;
    }
    
    public static class ScriptScheduleResponse {
        public String requestId;
        public boolean success;
        public String taskId;
        public String message;
        public long timestamp;
    }
    
    public static class ScriptDownloadConfirm {
        public boolean success;
        public String message;
        public long timestamp;
    }
}
