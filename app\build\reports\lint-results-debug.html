<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 2 errors and 193 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Sat Aug 02 22:24:33 CST 2025 by AGP (8.2.0)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#ScopedStorage"><i class="material-icons warning-icon">warning</i>Affected by scoped storage (1)</a>
      <a class="mdl-navigation__link" href="#TextViewEdits"><i class="material-icons warning-icon">warning</i>TextView should probably be an EditText instead (1)</a>
      <a class="mdl-navigation__link" href="#ApplySharedPref"><i class="material-icons warning-icon">warning</i>Use <code>apply()</code> on <code>SharedPreferences</code> (1)</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (7)</a>
      <a class="mdl-navigation__link" href="#InlinedApi"><i class="material-icons warning-icon">warning</i>Using inlined constants on older versions (4)</a>
      <a class="mdl-navigation__link" href="#BatteryLife"><i class="material-icons warning-icon">warning</i>Battery Life Issues (13)</a>
      <a class="mdl-navigation__link" href="#InflateParams"><i class="material-icons warning-icon">warning</i>Layout Inflation without a Parent (1)</a>
      <a class="mdl-navigation__link" href="#QueryPermissionsNeeded"><i class="material-icons warning-icon">warning</i>Using APIs affected by query permissions (1)</a>
      <a class="mdl-navigation__link" href="#RedundantLabel"><i class="material-icons warning-icon">warning</i>Redundant label on activity (1)</a>
      <a class="mdl-navigation__link" href="#UnspecifiedRegisterReceiverFlag"><i class="material-icons error-icon">error</i>Missing <code>registerReceiver()</code> exported flag (2)</a>
      <a class="mdl-navigation__link" href="#PxUsage"><i class="material-icons warning-icon">warning</i>Using 'px' dimension (1)</a>
      <a class="mdl-navigation__link" href="#UseAppTint"><i class="material-icons error-icon">error</i><code>app:tint</code> attribute should be used on <code>ImageView</code> and <code>ImageButton</code> (1)</a>
      <a class="mdl-navigation__link" href="#HardwareIds"><i class="material-icons warning-icon">warning</i>Hardware Id Usage (5)</a>
      <a class="mdl-navigation__link" href="#UnsafeProtectedBroadcastReceiver"><i class="material-icons warning-icon">warning</i>Unsafe Protected <code>BroadcastReceiver</code> (2)</a>
      <a class="mdl-navigation__link" href="#ExportedService"><i class="material-icons warning-icon">warning</i>Exported service does not require permission (1)</a>
      <a class="mdl-navigation__link" href="#SystemPermissionTypo"><i class="material-icons warning-icon">warning</i>Permission appears to be a standard permission with a typo (1)</a>
      <a class="mdl-navigation__link" href="#Wakelock"><i class="material-icons warning-icon">warning</i>Incorrect <code>WakeLock</code> usage (1)</a>
      <a class="mdl-navigation__link" href="#WakelockTimeout"><i class="material-icons warning-icon">warning</i>Using wakeLock without timeout (1)</a>
      <a class="mdl-navigation__link" href="#ObsoleteLayoutParam"><i class="material-icons warning-icon">warning</i>Obsolete layout params (8)</a>
      <a class="mdl-navigation__link" href="#ObsoleteSdkInt"><i class="material-icons warning-icon">warning</i>Obsolete SDK_INT Version Check (27)</a>
      <a class="mdl-navigation__link" href="#StaticFieldLeak"><i class="material-icons warning-icon">warning</i>Static Field Leaks (3)</a>
      <a class="mdl-navigation__link" href="#HandlerLeak"><i class="material-icons warning-icon">warning</i>Handler reference leaks (1)</a>
      <a class="mdl-navigation__link" href="#InefficientWeight"><i class="material-icons warning-icon">warning</i>Inefficient layout weight (1)</a>
      <a class="mdl-navigation__link" href="#Overdraw"><i class="material-icons warning-icon">warning</i>Overdraw: Painting regions more than once (4)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (22)</a>
      <a class="mdl-navigation__link" href="#UselessParent"><i class="material-icons warning-icon">warning</i>Unnecessary parent layout (1)</a>
      <a class="mdl-navigation__link" href="#RedundantNamespace"><i class="material-icons warning-icon">warning</i>Redundant namespace (6)</a>
      <a class="mdl-navigation__link" href="#UnusedNamespace"><i class="material-icons warning-icon">warning</i>Unused namespace (6)</a>
      <a class="mdl-navigation__link" href="#IconXmlAndPng"><i class="material-icons warning-icon">warning</i>Icon is specified both as <code>.xml</code> file and as a bitmap (1)</a>
      <a class="mdl-navigation__link" href="#IconColors"><i class="material-icons warning-icon">warning</i>Icon colors do not follow the recommended visual style (5)</a>
      <a class="mdl-navigation__link" href="#IconLocation"><i class="material-icons warning-icon">warning</i>Image defined in density-independent drawable folder (6)</a>
      <a class="mdl-navigation__link" href="#ButtonStyle"><i class="material-icons warning-icon">warning</i>Button should be borderless (7)</a>
      <a class="mdl-navigation__link" href="#TextFields"><i class="material-icons warning-icon">warning</i>Missing <code>inputType</code> (1)</a>
      <a class="mdl-navigation__link" href="#Autofill"><i class="material-icons warning-icon">warning</i>Use Autofill (3)</a>
      <a class="mdl-navigation__link" href="#ClickableViewAccessibility"><i class="material-icons warning-icon">warning</i>Accessibility in Custom Views (1)</a>
      <a class="mdl-navigation__link" href="#ContentDescription"><i class="material-icons warning-icon">warning</i>Image without <code>contentDescription</code> (5)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (4)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (26)</a>
      <a class="mdl-navigation__link" href="#RtlSymmetry"><i class="material-icons warning-icon">warning</i>Padding and margin symmetry (1)</a>
      <a class="mdl-navigation__link" href="#RtlHardcoded"><i class="material-icons warning-icon">warning</i>Using left/right instead of start/end attributes (11)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ScopedStorage">ScopedStorage</a>: Affected by scoped storage</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#TextViewEdits">TextViewEdits</a>: TextView should probably be an EditText instead</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ApplySharedPref">ApplySharedPref</a>: Use <code>apply()</code> on <code>SharedPreferences</code></td></tr>
<tr>
<td class="countColumn">7</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InlinedApi">InlinedApi</a>: Using inlined constants on older versions</td></tr>
<tr>
<td class="countColumn">13</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#BatteryLife">BatteryLife</a>: Battery Life Issues</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InflateParams">InflateParams</a>: Layout Inflation without a Parent</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#QueryPermissionsNeeded">QueryPermissionsNeeded</a>: Using APIs affected by query permissions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RedundantLabel">RedundantLabel</a>: Redundant label on activity</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#UnspecifiedRegisterReceiverFlag">UnspecifiedRegisterReceiverFlag</a>: Missing <code>registerReceiver()</code> exported flag</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#PxUsage">PxUsage</a>: Using 'px' dimension</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#UseAppTint">UseAppTint</a>: <code>app:tint</code> attribute should be used on <code>ImageView</code> and <code>ImageButton</code></td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Security">Security</a>
</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardwareIds">HardwareIds</a>: Hardware Id Usage</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnsafeProtectedBroadcastReceiver">UnsafeProtectedBroadcastReceiver</a>: Unsafe Protected <code>BroadcastReceiver</code></td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ExportedService">ExportedService</a>: Exported service does not require permission</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SystemPermissionTypo">SystemPermissionTypo</a>: Permission appears to be a standard permission with a typo</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Wakelock">Wakelock</a>: Incorrect <code>WakeLock</code> usage</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#WakelockTimeout">WakelockTimeout</a>: Using wakeLock without timeout</td></tr>
<tr>
<td class="countColumn">8</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteLayoutParam">ObsoleteLayoutParam</a>: Obsolete layout params</td></tr>
<tr>
<td class="countColumn">27</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteSdkInt">ObsoleteSdkInt</a>: Obsolete SDK_INT Version Check</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#StaticFieldLeak">StaticFieldLeak</a>: Static Field Leaks</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HandlerLeak">HandlerLeak</a>: Handler reference leaks</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InefficientWeight">InefficientWeight</a>: Inefficient layout weight</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Overdraw">Overdraw</a>: Overdraw: Painting regions more than once</td></tr>
<tr>
<td class="countColumn">22</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UselessParent">UselessParent</a>: Unnecessary parent layout</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RedundantNamespace">RedundantNamespace</a>: Redundant namespace</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedNamespace">UnusedNamespace</a>: Unused namespace</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability:Icons">Usability:Icons</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconXmlAndPng">IconXmlAndPng</a>: Icon is specified both as <code>.xml</code> file and as a bitmap</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconColors">IconColors</a>: Icon colors do not follow the recommended visual style</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconLocation">IconLocation</a>: Image defined in density-independent drawable folder</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability">Usability</a>
</td></tr>
<tr>
<td class="countColumn">7</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ButtonStyle">ButtonStyle</a>: Button should be borderless</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#TextFields">TextFields</a>: Missing <code>inputType</code></td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Autofill">Autofill</a>: Use Autofill</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Accessibility">Accessibility</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ClickableViewAccessibility">ClickableViewAccessibility</a>: Accessibility in Custom Views</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ContentDescription">ContentDescription</a>: Image without <code>contentDescription</code></td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">26</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization:Bidirectional Text">Internationalization:Bidirectional Text</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlSymmetry">RtlSymmetry</a>: Padding and margin symmetry</td></tr>
<tr>
<td class="countColumn">11</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlHardcoded">RtlHardcoded</a>: Using left/right instead of start/end attributes</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (31)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (39)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="ScopedStorage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ScopedStorageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Affected by scoped storage</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:29</span>: <span class="message">READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: <code>READ_MEDIA_IMAGES</code>, <code>READ_MEDIA_VIDEO</code> or <code>READ_MEDIA_AUDIO</code>.</span><br /><pre class="errorlines">
<span class="lineno">  26 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_NETWORK_STATE"</span> />
<span class="lineno">  27 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_WIFI_STATE"</span> />
<span class="lineno">  28 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.INTERNET"</span> />
<span class="caretline"><span class="lineno">  29 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.READ_EXTERNAL_STORAGE</span></span><span class="value">"</span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  30 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.POST_NOTIFICATIONS"</span> />
<span class="lineno">  31 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_MEDIA_IMAGES"</span> />
<span class="lineno">  32 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_FINE_LOCATION"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationScopedStorage" style="display: none;">
Scoped storage is enforced on Android 10+ (or Android 11+ if using <code>requestLegacyExternalStorage</code>). In particular, <code>WRITE_EXTERNAL_STORAGE</code> will no longer provide write access to all files; it will provide the equivalent of <code>READ_EXTERNAL_STORAGE</code> instead.<br/>
<br/>
As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:<br/>
* <code>android.permission.READ_MEDIA_IMAGES</code><br/>
* <code>android.permission.READ_MEDIA_VIDEO</code><br/>
* <code>android.permission.READ_MEDIA_AUDIO</code><br/>
<br/>
and then add <code>maxSdkVersion="33"</code> to the older permission. See the developer guide for how to do this: <a href="https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions">https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions</a><br/>
<br/>
The <code>MANAGE_EXTERNAL_STORAGE</code> permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at <a href="https://goo.gle/android-mediastore-createwriterequest">https://goo.gle/android-mediastore-createwriterequest</a>.<br/>
<br/>
To learn more, read these resources: Play policy: <a href="https://goo.gle/policy-storage-help">https://goo.gle/policy-storage-help</a> Allowable use cases: <a href="https://goo.gle/policy-storage-usecases">https://goo.gle/policy-storage-usecases</a><br/><div class="moreinfo">More info: <a href="https://goo.gle/android-storage-usecases">https://goo.gle/android-storage-usecases</a>
</div>To suppress this error, use the issue id "ScopedStorage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ScopedStorage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationScopedStorageLink" onclick="reveal('explanationScopedStorage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ScopedStorageCardLink" onclick="hideid('ScopedStorageCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="TextViewEdits"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="TextViewEditsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView should probably be an EditText instead</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:87</span>: <span class="message">Attribute <code>android:inputType</code> should not be used with <code>&lt;TextView></code>: Change element type to <code>&lt;EditText></code> ?</span><br /><pre class="errorlines">
<span class="lineno">  84 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  85 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  86 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  87 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"text"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  88 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"SMS:"</span>
<span class="lineno">  89 </span>                <span class="prefix">android:</span><span class="attribute">paddingStart</span>=<span class="value">"8dp"</span>
<span class="lineno">  90 </span>                <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"8dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationTextViewEdits" style="display: none;">
Using a <code>&lt;TextView></code> to input text is generally an error, you should be using <code>&lt;EditText></code> instead.  <code>EditText</code> is a subclass of <code>TextView</code>, and some of the editing support is provided by <code>TextView</code>, so it's possible to set some input-related properties on a <code>TextView</code>. However, using a <code>TextView</code> along with input attributes is usually a cut &amp; paste error. To input text you should be using <code>&lt;EditText></code>.<br/>
<br/>
This check also checks subclasses of <code>TextView</code>, such as <code>Button</code> and <code>CheckBox</code>, since these have the same issue: they should not be used with editable attributes.<br/>To suppress this error, use the issue id "TextViewEdits" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">TextViewEdits</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 7/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationTextViewEditsLink" onclick="reveal('explanationTextViewEdits');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="TextViewEditsCardLink" onclick="hideid('TextViewEditsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ApplySharedPref"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ApplySharedPrefCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use apply() on SharedPreferences</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/Sys.java">../../src/main/java/com/bm/atool/Sys.java</a>:141</span>: <span class="message">Consider using <code>apply()</code> instead; <code>commit</code> writes its data to persistent storage immediately, whereas <code>apply</code> will handle it in the background</span><br /><pre class="errorlines">
<span class="lineno"> 138 </span>        SharedPreferences.Editor editor = sp.edit();
<span class="lineno"> 139 </span>        editor.putString(<span class="string">"token"</span>, token);
<span class="lineno"> 140 </span>        editor.putString(<span class="string">"username"</span>,username);
<span class="caretline"><span class="lineno"> 141 </span>        editor.<span class="warning">commit()</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 142 </span>        loginEventSource.fire(<span class="keyword">new</span> LoginEvent(username, token));
<span class="lineno"> 143 </span>    }
<span class="lineno"> 144 </span>    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">final</span> <span class="keyword">class</span>  LoginEvent{
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationApplySharedPref" style="display: none;">
Consider using <code>apply()</code> instead of <code>commit</code> on shared preferences. Whereas <code>commit</code> blocks and writes its data to persistent storage immediately, <code>apply</code> will handle it in the background.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ApplySharedPref" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ApplySharedPref</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationApplySharedPrefLink" onclick="reveal('explanationApplySharedPref');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ApplySharedPrefCardLink" onclick="hideid('ApplySharedPrefCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/js/AndroidBridge.java">../../src/main/java/com/bm/atool/js/AndroidBridge.java</a>:49</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toUpperCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno">  46 </span><span class="javadoc">     */</span>
<span class="lineno">  47 </span>    <span class="annotation">@JavascriptInterface</span>
<span class="lineno">  48 </span>    <span class="keyword">public</span> <span class="keyword">void</span> log(String level, String message) {
<span class="caretline"><span class="lineno">  49 </span>        <span class="keyword">switch</span> (level.<span class="warning">toUpperCase</span>()) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  50 </span>            <span class="keyword">case</span> <span class="string">"ERROR"</span>:
<span class="lineno">  51 </span>                Log.e(<span class="string">"JS"</span>, message);
<span class="lineno">  52 </span>                <span class="keyword">break</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/ui/DebugFragment.java">../../src/main/java/com/bm/atool/ui/DebugFragment.java</a>:139</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 136 </span>                Object result = jsEngine.executeScript(script, <span class="string">"debug_script.js"</span>);
<span class="lineno"> 137 </span>                <span class="keyword">long</span> executionTime = System.currentTimeMillis() - startTime;
<span class="lineno"> 138 </span>
<span class="caretline"><span class="lineno"> 139 </span>                String resultText = <span class="warning">String.format(</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 140 </span>                    <span class="string">"执行时间: %dms\n成功: %s\n结果: %s"</span>,
<span class="lineno"> 141 </span>                    executionTime,
<span class="lineno"> 142 </span>                    <span class="string">"是"</span>,
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:123</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toLowerCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>      
<span class="lineno"> 121 </span>      Log.d(TAG, <span class="string">"isAccessibilitySettingsOn: Checking if "</span> + serviceId + <span class="string">" is in "</span> + enabledServices);
<span class="lineno"> 122 </span>      
<span class="caretline"><span class="lineno"> 123 </span>      <span class="keyword">boolean</span> isEnabled = enabledServices.<span class="warning">toLowerCase</span>().contains(serviceId.toLowerCase());&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>      Log.d(TAG, <span class="string">"isAccessibilitySettingsOn: Service is "</span> + (isEnabled ? <span class="string">"enabled"</span> : <span class="string">"not enabled"</span>));
<span class="lineno"> 125 </span>      <span class="keyword">return</span> isEnabled;
<span class="lineno"> 126 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:123</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toLowerCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>      
<span class="lineno"> 121 </span>      Log.d(TAG, <span class="string">"isAccessibilitySettingsOn: Checking if "</span> + serviceId + <span class="string">" is in "</span> + enabledServices);
<span class="lineno"> 122 </span>      
<span class="caretline"><span class="lineno"> 123 </span>      <span class="keyword">boolean</span> isEnabled = enabledServices.toLowerCase().contains(serviceId.<span class="warning">toLowerCase</span>());&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>      Log.d(TAG, <span class="string">"isAccessibilitySettingsOn: Service is "</span> + (isEnabled ? <span class="string">"enabled"</span> : <span class="string">"not enabled"</span>));
<span class="lineno"> 125 </span>      <span class="keyword">return</span> isEnabled;
<span class="lineno"> 126 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/js/ScriptScheduler.java">../../src/main/java/com/bm/atool/js/ScriptScheduler.java</a>:268</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 265 </span>        
<span class="lineno"> 266 </span>        <span class="annotation">@Override</span>
<span class="lineno"> 267 </span>        <span class="keyword">public</span> String toString() {
<span class="caretline"><span class="lineno"> 268 </span>            <span class="keyword">return</span> <span class="warning">String.format(<span class="string">"Task[%s] %s - %s (执行次数: %d, 成功: %d, 失败: %d)"</span>, </span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 269 </span>                taskId, type, description, executionCount, successCount, errorCount);
<span class="lineno"> 270 </span>        }
<span class="lineno"> 271 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/SocketService.java">../../src/main/java/com/bm/atool/service/SocketService.java</a>:867</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toUpperCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno">  864 </span>        <span class="keyword">if</span> (tm != <span class="keyword">null</span>) {
<span class="lineno">  865 </span>            String networkCountryIso = tm.getNetworkCountryIso();
<span class="lineno">  866 </span>            <span class="keyword">if</span> (networkCountryIso != <span class="keyword">null</span> &amp;&amp; !networkCountryIso.isEmpty()) {
<span class="caretline"><span class="lineno">  867 </span>                Log.d(TAG, <span class="string">"获取到网络国家代码: "</span> + networkCountryIso.<span class="warning">toUpperCase</span>());&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  868 </span>                <span class="keyword">return</span> networkCountryIso.toUpperCase();
<span class="lineno">  869 </span>            }
<span class="lineno">  870 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/SocketService.java">../../src/main/java/com/bm/atool/service/SocketService.java</a>:868</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toUpperCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno">  865 </span>            String networkCountryIso = tm.getNetworkCountryIso();
<span class="lineno">  866 </span>            <span class="keyword">if</span> (networkCountryIso != <span class="keyword">null</span> &amp;&amp; !networkCountryIso.isEmpty()) {
<span class="lineno">  867 </span>                Log.d(TAG, <span class="string">"获取到网络国家代码: "</span> + networkCountryIso.toUpperCase());
<span class="caretline"><span class="lineno">  868 </span>                <span class="keyword">return</span> networkCountryIso.<span class="warning">toUpperCase</span>();&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  869 </span>            }
<span class="lineno">  870 </span>        }
<span class="lineno">  871 </span>        Log.w(TAG, <span class="string">"未能获取到网络国家代码，使用默认国家代码: US"</span>);
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.US)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InlinedApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InlinedApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using inlined constants on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:50</span>: <span class="message">Field requires API level 28 (current min is 24): <code>android.Manifest.permission#FOREGROUND_SERVICE</code></span><br /><pre class="errorlines">
<span class="lineno">  47 </span>
<span class="lineno">  48 </span>  <span class="keyword">public</span> <span class="keyword">static</span> ArrayList&lt;PermissionModel> getAllPermissions(Context context){
<span class="lineno">  49 </span>      ArrayList&lt;PermissionModel> permissions = <span class="keyword">new</span> ArrayList&lt;>();
<span class="caretline"><span class="lineno">  50 </span>      permissions.add(createPermissionModel(context,<span class="string">"Foreground Service"</span>, <span class="warning">Manifest.permission.FOREGROUND_SERVICE</span>, <span class="keyword">false</span>));</span>
<span class="lineno">  51 </span>      permissions.add(createPermissionModel(context,<span class="string">"Notifications"</span>, Manifest.permission.POST_NOTIFICATIONS, <span class="keyword">false</span>));
<span class="lineno">  52 </span>
<span class="lineno">  53 </span>      permissions.add(createPermissionModel(context,<span class="string">"NotificationListener"</span>, <span class="string">"NotificationListener"</span>, <span class="keyword">false</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:51</span>: <span class="message">Field requires API level 33 (current min is 24): <code>android.Manifest.permission#POST_NOTIFICATIONS</code></span><br /><pre class="errorlines">
<span class="lineno">  48 </span>    <span class="keyword">public</span> <span class="keyword">static</span> ArrayList&lt;PermissionModel> getAllPermissions(Context context){
<span class="lineno">  49 </span>        ArrayList&lt;PermissionModel> permissions = <span class="keyword">new</span> ArrayList&lt;>();
<span class="lineno">  50 </span>        permissions.add(createPermissionModel(context,<span class="string">"Foreground Service"</span>, Manifest.permission.FOREGROUND_SERVICE, <span class="keyword">false</span>));
<span class="caretline"><span class="lineno">  51 </span>        permissions.add(createPermissionModel(context,<span class="string">"Notifications"</span>, <span class="warning">Manifest.permission.POST_NOTIFICATIONS</span>, <span class="keyword">false</span>));</span>
<span class="lineno">  52 </span>
<span class="lineno">  53 </span>        permissions.add(createPermissionModel(context,<span class="string">"NotificationListener"</span>, <span class="string">"NotificationListener"</span>, <span class="keyword">false</span>));
<span class="lineno">  54 </span><span class="comment">//        permissions.add(createPermissionModel(context,"VPN", Manifest.permission.BIND_VPN_SERVICE, false));</span></pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:59</span>: <span class="message">Field requires API level 26 (current min is 24): <code>android.Manifest.permission#READ_PHONE_NUMBERS</code></span><br /><pre class="errorlines">
<span class="lineno">  56 </span><span class="comment">//        permissions.add(createPermissionModel(context,"PRIVILEGED Phone State", "android.permission.READ_PRIVILEGED_PHONE_STATE", false));</span>
<span class="lineno">  57 </span>
<span class="lineno">  58 </span>
<span class="caretline"><span class="lineno">  59 </span>        permissions.add(createPermissionModel(context,<span class="string">"Read Phone Numbers"</span>, <span class="warning">Manifest.permission.READ_PHONE_NUMBERS</span>, <span class="keyword">false</span>));</span>
<span class="lineno">  60 </span>        permissions.add(createPermissionModel(context,<span class="string">"Receive SMS"</span>, Manifest.permission.RECEIVE_SMS, <span class="keyword">false</span>));
<span class="lineno">  61 </span>        permissions.add(createPermissionModel(context,<span class="string">"Read SMS"</span>, Manifest.permission.READ_SMS, <span class="keyword">false</span>));
<span class="lineno">  62 </span>        permissions.add(createPermissionModel(context,<span class="string">"Send SMS"</span>, Manifest.permission.SEND_SMS, <span class="keyword">false</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/Sys.java">../../src/main/java/com/bm/atool/Sys.java</a>:241</span>: <span class="message">Field requires API level 33 (current min is 26): <code>android.content.Context#RECEIVER_EXPORTED</code></span><br /><pre class="errorlines">
<span class="lineno"> 238 </span>
<span class="lineno"> 239 </span>  <span class="keyword">public</span> <span class="keyword">static</span> Intent registerReceiver(Context context, BroadcastReceiver receiver, IntentFilter filter) {
<span class="lineno"> 240 </span>      <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
<span class="caretline"><span class="lineno"> 241 </span>          <span class="keyword">return</span> context.registerReceiver(receiver, filter,<span class="warning">RECEIVER_EXPORTED</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 242 </span>      }
<span class="lineno"> 243 </span>      <span class="keyword">else</span>{
<span class="lineno"> 244 </span>          <span class="keyword">return</span> context.registerReceiver(receiver, filter);
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInlinedApi" style="display: none;">
This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that's fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it's safe and can be suppressed or whether the code needs to be guarded.<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InlinedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InlinedApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInlinedApiLink" onclick="reveal('explanationInlinedApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InlinedApiCardLink" onclick="hideid('InlinedApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="BatteryLife"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="BatteryLifeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Battery Life Issues</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:96</span>: <span class="message">Declaring a broadcastreceiver for <code>android.net.conn.CONNECTIVITY_CHANGE</code> is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use <code>WorkManager</code>.</span><br /><pre class="errorlines">
<span class="lineno">  93 </span>           <span class="tag">&lt;intent-filter></span>
<span class="lineno">  94 </span>               <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.USER_PRESENT"</span>/>
<span class="lineno">  95 </span>               <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.BOOT_COMPLETED"</span> />
<span class="caretline"><span class="lineno">  96 </span>               <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.net.conn.CONNECTIVITY_CHANGE</span></span><span class="value">"</span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  97 </span>               <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.USER_PRESENT"</span> />
<span class="lineno">  98 </span>               <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.MEDIA_MOUNTED"</span> />
<span class="lineno">  99 </span>               <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.ACTION_POWER_CONNECTED"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:110</span>: <span class="message">Declaring a broadcastreceiver for <code>android.net.conn.CONNECTIVITY_CHANGE</code> is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use <code>WorkManager</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 107 </span>            <span class="comment">&lt;!-- 手机启动 --></span>
<span class="lineno"> 108 </span>            <span class="tag">&lt;intent-filter></span>
<span class="lineno"> 109 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.BOOT_COMPLETED"</span>/>
<span class="caretline"><span class="lineno"> 110 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.net.conn.CONNECTIVITY_CHANGE</span></span><span class="value">"</span>/>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 111 </span>            <span class="tag">&lt;/intent-filter></span>
<span class="lineno"> 112 </span>            <span class="comment">&lt;!-- 软件安装卸载--></span>
<span class="lineno"> 113 </span>            <span class="tag">&lt;intent-filter></span>
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:120</span>: <span class="message">Declaring a broadcastreceiver for <code>android.net.conn.CONNECTIVITY_CHANGE</code> is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use <code>WorkManager</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 117 </span>            <span class="tag">&lt;/intent-filter></span>
<span class="lineno"> 118 </span>            <span class="comment">&lt;!-- 网络监听 --></span>
<span class="lineno"> 119 </span>            <span class="tag">&lt;intent-filter></span>
<span class="caretline"><span class="lineno"> 120 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.net.conn.CONNECTIVITY_CHANGE</span></span><span class="value">"</span>/>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 121 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.net.wifi.WIFI_STATE_CJANGED"</span>/>
<span class="lineno"> 122 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.net.wifi.STATE_CHANGE"</span>/>
<span class="lineno"> 123 </span>            <span class="tag">&lt;/intent-filter></span>
</pre>

<span class="location"><a href="../../src/main/java/com/xuexiang/keeplive/whitelist/impl/DefaultWhiteListProvider.java">../../src/main/java/com/xuexiang/keeplive/whitelist/impl/DefaultWhiteListProvider.java</a>:56</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno">  53 </span>  PowerManager pm = (PowerManager) application.getSystemService(Context.POWER_SERVICE);
<span class="lineno">  54 </span>  <span class="keyword">boolean</span> ignoringBatteryOptimizations = pm.isIgnoringBatteryOptimizations(application.getPackageName());
<span class="lineno">  55 </span>  <span class="keyword">if</span> (!ignoringBatteryOptimizations) {
<span class="caretline"><span class="lineno">  56 </span>      Intent dozeIntent = <span class="keyword">new</span> Intent(<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  57 </span>      dozeIntent.setData(Uri.parse(<span class="string">"package:"</span> + application.getPackageName()));
<span class="lineno">  58 </span>      intentWrappers.add(<span class="keyword">new</span> WhiteListIntentWrapper(dozeIntent, IntentType.DOZE));
<span class="lineno">  59 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/LoginActivity.java">../../src/main/java/com/bm/atool/LoginActivity.java</a>:100</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno">  97 </span>  List&lt;String> ungrantedRuntimePermissions = allPermissions.stream()
<span class="lineno">  98 </span>          .filter(p -> p.fullName != <span class="keyword">null</span> &amp;&amp; !p.granted 
<span class="lineno">  99 </span>                  &amp;&amp; !p.fullName.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
<span class="caretline"><span class="lineno"> 100 </span>                  &amp;&amp; !p.fullName.equals(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>)&nbsp;</span>
<span class="lineno"> 101 </span>                  &amp;&amp; !p.name.equals(<span class="string">"Accessibility"</span>))
<span class="lineno"> 102 </span>          .map(p -> p.fullName)
<span class="lineno"> 103 </span>          .collect(Collectors.toList());
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="BatteryLifeDivLink" onclick="reveal('BatteryLifeDiv');" />+ 8 More Occurrences...</button>
<div id="BatteryLifeDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/bm/atool/MainActivity.java">../../src/main/java/com/bm/atool/MainActivity.java</a>:350</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno"> 347 </span>        <span class="keyword">if</span>(permission.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)){
<span class="lineno"> 348 </span>            PermissionUtils.manageOverlayToSettingPage(<span class="keyword">this</span>);
<span class="lineno"> 349 </span>        }
<span class="caretline"><span class="lineno"> 350 </span>        <span class="keyword">else</span> <span class="keyword">if</span>(permission.equals(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>)){&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 351 </span>            PermissionUtils.batteryOptimizationToSettingPage(<span class="keyword">this</span>);
<span class="lineno"> 352 </span>        }
<span class="lineno"> 353 </span>        <span class="keyword">else</span>{
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:66</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno">  63 </span>        permissions.add(createPermissionModel(context,<span class="string">"Write SMS"</span>, <span class="string">"android.permission.WRITE_SMS"</span>, <span class="keyword">false</span>));
<span class="lineno">  64 </span>        permissions.add(createPermissionModel(context,<span class="string">"Accessibility"</span>, <span class="keyword">null</span>, <span class="keyword">false</span>));
<span class="lineno">  65 </span>        permissions.add(createPermissionModel(context,<span class="string">"Manage Overlay"</span>, Settings.ACTION_MANAGE_OVERLAY_PERMISSION, <span class="keyword">false</span>));
<span class="caretline"><span class="lineno">  66 </span>        permissions.add(createPermissionModel(context,<span class="string">"Battery Optimizations"</span>, Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>, <span class="keyword">false</span>));</span>
<span class="lineno">  67 </span><span class="comment">//        permissions.add(createPermissionModel(context,"ACCESSIBILITY_SERVICE", Manifest.permission.BIND_ACCESSIBILITY_SERVICE, false));</span>
<span class="lineno">  68 </span>        <span class="keyword">return</span> permissions;
<span class="lineno">  69 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:78</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno">  75 </span>           grantedStatus = isNotificationListenerEnabled(context);
<span class="lineno">  76 </span>       } <span class="keyword">else</span> <span class="keyword">if</span>(Settings.ACTION_MANAGE_OVERLAY_PERMISSION.equals(permission)){
<span class="lineno">  77 </span>           grantedStatus = canDrawOverlays(context);
<span class="caretline"><span class="lineno">  78 </span>       } <span class="keyword">else</span> <span class="keyword">if</span>(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>.equals(permission)){&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  79 </span>           grantedStatus = isIgnoringBatteryOptimizations(context);
<span class="lineno">  80 </span>       } <span class="keyword">else</span> {
<span class="lineno">  81 </span>           grantedStatus = isPermissionOk(context, permission);
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:199</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno"> 196 </span>  Log.d(TAG, <span class="string">"batteryOptimizationToSettingPage: Attempting to open battery optimization settings for package: "</span> + context.getPackageName());
<span class="lineno"> 197 </span>  <span class="keyword">try</span> {
<span class="lineno"> 198 </span>      <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
<span class="caretline"><span class="lineno"> 199 </span>          Intent intent = <span class="keyword">new</span> Intent(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>);&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 200 </span>          intent.setData(Uri.parse(<span class="string">"package:"</span> + context.getPackageName()));
<span class="lineno"> 201 </span>          intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
<span class="lineno"> 202 </span>          context.startActivity(intent);
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:241</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno"> 238 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (permission.fullName.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)) {
<span class="lineno"> 239 </span>      manageOverlayToSettingPage(context);
<span class="lineno"> 240 </span>      <span class="keyword">return</span>;
<span class="caretline"><span class="lineno"> 241 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (permission.fullName.equals(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>)) {</span>
<span class="lineno"> 242 </span>      batteryOptimizationToSettingPage(context);
<span class="lineno"> 243 </span>      <span class="keyword">return</span>;
<span class="lineno"> 244 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (permission.fullName != <span class="keyword">null</span>) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/ui/adapters/PermissionsAdapter.java">../../src/main/java/com/bm/atool/ui/adapters/PermissionsAdapter.java</a>:51</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno"> 48 </span>  <span class="keyword">boolean</span> isSpecialPermission = <span class="keyword">false</span>;
<span class="lineno"> 49 </span>  <span class="keyword">if</span> (<span class="string">"Accessibility"</span>.equals(permissionModel.name) ||
<span class="lineno"> 50 </span>      (permissionModel.fullName != <span class="keyword">null</span> &amp;&amp; Settings.ACTION_MANAGE_OVERLAY_PERMISSION.equals(permissionModel.fullName)) ||
<span class="caretline"><span class="lineno"> 51 </span>      (permissionModel.fullName != <span class="keyword">null</span> &amp;&amp; Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>.equals(permissionModel.fullName))) {</span>
<span class="lineno"> 52 </span>      isSpecialPermission = <span class="keyword">true</span>;
<span class="lineno"> 53 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/ui/SettingsFragment.java">../../src/main/java/com/bm/atool/ui/SettingsFragment.java</a>:75</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno">  72 </span>      Log.d(TAG, <span class="string">"requestPermissionsAutomatically: Requesting Manage Overlay permission."</span>);
<span class="lineno">  73 </span>      PermissionUtils.manageOverlayToSettingPage(getActivity());
<span class="lineno">  74 </span>      <span class="keyword">return</span>;
<span class="caretline"><span class="lineno">  75 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (permission.fullName != <span class="keyword">null</span> &amp;&amp; permission.fullName.equals(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>)) {</span>
<span class="lineno">  76 </span>      Log.d(TAG, <span class="string">"requestPermissionsAutomatically: Requesting Battery Optimizations permission."</span>);
<span class="lineno">  77 </span>      PermissionUtils.batteryOptimizationToSettingPage(getActivity());
<span class="lineno">  78 </span>      <span class="keyword">return</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/Sys.java">../../src/main/java/com/bm/atool/Sys.java</a>:68</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno">  65 </span>
<span class="lineno">  66 </span>  <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> checkUpdatePermissions(){
<span class="lineno">  67 </span>      <span class="keyword">if</span>(Sys.permissionModels.stream().anyMatch(p->{
<span class="caretline"><span class="lineno">  68 </span>          <span class="keyword">if</span>(p.fullName != <span class="keyword">null</span> &amp;&amp; p.fullName.equals(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>)</span>
<span class="lineno">  69 </span>                  &amp;&amp; p.granted != PermissionUtils.isIgnoringBatteryOptimizations(app)){
<span class="lineno">  70 </span>              <span class="keyword">return</span> <span class="keyword">true</span>;
<span class="lineno">  71 </span>          }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationBatteryLife" style="display: none;">
This issue flags code that either<br/>
* negatively affects battery life, or<br/>
* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.<br/>
<br/>
Generally, you should be using <code>WorkManager</code> instead.<br/>
<br/>
For more details on how to update your code, please see <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a><br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a>
</div>To suppress this error, use the issue id "BatteryLife" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">BatteryLife</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationBatteryLifeLink" onclick="reveal('explanationBatteryLife');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="BatteryLifeCardLink" onclick="hideid('BatteryLifeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InflateParams"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InflateParamsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Layout Inflation without a Parent</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/ui/FloatingWindow.java">../../src/main/java/com/bm/atool/ui/FloatingWindow.java</a>:88</span>: <span class="message">Avoid passing <code>null</code> as the view root (needed to resolve layout parameters on the inflated layout's root element)</span><br /><pre class="errorlines">
<span class="lineno">  85 </span>                <span class="keyword">return</span>;
<span class="lineno">  86 </span>            }
<span class="lineno">  87 </span>            layoutParam = createLayoutParams();
<span class="caretline"><span class="lineno">  88 </span>            floatRootView = LayoutInflater.from(<span class="keyword">this</span>.context).inflate(R.layout.activity_float_item, <span class="warning"><span class="keyword">null</span></span>);</span>
<span class="lineno">  89 </span>            floatRootView.setOnTouchListener(<span class="keyword">new</span> ItemViewTouchListener(layoutParam, windowManager));
<span class="lineno">  90 </span><span class="comment">//        ImageView iconLogo = floatRootView.findViewById(R.id.iconLogo);</span>
<span class="lineno">  91 </span><span class="comment">//        iconLogo.setOnClickListener(new View.OnClickListener() {</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInflateParams" style="display: none;">
When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored.<br/><div class="moreinfo">More info: <a href="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/">https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/</a>
</div>To suppress this error, use the issue id "InflateParams" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InflateParams</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInflateParamsLink" onclick="reveal('explanationInflateParams');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InflateParamsCardLink" onclick="hideid('InflateParamsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="QueryPermissionsNeeded"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="QueryPermissionsNeededCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using APIs affected by query permissions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteListIntentWrapper.java">../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteListIntentWrapper.java</a>:74</span>: <span class="message">Consider adding a <code>&lt;queries></code> declaration to your manifest when calling this method; see <a href="https://g.co/dev/packagevisibility">https://g.co/dev/packagevisibility</a> for details</span><br /><pre class="errorlines">
<span class="lineno">  71 </span><span class="javadoc">   */</span>
<span class="lineno">  72 </span>  <span class="keyword">public</span> <span class="keyword">boolean</span> doesActivityExists() {
<span class="lineno">  73 </span>      PackageManager pm = Sys.app.getPackageManager();
<span class="caretline"><span class="lineno">  74 </span>      List&lt;ResolveInfo> list = pm.<span class="warning">queryIntentActivities</span>(mIntent, PackageManager.MATCH_DEFAULT_ONLY);</span>
<span class="lineno">  75 </span>      <span class="keyword">return</span> list != <span class="keyword">null</span> &amp;&amp; list.size() > <span class="number">0</span>;
<span class="lineno">  76 </span>  }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationQueryPermissionsNeeded" style="display: none;">
Apps that target Android 11 cannot query or interact with other installed apps by default. If you need to query or interact with other installed apps, you may need to add a <code>&lt;queries></code> declaration in your manifest.<br/>
<br/>
As a corollary, the methods <code>PackageManager#getInstalledPackages</code> and <code>PackageManager#getInstalledApplications</code> will no longer return information about all installed apps. To query specific apps or types of apps, you can use methods like <code>PackageManager#getPackageInfo</code> or <code>PackageManager#queryIntentActivities</code>.<br/><div class="moreinfo">More info: <a href="https://g.co/dev/packagevisibility">https://g.co/dev/packagevisibility</a>
</div>To suppress this error, use the issue id "QueryPermissionsNeeded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">QueryPermissionsNeeded</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationQueryPermissionsNeededLink" onclick="reveal('explanationQueryPermissionsNeeded');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="QueryPermissionsNeededCardLink" onclick="hideid('QueryPermissionsNeededCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RedundantLabel"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RedundantLabelCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Redundant label on activity</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:59</span>: <span class="message">Redundant label can be removed</span><br /><pre class="errorlines">
<span class="lineno">  56 </span>        <span class="tag">&lt;activity</span><span class="attribute">
</span><span class="lineno">  57 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".MainActivity"</span>
<span class="lineno">  58 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno">  59 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  60 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.AndroidTool"</span>
<span class="lineno">  61 </span>            <span class="prefix">android:</span><span class="attribute">launchMode</span>=<span class="value">"singleTask"</span>
<span class="lineno">  62 </span>            <span class="prefix">android:</span><span class="attribute">taskAffinity</span>=<span class="value">""</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRedundantLabel" style="display: none;">
When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted.<br/>To suppress this error, use the issue id "RedundantLabel" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RedundantLabel</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRedundantLabelLink" onclick="reveal('explanationRedundantLabel');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RedundantLabelCardLink" onclick="hideid('RedundantLabelCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnspecifiedRegisterReceiverFlag"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnspecifiedRegisterReceiverFlagCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing registerReceiver() exported flag</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/androidTest/java/com/bm/atool/StressTest.java">../../src/androidTest/java/com/bm/atool/StressTest.java</a>:104</span>: <span class="message"><code>pongReceiver</code> is missing <code>RECEIVER_EXPORTED</code> or <code>RECEIVER_NOT_EXPORTED</code> flag for unprotected broadcasts registered for ACTION_SOCKET_PONG</span><br /><pre class="errorlines">
<span class="lineno"> 101 </span>            }
<span class="lineno"> 102 </span>        };
<span class="lineno"> 103 </span>        IntentFilter filter = <span class="keyword">new</span> IntentFilter(Sys.ACTION_SOCKET_PONG);
<span class="caretline"><span class="lineno"> 104 </span>        <span class="error">context.registerReceiver(pongReceiver, filter)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 105 </span>    }
<span class="lineno"> 106 </span>
<span class="lineno"> 107 </span>    <span class="annotation">@After</span></pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/Sys.java">../../src/main/java/com/bm/atool/Sys.java</a>:244</span>: <span class="message"><code>receiver</code> is missing <code>RECEIVER_EXPORTED</code> or <code>RECEIVER_NOT_EXPORTED</code> flag for unprotected broadcasts registered for an IntentFilter that cannot be inspected by lint</span><br /><pre class="errorlines">
<span class="lineno"> 241 </span>            <span class="keyword">return</span> context.registerReceiver(receiver, filter,RECEIVER_EXPORTED);
<span class="lineno"> 242 </span>        }
<span class="lineno"> 243 </span>        <span class="keyword">else</span>{
<span class="caretline"><span class="lineno"> 244 </span>            <span class="keyword">return</span> <span class="warning">context.registerReceiver(receiver, filter)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 245 </span>        }
<span class="lineno"> 246 </span>    }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnspecifiedRegisterReceiverFlag" style="display: none;">
In Android U, all receivers registering for non-system broadcasts are required to include a flag indicating the receiver's exported state. Apps registering for non-system broadcasts should use the <code>ContextCompat#registerReceiver</code> APIs with flags set to either <code>RECEIVER_EXPORTED</code> or <code>RECEIVER_NOT_EXPORTED</code>.<br/>
<br/>
If you are not expecting broadcasts from other apps on the device, register your receiver with <code>RECEIVER_NOT_EXPORTED</code> to protect your receiver on all platform releases.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)">https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)</a>
</div>To suppress this error, use the issue id "UnspecifiedRegisterReceiverFlag" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnspecifiedRegisterReceiverFlag</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnspecifiedRegisterReceiverFlagLink" onclick="reveal('explanationUnspecifiedRegisterReceiverFlag');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnspecifiedRegisterReceiverFlagCardLink" onclick="hideid('UnspecifiedRegisterReceiverFlagCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="PxUsage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="PxUsageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using 'px' dimension</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:34</span>: <span class="message">Avoid using "<code>px</code>" as units; use "<code>dp</code>" instead</span><br /><pre class="errorlines">
<span class="lineno"> 31 </span>        <span class="prefix">app:</span><span class="attribute">tabIndicatorFullWidth</span>=<span class="value">"false"</span>
<span class="lineno"> 32 </span>        <span class="prefix">app:</span><span class="attribute">tabTextColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno"> 33 </span>        <span class="prefix">app:</span><span class="attribute">tabIndicatorColor</span>=<span class="value">"@color/text_primary"</span>
<span class="caretline"><span class="lineno"> 34 </span>        <span class="warning"><span class="prefix">app:</span><span class="attribute">tabIndicatorHeight</span>=<span class="value">"4px"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 35 </span>        <span class="prefix">app:</span><span class="attribute">tabPaddingEnd</span>=<span class="value">"0dp"</span>
<span class="lineno"> 36 </span>        <span class="prefix">app:</span><span class="attribute">tabPaddingStart</span>=<span class="value">"0dp"</span>
<span class="lineno"> 37 </span>        <span class="prefix">app:</span><span class="attribute">tabPaddingTop</span>=<span class="value">"0dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationPxUsage" style="display: none;">
For performance reasons and to keep the code simpler, the Android system uses pixels as the standard unit for expressing dimension or coordinate values. That means that the dimensions of a view are always expressed in the code using pixels, but always based on the current screen density. For instance, if <code>myView.getWidth()</code> returns 10, the view is 10 pixels wide on the current screen, but on a device with a higher density screen, the value returned might be 15. If you use pixel values in your application code to work with bitmaps that are not pre-scaled for the current screen density, you might need to scale the pixel values that you use in your code to match the un-scaled bitmap source.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html#screen-independence">https://developer.android.com/guide/practices/screens_support.html#screen-independence</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "PxUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">PxUsage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationPxUsageLink" onclick="reveal('explanationPxUsage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="PxUsageCardLink" onclick="hideid('PxUsageCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseAppTint"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseAppTintCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">app:tint attribute should be used on ImageView and ImageButton</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/setting_row.xml">../../src/main/res/layout/setting_row.xml</a>:25</span>: <span class="message">Must use <code>app:tint</code> instead of <code>android:tint</code></span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>    <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 23 </span>    <span class="prefix">android:</span><span class="attribute">scaleType</span>=<span class="value">"centerInside"</span>
<span class="lineno"> 24 </span>    <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_action_ok"</span>
<span class="caretline"><span class="lineno"> 25 </span>    <span class="error"><span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"@color/check_mark_color"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>    <span class="prefix">tools:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_action_ok"</span> />
<span class="lineno"> 27 </span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;Button</span><span class="attribute">
</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseAppTint" style="display: none;">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/>To suppress this error, use the issue id "UseAppTint" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseAppTint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 1/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseAppTintLink" onclick="reveal('explanationUseAppTint');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseAppTintCardLink" onclick="hideid('UseAppTintCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Security"></a>
<a name="HardwareIds"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardwareIdsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardware Id Usage</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PhoneUtils.java">../../src/main/java/com/bm/atool/utils/PhoneUtils.java</a>:43</span>: <span class="message">Using <code>getSubscriberId</code> to get device identifiers is not recommended</span><br /><pre class="errorlines">
<span class="lineno">  40 </span>            arrayList.add(SubscriptionInfoModel.from(subscriptionInfo,telephonyManager1));
<span class="lineno">  41 </span>        }
<span class="lineno">  42 </span>    } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno">  43 </span>        <span class="keyword">if</span> (<span class="warning">telephonyManager.getSubscriberId()</span> != <span class="keyword">null</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  44 </span>            arrayList.add(SubscriptionInfoModel.from(telephonyManager));
<span class="lineno">  45 </span>        }
<span class="lineno">  46 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/SocketService.java">../../src/main/java/com/bm/atool/service/SocketService.java</a>:165</span>: <span class="message">Using <code>getString</code> to get device identifiers is not recommended</span><br /><pre class="errorlines">
<span class="lineno">  162 </span>  <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> String PREF_DEVICE_GUID = <span class="string">"PREF_DEVICE_GUID"</span>;
<span class="lineno">  163 </span>
<span class="lineno">  164 </span>  <span class="keyword">private</span> String getUniqueDeviceId() {
<span class="caretline"><span class="lineno">  165 </span>      String androidId = <span class="warning">Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID)</span>;</span>
<span class="lineno">  166 </span>      <span class="keyword">if</span> (androidId == <span class="keyword">null</span> || androidId.isEmpty() || <span class="string">"9774d56d682e549c"</span>.equals(androidId)) {
<span class="lineno">  167 </span>          String storedGuid = PrefUtil.getString(<span class="keyword">this</span>, PREF_DEVICE_GUID);
<span class="lineno">  168 </span>          <span class="keyword">if</span> (storedGuid == <span class="keyword">null</span> || storedGuid.isEmpty()) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/model/SubscriptionInfoModel.java">../../src/main/java/com/bm/atool/model/SubscriptionInfoModel.java</a>:126</span>: <span class="message">Using <code>getSubscriberId</code> to get device identifiers is not recommended</span><br /><pre class="errorlines">
<span class="lineno"> 123 </span>  }
<span class="lineno"> 124 </span>
<span class="lineno"> 125 </span>  <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 126 </span>      subscriptionInfoModel.subscriptionId = String.valueOf(<span class="warning">telephonyManager.getSubscriberId()</span>);</span>
<span class="lineno"> 127 </span>      Log.d(TAG, <span class="string">"SubscriptionId (getSubscriberId) set to: "</span> + subscriptionInfoModel.subscriptionId);
<span class="lineno"> 128 </span>  } <span class="keyword">catch</span> (SecurityException se) {
<span class="lineno"> 129 </span>      Log.w(TAG, <span class="string">"SecurityException getting SubscriberId: "</span> + se.getMessage());
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/model/SubscriptionInfoModel.java">../../src/main/java/com/bm/atool/model/SubscriptionInfoModel.java</a>:142</span>: <span class="message">Using <code>getSimSerialNumber</code> to get device identifiers is not recommended</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>
<span class="lineno"> 140 </span>        <span class="keyword">try</span> {
<span class="lineno"> 141 </span>            Log.d(TAG, <span class="string">"Attempting to get ICCID (getSimSerialNumber)"</span>);
<span class="caretline"><span class="lineno"> 142 </span>            subscriptionInfoModel.iccId = <span class="warning">telephonyManager.getSimSerialNumber()</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>            Log.d(TAG, <span class="string">"ICCID (getSimSerialNumber): "</span> + subscriptionInfoModel.iccId);
<span class="lineno"> 144 </span>        }
<span class="lineno"> 145 </span>        <span class="keyword">catch</span> (SecurityException se) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/model/SubscriptionInfoModel.java">../../src/main/java/com/bm/atool/model/SubscriptionInfoModel.java</a>:157</span>: <span class="message">Using <code>getLine1Number</code> to get device identifiers is not recommended</span><br /><pre class="errorlines">
<span class="lineno"> 154 </span>
<span class="lineno"> 155 </span>  <span class="keyword">try</span> {
<span class="lineno"> 156 </span>      Log.d(TAG, <span class="string">"Attempting to get PhoneNumber (getLine1Number)"</span>);
<span class="caretline"><span class="lineno"> 157 </span>      subscriptionInfoModel.phoneNumber = <span class="warning">telephonyManager.getLine1Number()</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 158 </span>      Log.d(TAG, <span class="string">"PhoneNumber (getLine1Number): "</span> + subscriptionInfoModel.phoneNumber);
<span class="lineno"> 159 </span>  } <span class="keyword">catch</span> (SecurityException se) {
<span class="lineno"> 160 </span>      Log.w(TAG, <span class="string">"SecurityException getting PhoneNumber (getLine1Number): "</span> + se.getMessage());
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHardwareIds" style="display: none;">
Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use <code>AdvertisingIdClient$Info#getId</code> and for analytics, use <code>InstanceId#getId</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/articles/user-data-ids.html">https://developer.android.com/training/articles/user-data-ids.html</a>
</div>To suppress this error, use the issue id "HardwareIds" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardwareIds</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardwareIdsLink" onclick="reveal('explanationHardwareIds');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardwareIdsCardLink" onclick="hideid('HardwareIdsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnsafeProtectedBroadcastReceiver"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnsafeProtectedBroadcastReceiverCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unsafe Protected BroadcastReceiver</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/receivers/SimChangedReceiver.java">../../src/main/java/com/bm/atool/receivers/SimChangedReceiver.java</a>:13</span>: <span class="message">This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver's onReceive method does not appear to call getAction to ensure that the received Intent's action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored.</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span><span class="keyword">public</span> <span class="keyword">class</span> SimChangedReceiver <span class="keyword">extends</span> BroadcastReceiver {
<span class="lineno"> 12 </span>    <span class="annotation">@Override</span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="keyword">public</span> <span class="keyword">void</span> <span class="warning">onReceive</span>(Context context, Intent intent) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        Log.d(<span class="string">"sms-change"</span>,intent.getStringExtra(<span class="string">"ss"</span>));
<span class="lineno"> 15 </span>        Sys.onPhoneCHangeEvent(PhoneUtils.getPhones(context));
<span class="lineno"> 16 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/receivers/SmsReceiver.java">../../src/main/java/com/bm/atool/receivers/SmsReceiver.java</a>:31</span>: <span class="message">This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver's onReceive method does not appear to call getAction to ensure that the received Intent's action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored.</span><br /><pre class="errorlines">
<span class="lineno">  28 </span>    <span class="keyword">private</span> <span class="keyword">final</span> <span class="keyword">static</span> String TAG = SmsReceiver.<span class="keyword">class</span>.getSimpleName();
<span class="lineno">  29 </span>    <span class="annotation">@Override</span> <span class="comment">// android.content.BroadcastReceiver</span>
<span class="lineno">  30 </span>    <span class="annotation">@SuppressLint</span>({<span class="string">"NewApi"</span>})
<span class="caretline"><span class="lineno">  31 </span>    <span class="keyword">public</span> <span class="keyword">void</span> <span class="warning">onReceive</span>(Context context,  Intent intent) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  32 </span>        <span class="keyword">if</span>(!Sys.isLogin()){
<span class="lineno">  33 </span>            <span class="keyword">return</span>;
<span class="lineno">  34 </span>        }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnsafeProtectedBroadcastReceiver" style="display: none;">
`BroadcastReceiver`s that declare an intent-filter for a protected-broadcast action string must check that the received intent's action string matches the expected value, otherwise it is possible for malicious actors to spoof intents.<br/><div class="moreinfo">More info: <a href="https://goo.gle/UnsafeProtectedBroadcastReceiver">https://goo.gle/UnsafeProtectedBroadcastReceiver</a>
</div>To suppress this error, use the issue id "UnsafeProtectedBroadcastReceiver" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnsafeProtectedBroadcastReceiver</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnsafeProtectedBroadcastReceiverLink" onclick="reveal('explanationUnsafeProtectedBroadcastReceiver');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnsafeProtectedBroadcastReceiverCardLink" onclick="hideid('UnsafeProtectedBroadcastReceiverCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ExportedService"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExportedServiceCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Exported service does not require permission</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:140</span>: <span class="message">Exported service does not require permission</span><br /><pre class="errorlines">
<span class="lineno"> 137 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"true"</span>
<span class="lineno"> 138 </span>            <span class="prefix">android:</span><span class="attribute">process</span>=<span class="value">":watch_job"</span>/>
<span class="lineno"> 139 </span>
<span class="caretline"><span class="lineno"> 140 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">service</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 141 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".service.WatchDogService"</span>
<span class="lineno"> 142 </span>            <span class="prefix">android:</span><span class="attribute">foregroundServiceType</span>=<span class="value">"mediaPlayback"</span>
<span class="lineno"> 143 </span>            <span class="prefix">android:</span><span class="attribute">enabled</span>=<span class="value">"true"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationExportedService" style="display: none;">
Exported services (services which either set <code>exported=true</code> or contain an intent-filter and do not specify <code>exported=false</code>) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service.<br/><div class="moreinfo">More info: <a href="https://goo.gle/ExportedService">https://goo.gle/ExportedService</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ExportedService" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ExportedService</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationExportedServiceLink" onclick="reveal('explanationExportedService');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExportedServiceCardLink" onclick="hideid('ExportedServiceCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SystemPermissionTypo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SystemPermissionTypoCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Permission appears to be a standard permission with a typo</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:15</span>: <span class="message">Did you mean <code>android.permission.WRITE_OBB</code>?</span><br /><pre class="errorlines">
<span class="lineno">  12 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.RECEIVE_SMS"</span> />
<span class="lineno">  13 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_SMS"</span> />
<span class="lineno">  14 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.SEND_SMS"</span> />
<span class="caretline"><span class="lineno">  15 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.WRITE_SMS</span></span><span class="value">"</span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  16 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.INTERNET"</span> />
<span class="lineno">  17 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_PHONE_STATE"</span> />
<span class="lineno">  18 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_PHONE_NUMBERS"</span>/>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSystemPermissionTypo" style="display: none;">
This check looks for required permissions that <i>look</i> like well-known system permissions or permissions from the Android SDK, but aren't, and may be typos.<br/>
<br/>
Please double check the permission value you have supplied.<br/>To suppress this error, use the issue id "SystemPermissionTypo" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SystemPermissionTypo</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSystemPermissionTypoLink" onclick="reveal('explanationSystemPermissionTypo');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SystemPermissionTypoCardLink" onclick="hideid('SystemPermissionTypoCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="Wakelock"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="WakelockCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Incorrect WakeLock usage</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/MainActivity.java">../../src/main/java/com/bm/atool/MainActivity.java</a>:235</span>: <span class="message">Should not set both <code>PARTIAL_WAKE_LOCK</code> and <code>ACQUIRE_CAUSES_WAKEUP</code>. If you do not want the screen to turn on, get rid of <code>ACQUIRE_CAUSES_WAKEUP</code></span><br /><pre class="errorlines">
<span class="lineno"> 232 </span>  <span class="keyword">try</span>{
<span class="lineno"> 233 </span>      <span class="keyword">if</span>(Objects.isNull(wakeLock)){
<span class="lineno"> 234 </span>          PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
<span class="caretline"><span class="lineno"> 235 </span>          wakeLock = powerManager.<span class="warning">newWakeLock</span>(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 236 </span>                  PowerManager.PARTIAL_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, <span class="string">"androidtoolTag"</span>);
<span class="lineno"> 237 </span>      }
<span class="lineno"> 238 </span>      wakeLock.acquire();
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationWakelock" style="display: none;">
Failing to release a wakelock properly can keep the Android device in a high power mode, which reduces battery life. There are several causes of this, such as releasing the wake lock in <code>onDestroy()</code> instead of in <code>onPause()</code>, failing to call <code>release()</code> in all possible code paths after an <code>acquire()</code>, and so on.<br/>
<br/>
NOTE: If you are using the lock just to keep the screen on, you should strongly consider using <code>FLAG_KEEP_SCREEN_ON</code> instead. This window flag will be correctly managed by the platform as the user moves between applications and doesn't require a special permission. See <a href="https://developer.android.com/reference/android/view/WindowManager.LayoutParams.html#FLAG_KEEP_SCREEN_ON">https://developer.android.com/reference/android/view/WindowManager.LayoutParams.html#FLAG_KEEP_SCREEN_ON</a>.<br/>To suppress this error, use the issue id "Wakelock" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Wakelock</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 9/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationWakelockLink" onclick="reveal('explanationWakelock');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="WakelockCardLink" onclick="hideid('WakelockCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="WakelockTimeout"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="WakelockTimeoutCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using wakeLock without timeout</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/MainActivity.java">../../src/main/java/com/bm/atool/MainActivity.java</a>:238</span>: <span class="message">Provide a timeout when requesting a wakelock with <code>PowerManager.Wakelock.acquire(long timeout)</code>. This will ensure the OS will cleanup any wakelocks that last longer than you intend, and will save your user's battery.</span><br /><pre class="errorlines">
<span class="lineno"> 235 </span>          wakeLock = powerManager.newWakeLock(
<span class="lineno"> 236 </span>                  PowerManager.PARTIAL_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, <span class="string">"androidtoolTag"</span>);
<span class="lineno"> 237 </span>      }
<span class="caretline"><span class="lineno"> 238 </span>      <span class="warning">wakeLock.acquire()</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 239 </span>  }
<span class="lineno"> 240 </span>  <span class="keyword">catch</span> (Exception ex){
<span class="lineno"> 241 </span>      ex.printStackTrace();
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationWakelockTimeout" style="display: none;">
Wakelocks have two acquire methods: one with a timeout, and one without. You should generally always use the one with a timeout. A typical timeout is 10 minutes. If the task takes longer than it is critical that it happens (i.e. can't use <code>JobScheduler</code>) then maybe they should consider a foreground service instead (which is a stronger run guarantee and lets the user know something long/important is happening).<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WakelockTimeout" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">WakelockTimeout</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 9/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationWakelockTimeoutLink" onclick="reveal('explanationWakelockTimeout');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="WakelockTimeoutCardLink" onclick="hideid('WakelockTimeoutCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ObsoleteLayoutParam"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteLayoutParamCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete layout params</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/phone_row.xml">../../src/main/res/layout/phone_row.xml</a>:14</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 14 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>/>
<span class="lineno"> 17 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/phone_row.xml">../../src/main/res/layout/phone_row.xml</a>:23</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 22 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 23 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/darker_gray"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>/>
<span class="lineno"> 26 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/phone_row.xml">../../src/main/res/layout/phone_row.xml</a>:34</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"请输入手机号码"</span>
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"phone"</span>
<span class="caretline"><span class="lineno"> 34 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span>
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>/>
<span class="lineno"> 37 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/phone_row.xml">../../src/main/res/layout/phone_row.xml</a>:42</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 40 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 41 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 42 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 43 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 44 </span>
<span class="lineno"> 45 </span>    <span class="comment">&lt;!--    &lt;TextView--></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:33</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 30 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 31 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="lineno"> 32 </span>            <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"6dp"</span>
<span class="caretline"><span class="lineno"> 33 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 34 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 35 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 36 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/txtTime"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:40</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"120dp"</span>
<span class="lineno"> 38 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 39 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 40 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 42 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 43 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/txtStatus"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:47</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 44 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"120dp"</span>
<span class="lineno"> 45 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 46 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 47 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 48 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 49 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 50 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:66</span>: <span class="message">Invalid layout param in a <code>LinearLayout</code>: <code>layout_centerVertical</code></span><br /><pre class="errorlines">
<span class="lineno"> 63 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 64 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="lineno"> 65 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"48dp"</span>
<span class="caretline"><span class="lineno"> 66 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 67 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 68 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 69 </span><span class="tag">&lt;/LinearLayout></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteLayoutParam" style="display: none;">
The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteLayoutParam" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteLayoutParam</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteLayoutParamLink" onclick="reveal('explanationObsoleteLayoutParam');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteLayoutParamCardLink" onclick="hideid('ObsoleteLayoutParamCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ObsoleteSdkInt"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteSdkIntCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete SDK_INT Version Check</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/App.java">../../src/main/java/com/bm/atool/App.java</a>:101</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  98 </span>
<span class="lineno">  99 </span>  <span class="comment">// Before API 18, the method was incorrectly named "currentPackageName", but it still returned the process name</span>
<span class="lineno"> 100 </span>  <span class="comment">// See https://github.com/aosp-mirror/platform_frameworks_base/commit/b57a50bd16ce25db441da5c1b63d48721bb90687</span>
<span class="caretline"><span class="lineno"> 101 </span>  String methodName = <span class="warning">Build.VERSION.SDK_INT >= <span class="number">18</span></span> ? <span class="string">"currentProcessName"</span> : <span class="string">"currentPackageName"</span>;</span>
<span class="lineno"> 102 </span>
<span class="lineno"> 103 </span>  Method getProcessName = activityThread.getDeclaredMethod(methodName);
<span class="lineno"> 104 </span>  <span class="keyword">return</span> (String) getProcessName.invoke(<span class="keyword">null</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/ui/views/BlackUnderlineEditText.java">../../src/main/java/com/bm/atool/ui/views/BlackUnderlineEditText.java</a>:53</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 50 </span>        );
<span class="lineno"> 51 </span>        
<span class="lineno"> 52 </span>        <span class="comment">// 适配不同的Android版本</span>
<span class="caretline"><span class="lineno"> 53 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 54 </span>            setBackgroundTintList(colorStateList);
<span class="lineno"> 55 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 56 </span>            Drawable drawable = getBackground();
</pre>

<span class="location"><a href="../../src/main/java/com/xuexiang/keeplive/whitelist/impl/DefaultWhiteListProvider.java">../../src/main/java/com/xuexiang/keeplive/whitelist/impl/DefaultWhiteListProvider.java</a>:52</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  49 </span>  List&lt;WhiteListIntentWrapper> intentWrappers = <span class="keyword">new</span> ArrayList&lt;>();
<span class="lineno">  50 </span>
<span class="lineno">  51 </span>  <span class="comment">//Android 7.0+ Doze 模式</span>
<span class="caretline"><span class="lineno">  52 </span>  <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.N</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  53 </span>      PowerManager pm = (PowerManager) application.getSystemService(Context.POWER_SERVICE);
<span class="lineno">  54 </span>      <span class="keyword">boolean</span> ignoringBatteryOptimizations = pm.isIgnoringBatteryOptimizations(application.getPackageName());
<span class="lineno">  55 </span>      <span class="keyword">if</span> (!ignoringBatteryOptimizations) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/JobSchedulerService.java">../../src/main/java/com/bm/atool/service/JobSchedulerService.java</a>:16</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span><span class="javadoc"> * 运行在 :watch 子进程中.
</span><span class="lineno"> 14 </span><span class="javadoc"> *
</span><span class="lineno"> 15 </span><span class="javadoc"> */</span>
<span class="caretline"><span class="lineno"> 16 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.LOLLIPOP)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span><span class="keyword">public</span> <span class="keyword">class</span> JobSchedulerService <span class="keyword">extends</span> JobService {
<span class="lineno"> 18 </span>
<span class="lineno"> 19 </span>    <span class="annotation">@Override</span></pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:134</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 131 </span>            <span class="keyword">return</span> <span class="keyword">false</span>;
<span class="lineno"> 132 </span>        }
<span class="lineno"> 133 </span>        
<span class="caretline"><span class="lineno"> 134 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 135 </span>            <span class="keyword">return</span> Settings.canDrawOverlays(context);
<span class="lineno"> 136 </span>        }
<span class="lineno"> 137 </span>        <span class="keyword">return</span> <span class="keyword">true</span>;
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ObsoleteSdkIntDivLink" onclick="reveal('ObsoleteSdkIntDiv');" />+ 22 More Occurrences...</button>
<div id="ObsoleteSdkIntDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:146</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 143 </span>      <span class="keyword">return</span> <span class="keyword">false</span>;
<span class="lineno"> 144 </span>  }
<span class="lineno"> 145 </span>  
<span class="caretline"><span class="lineno"> 146 </span>  <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 147 </span>      <span class="keyword">try</span> {
<span class="lineno"> 148 </span>          PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
<span class="lineno"> 149 </span>          <span class="keyword">if</span> (powerManager != <span class="keyword">null</span>) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:175</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 172 </span>
<span class="lineno"> 173 </span>  <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> manageOverlayToSettingPage(Context context) {
<span class="lineno"> 174 </span>      Log.d(TAG, <span class="string">"manageOverlayToSettingPage: Attempting to open overlay settings for package: "</span> + context.getPackageName());
<span class="caretline"><span class="lineno"> 175 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 176 </span>          <span class="keyword">try</span> {
<span class="lineno"> 177 </span>              Intent intent = <span class="keyword">new</span> Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse(<span class="string">"package:"</span> + context.getPackageName()));
<span class="lineno"> 178 </span>              intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PermissionUtils.java">../../src/main/java/com/bm/atool/utils/PermissionUtils.java</a>:198</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 195 </span>  <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> batteryOptimizationToSettingPage(Context context) {
<span class="lineno"> 196 </span>      Log.d(TAG, <span class="string">"batteryOptimizationToSettingPage: Attempting to open battery optimization settings for package: "</span> + context.getPackageName());
<span class="lineno"> 197 </span>      <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 198 </span>          <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 199 </span>              Intent intent = <span class="keyword">new</span> Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
<span class="lineno"> 200 </span>              intent.setData(Uri.parse(<span class="string">"package:"</span> + context.getPackageName()));
<span class="lineno"> 201 </span>              intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PhoneUtils.java">../../src/main/java/com/bm/atool/utils/PhoneUtils.java</a>:36</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>        }
<span class="lineno">  34 </span>        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
<span class="lineno">  35 </span><span class="comment">//        Log.e(TAG,((TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE)).getLine1Number());</span>
<span class="caretline"><span class="lineno">  36 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= <span class="number">22</span></span> ) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  37 </span>            SubscriptionManager from = SubscriptionManager.from(context.getApplicationContext());
<span class="lineno">  38 </span>            <span class="keyword">for</span> (SubscriptionInfo subscriptionInfo : from.getActiveSubscriptionInfoList()) {
<span class="lineno">  39 </span>                TelephonyManager telephonyManager1 = telephonyManager.createForSubscriptionId(subscriptionInfo.getSubscriptionId());
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/utils/PhoneUtils.java">../../src/main/java/com/bm/atool/utils/PhoneUtils.java</a>:52</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  49 </span>
<span class="lineno">  50 </span>  <span class="annotation">@SuppressLint</span>(<span class="string">"MissingPermission"</span>)
<span class="lineno">  51 </span>  <span class="keyword">public</span> <span class="keyword">static</span> PhoneAccountHandle getPhoneAccountHandleFromSubscriptionId(Context context, <span class="keyword">int</span> subscriptionId) {
<span class="caretline"><span class="lineno">  52 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  53 </span>          TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
<span class="lineno">  54 </span>          <span class="keyword">if</span> (telecomManager != <span class="keyword">null</span>) {
<span class="lineno">  55 </span>              List&lt;PhoneAccountHandle> phoneAccountHandleList = telecomManager.getCallCapablePhoneAccounts();
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/singlepixel/SinglePixelActivity.java">../../src/main/java/com/bm/atool/service/singlepixel/SinglePixelActivity.java</a>:85</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 82 </span>
<span class="lineno"> 83 </span>    <span class="keyword">private</span> <span class="keyword">boolean</span> isScreenOn() {
<span class="lineno"> 84 </span>        PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
<span class="caretline"><span class="lineno"> 85 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 86 </span>            <span class="keyword">return</span> powerManager.isInteractive();
<span class="lineno"> 87 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 88 </span>            <span class="keyword">return</span> powerManager.isScreenOn();
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/SmsSender.java">../../src/main/java/com/bm/atool/SmsSender.java</a>:110</span>: <span class="message">Unnecessary; SDK_INT is never &lt; 24</span><br /><pre class="errorlines">
<span class="lineno"> 107 </span>  }
<span class="lineno"> 108 </span>
<span class="lineno"> 109 </span>  <span class="keyword">private</span> <span class="keyword">static</span> SmsManager getSmsManagerForPhoneNumber(Context context, String phoneNumber) {
<span class="caretline"><span class="lineno"> 110 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 111 </span>          Log.d(TAG, <span class="string">"当前系统版本不支持指定手机号发送短信，使用默认SmsManager"</span>);
<span class="lineno"> 112 </span>          <span class="keyword">return</span> <span class="keyword">null</span>;
<span class="lineno"> 113 </span>      }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/UssdProcessor.java">../../src/main/java/com/bm/atool/UssdProcessor.java</a>:82</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  79 </span>  <span class="keyword">int</span> subId = getSubscriptionIdForPhoneNumber(context, request.targetPhone);
<span class="lineno">  80 </span>  <span class="keyword">if</span> (subId != -<span class="number">1</span>) {
<span class="lineno">  81 </span>      Log.d(TAG, <span class="string">"找到匹配的SIM卡，subscriptionId: "</span> + subId);
<span class="caretline"><span class="lineno">  82 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  83 </span>          intent.putExtra(<span class="string">"android.telecom.extra.PHONE_ACCOUNT_HANDLE"</span>, 
<span class="lineno">  84 </span>                  PhoneUtils.getPhoneAccountHandleFromSubscriptionId(context, subId));
<span class="lineno">  85 </span>          simSelected = <span class="keyword">true</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/UssdProcessor.java">../../src/main/java/com/bm/atool/UssdProcessor.java</a>:96</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  93 </span>  <span class="keyword">if</span> (!simSelected &amp;&amp; request.subscriptionId != <span class="keyword">null</span> &amp;&amp; !request.subscriptionId.isEmpty()) {
<span class="lineno">  94 </span>      <span class="keyword">int</span> subId = Integer.parseInt(request.subscriptionId);
<span class="lineno">  95 </span>      Log.d(TAG, <span class="string">"使用subscriptionId执行USSD: "</span> + subId);
<span class="caretline"><span class="lineno">  96 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  97 </span>          intent.putExtra(<span class="string">"android.telecom.extra.PHONE_ACCOUNT_HANDLE"</span>, 
<span class="lineno">  98 </span>                  PhoneUtils.getPhoneAccountHandleFromSubscriptionId(context, subId));
<span class="lineno">  99 </span>      }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/UssdProcessor.java">../../src/main/java/com/bm/atool/UssdProcessor.java</a>:134</span>: <span class="message">Unnecessary; SDK_INT is never &lt; 24</span><br /><pre class="errorlines">
<span class="lineno"> 131 </span>    }
<span class="lineno"> 132 </span>
<span class="lineno"> 133 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">int</span> getSubscriptionIdForPhoneNumber(Context context, String phoneNumber) {
<span class="caretline"><span class="lineno"> 134 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 135 </span>            Log.d(TAG, <span class="string">"当前系统版本不支持根据手机号确定SIM卡"</span>);
<span class="lineno"> 136 </span>            <span class="keyword">return</span> -<span class="number">1</span>;
<span class="lineno"> 137 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/WatchDogService.java">../../src/main/java/com/bm/atool/service/WatchDogService.java</a>:78</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  75 </span>  ForegroundNotificationUtils.startForegroundNotification(<span class="keyword">this</span>);
<span class="lineno">  76 </span>  <span class="keyword">if</span> (mDisposable == <span class="keyword">null</span> || mDisposable.isDisposed()) {
<span class="lineno">  77 </span>      <span class="comment">//定时检查 AbsWorkService 是否在运行，如果不在运行就把它拉起来   Android 5.0+ 使用 JobScheduler，效果比 AlarmManager 好</span>
<span class="caretline"><span class="lineno">  78 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  79 </span>          JobInfo.Builder builder = <span class="keyword">new</span> JobInfo.Builder(HASH_CODE,
<span class="lineno">  80 </span>                  <span class="keyword">new</span> ComponentName(WatchDogService.<span class="keyword">this</span>, JobSchedulerService.<span class="keyword">class</span>));
<span class="lineno">  81 </span>          builder.setPeriodic(Sys.getWakeUpInterval(Sys.MINIMAL_WAKE_UP_INTERVAL));
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/WatchDogService.java">../../src/main/java/com/bm/atool/service/WatchDogService.java</a>:83</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>          <span class="keyword">new</span> ComponentName(WatchDogService.<span class="keyword">this</span>, JobSchedulerService.<span class="keyword">class</span>));
<span class="lineno">  81 </span>  builder.setPeriodic(Sys.getWakeUpInterval(Sys.MINIMAL_WAKE_UP_INTERVAL));
<span class="lineno">  82 </span>  <span class="comment">//Android 7.0+ 增加了一项针对 JobScheduler 的新限制，最小间隔只能是下面设定的数字</span>
<span class="caretline"><span class="lineno">  83 </span>  <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.N</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>      builder.setPeriodic(JobInfo.getMinPeriodMillis(), JobInfo.getMinFlexMillis());
<span class="lineno">  85 </span>  }
<span class="lineno">  86 </span>  builder.setPersisted(<span class="keyword">true</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/WatchDogService.java">../../src/main/java/com/bm/atool/service/WatchDogService.java</a>:187</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 184 </span><span class="javadoc">   * 而是向 WakeUpReceiver 发送一个 Action 为 WakeUpReceiver.ACTION_CANCEL_JOB_ALARM_SUB 的广播.
</span><span class="lineno"> 185 </span><span class="javadoc">   */</span>
<span class="lineno"> 186 </span>  <span class="keyword">private</span> <span class="keyword">void</span> cancelJobAlarmSub() {
<span class="caretline"><span class="lineno"> 187 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 188 </span>          JobScheduler scheduler = (JobScheduler) WatchDogService.<span class="keyword">this</span>.getSystemService(JOB_SCHEDULER_SERVICE);
<span class="lineno"> 189 </span>          scheduler.cancel(HASH_CODE);
<span class="lineno"> 190 </span>      } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/receivers/WatchDogStatusReceiver.java">../../src/main/java/com/bm/atool/receivers/WatchDogStatusReceiver.java</a>:44</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 41 </span>
<span class="lineno"> 42 </span>      <span class="comment">// Before API 18, the method was incorrectly named "currentPackageName", but it still returned the process name</span>
<span class="lineno"> 43 </span>      <span class="comment">// See https://github.com/aosp-mirror/platform_frameworks_base/commit/b57a50bd16ce25db441da5c1b63d48721bb90687</span>
<span class="caretline"><span class="lineno"> 44 </span>      String methodName = <span class="warning">Build.VERSION.SDK_INT >= <span class="number">18</span></span> ? <span class="string">"currentProcessName"</span> : <span class="string">"currentPackageName"</span>;</span>
<span class="lineno"> 45 </span>      Method getProcessName = activityThread.getDeclaredMethod(methodName);
<span class="lineno"> 46 </span>      <span class="keyword">return</span> (String) getProcessName.invoke(<span class="keyword">null</span>);
<span class="lineno"> 47 </span>  } <span class="keyword">catch</span> (Exception e) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/WatchedService.java">../../src/main/java/com/bm/atool/service/WatchedService.java</a>:59</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span>
<span class="lineno"> 57 </span>      <span class="comment">// Before API 18, the method was incorrectly named "currentPackageName", but it still returned the process name</span>
<span class="lineno"> 58 </span>      <span class="comment">// See https://github.com/aosp-mirror/platform_frameworks_base/commit/b57a50bd16ce25db441da5c1b63d48721bb90687</span>
<span class="caretline"><span class="lineno"> 59 </span>      String methodName = <span class="warning">Build.VERSION.SDK_INT >= <span class="number">18</span></span> ? <span class="string">"currentProcessName"</span> : <span class="string">"currentPackageName"</span>;</span>
<span class="lineno"> 60 </span>      Method getProcessName = activityThread.getDeclaredMethod(methodName);
<span class="lineno"> 61 </span>      <span class="keyword">return</span> (String) getProcessName.invoke(<span class="keyword">null</span>);
<span class="lineno"> 62 </span>  } <span class="keyword">catch</span> (Exception e) {
</pre>

<span class="location"><a href="../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteList.java">../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteList.java</a>:95</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  92 </span>  }
<span class="lineno">  93 </span>
<span class="lineno">  94 </span>  <span class="keyword">if</span> (intentWrapper.getType() == IntentType.DOZE) {
<span class="caretline"><span class="lineno">  95 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.N</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  96 </span>          PowerManager pm = (PowerManager) Sys.app.getSystemService(Context.POWER_SERVICE);
<span class="lineno">  97 </span>          <span class="keyword">if</span> (!pm.isIgnoringBatteryOptimizations(Sys.app.getPackageName())) {
<span class="lineno">  98 </span>              sMatchedWhiteListIntent.add(intentWrapper);
</pre>

<span class="location"><a href="../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteList.java">../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteList.java</a>:142</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>  <span class="keyword">while</span> (iterator.hasNext()) {
<span class="lineno"> 140 </span>      WhiteListIntentWrapper intentWrapper = iterator.next();
<span class="lineno"> 141 </span>      <span class="keyword">if</span> (intentWrapper.getType() == IntentType.DOZE) {
<span class="caretline"><span class="lineno"> 142 </span>          <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.N</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>              PowerManager pm = (PowerManager) Sys.app.getSystemService(Context.POWER_SERVICE);
<span class="lineno"> 144 </span>              <span class="keyword">if</span> (pm.isIgnoringBatteryOptimizations(Sys.app.getPackageName())) {
<span class="lineno"> 145 </span>                  iterator.remove();
</pre>

<span class="location"><a href="../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteList.java">../../src/main/java/com/xuexiang/keeplive/whitelist/WhiteList.java</a>:173</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 170 </span>  <span class="keyword">while</span> (iterator.hasNext()) {
<span class="lineno"> 171 </span>      WhiteListIntentWrapper intentWrapper = iterator.next();
<span class="lineno"> 172 </span>      <span class="keyword">if</span> (intentWrapper.getType() == IntentType.DOZE) {
<span class="caretline"><span class="lineno"> 173 </span>          <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.N</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 174 </span>              PowerManager pm = (PowerManager) Sys.app.getSystemService(Context.POWER_SERVICE);
<span class="lineno"> 175 </span>              <span class="keyword">if</span> (pm.isIgnoringBatteryOptimizations(Sys.app.getPackageName())) {
<span class="lineno"> 176 </span>                  iterator.remove();
</pre>

<span class="location"><a href="../../src/main/res/drawable-v24">../../src/main/res/drawable-v24</a></span>: <span class="message">This folder configuration (<code>v24</code>) is unnecessary; <code>minSdkVersion</code> is 24. Merge all the resources in this folder into <code>drawable</code>.</span><br />
<span class="location"><a href="../../src/main/res/values-night/themes.xml">../../src/main/res/values-night/themes.xml</a>:6</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 3 </span>    <span class="tag">&lt;style</span><span class="attribute"> name</span>=<span class="value">"Base.Theme.Snettool"</span> <span class="attribute">parent</span>=<span class="value">"Theme.Material3.DayNight.NoActionBar"</span>>
<span class="lineno"> 4 </span>        <span class="comment">&lt;!-- Customize your dark theme here. --></span>
<span class="lineno"> 5 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>#F5F5F5<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno"> 6 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span> <span class="warning"><span class="prefix">tools:</span><span class="attribute">targetApi</span>=<span class="value">"l"</span></span>>#000000<span class="tag">&lt;/item></span> <span class="comment">&lt;!-- Black --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 7 </span>    <span class="tag">&lt;/style></span>
<span class="lineno"> 8 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:6</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="tag">&lt;style</span><span class="attribute"> name</span>=<span class="value">"Base.Theme.AndroidTool"</span> <span class="attribute">parent</span>=<span class="value">"Theme.Material3.DayNight.NoActionBar"</span>>
<span class="lineno">  4 </span>        <span class="comment">&lt;!-- Customize your light theme here. --></span>
<span class="lineno">  5 </span>         <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>#F5F5F5<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno">  6 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span> <span class="warning"><span class="prefix">tools:</span><span class="attribute">targetApi</span>=<span class="value">"l"</span></span>>#000000<span class="tag">&lt;/item></span> <span class="comment">&lt;!-- Black --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorControlNormal"</span>>#000000<span class="tag">&lt;/item></span>
<span class="lineno">  8 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorControlActivated"</span>>@color/green<span class="tag">&lt;/item></span>
<span class="lineno">  9 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorControlHighlight"</span>>@color/green<span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/values-v23">../../src/main/res/values-v23</a></span>: <span class="message">This folder configuration (<code>v23</code>) is unnecessary; <code>minSdkVersion</code> is 24. Merge all the resources in this folder into <code>values</code>.</span><br />
</div>
</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteSdkInt" style="display: none;">
This check flags version checks that are not necessary, because the <code>minSdkVersion</code> (or surrounding known API level) is already at least as high as the version checked for.<br/>
<br/>
Similarly, it also looks for resources in <code>-vNN</code> folders, such as <code>values-v14</code> where the version qualifier is less than or equal to the <code>minSdkVersion</code>, where the contents should be merged into the best folder.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteSdkInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteSdkInt</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteSdkIntLink" onclick="reveal('explanationObsoleteSdkInt');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteSdkIntCardLink" onclick="hideid('ObsoleteSdkIntCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="StaticFieldLeak"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="StaticFieldLeakCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Static Field Leaks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/service/singlepixel/ScreenManager.java">../../src/main/java/com/bm/atool/service/singlepixel/ScreenManager.java</a>:18</span>: <span class="message">Do not place Android context classes in static fields (static reference to <code>ScreenManager</code> which has field <code>mContext</code> pointing to <code>Context</code>); this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span><span class="keyword">public</span> <span class="keyword">class</span> ScreenManager {
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> String TAG = ScreenManager.<span class="keyword">class</span>.getSimpleName();
<span class="caretline"><span class="lineno"> 18 </span>    <span class="keyword">private</span> <span class="warning"><span class="keyword">static</span></span> ScreenManager sInstance;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>    <span class="keyword">private</span> Context mContext;
<span class="lineno"> 20 </span>    <span class="keyword">private</span> WeakReference&lt;Activity> mActivity;
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/SocketServiceConnection.java">../../src/main/java/com/bm/atool/service/SocketServiceConnection.java</a>:19</span>: <span class="message">Do not place Android context classes in static fields (static reference to <code>SocketServiceConnection</code> which has field <code>host</code> pointing to <code>Context</code>); this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span><span class="keyword">import</span> java.util.Objects;
<span class="lineno"> 17 </span>
<span class="lineno"> 18 </span><span class="keyword">public</span> <span class="keyword">class</span> SocketServiceConnection <span class="keyword">extends</span> BaseServiceConnection {
<span class="caretline"><span class="lineno"> 19 </span>    <span class="keyword">private</span> <span class="warning"><span class="keyword">static</span></span> SocketServiceConnection instance;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>    <span class="keyword">private</span> Messenger messenger = <span class="keyword">null</span>;
<span class="lineno"> 21 </span>    <span class="keyword">private</span> Context host = <span class="keyword">null</span>;
<span class="lineno"> 22 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> String TAG = SocketServiceConnection.<span class="keyword">class</span>.getSimpleName();
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/Sys.java">../../src/main/java/com/bm/atool/Sys.java</a>:47</span>: <span class="message">Do not place Android context classes in static fields (static reference to <code>FloatingWindow</code> which has field <code>floatRootView</code> pointing to <code>View</code>); this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno">  44 </span>    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">final</span> String ACTION_SOCKET_PONG = <span class="string">"ACTION_SOCKET_PONG"</span>;
<span class="lineno">  45 </span>    <span class="keyword">public</span> <span class="keyword">static</span> App app;
<span class="lineno">  46 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Boolean watchDogEnabled = <span class="keyword">false</span>;
<span class="caretline"><span class="lineno">  47 </span>    <span class="keyword">private</span> <span class="warning"><span class="keyword">static</span></span> FloatingWindow floatingWindow = <span class="keyword">null</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  48 </span>    <span class="keyword">public</span>  <span class="keyword">static</span> ArrayList&lt;SMSEvent> lastSMSList = <span class="keyword">new</span> ArrayList&lt;>();
<span class="lineno">  49 </span>    <span class="keyword">public</span>  <span class="keyword">static</span> ArrayList&lt;SubscriptionInfoModel> phones = <span class="keyword">null</span>;
<span class="lineno">  50 </span>    <span class="keyword">public</span>  <span class="keyword">static</span>  ArrayList&lt;SubscriptionInfoModel> initPhone(Context context){
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationStaticFieldLeak" style="display: none;">
A static field will leak contexts.<br/>
<br/>
Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a <code>Fragment</code> or <code>Activity</code>, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.<br/>
<br/>
Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.<br/>
<br/>
ViewModel classes should never point to Views or non-application Contexts.<br/>To suppress this error, use the issue id "StaticFieldLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">StaticFieldLeak</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationStaticFieldLeakLink" onclick="reveal('explanationStaticFieldLeak');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="StaticFieldLeakCardLink" onclick="hideid('StaticFieldLeakCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HandlerLeak"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HandlerLeakCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Handler reference leaks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/service/SocketService.java">../../src/main/java/com/bm/atool/service/SocketService.java</a>:269</span>: <span class="message">This <code>Handler</code> class should be static or leaks might occur (com.bm.atool.service.SocketService.IncomingHandler)</span><br /><pre class="errorlines">
<span class="lineno">  266 </span>            startSocket();
<span class="lineno">  267 </span>        }
<span class="lineno">  268 </span>    };
<span class="caretline"><span class="lineno">  269 </span>    <span class="keyword">private</span> <span class="keyword">class</span> <span class="warning">IncomingHandler</span> <span class="keyword">extends</span> Handler {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  270 </span>        <span class="annotation">@RequiresApi</span>(api = Build.VERSION_CODES.TIRAMISU)
<span class="lineno">  271 </span>        <span class="annotation">@Override</span>
<span class="lineno">  272 </span>        <span class="keyword">public</span> <span class="keyword">void</span> handleMessage(Message message) {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHandlerLeak" style="display: none;">
Since this Handler is declared as an inner class, it may prevent the outer class from being garbage collected. If the Handler is using a <code>Looper</code> or <code>MessageQueue</code> for a thread other than the main thread, then there is no issue. If the <code>Handler</code> is using the <code>Looper</code> or <code>MessageQueue</code> of the main thread, you need to fix your <code>Handler</code> declaration, as follows: Declare the <code>Handler</code> as a static class; In the outer class, instantiate a <code>WeakReference</code> to the outer class and pass this object to your <code>Handler</code> when you instantiate the <code>Handler</code>; Make all references to members of the outer class using the <code>WeakReference</code> object.<br/>To suppress this error, use the issue id "HandlerLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HandlerLeak</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHandlerLeakLink" onclick="reveal('explanationHandlerLeak');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HandlerLeakCardLink" onclick="hideid('HandlerLeakCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InefficientWeight"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InefficientWeightCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Inefficient layout weight</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:16</span>: <span class="message">Use a <code>layout_height</code> of <code>0dp</code> instead of <code>1dp</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span><span class="comment">&lt;!--    app:tabSelectedTextColor="@color/teal_700"--></span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;androidx.viewpager.widget.ViewPager</span><span class="attribute">
</span><span class="lineno"> 15 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 16 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_height</span> = <span class="value">"1dp"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">layout_weight</span> =<span class="value">"1"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/view_pager"</span>/>
<span class="lineno"> 19 </span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInefficientWeight" style="display: none;">
When only a single widget in a <code>LinearLayout</code> defines a weight, it is more efficient to assign a width/height of <code>0dp</code> to it since it will absorb all the remaining space anyway. With a declared width/height of <code>0dp</code> it does not have to measure its own size first.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InefficientWeight" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InefficientWeight</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInefficientWeightLink" onclick="reveal('explanationInefficientWeight');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InefficientWeightCardLink" onclick="hideid('InefficientWeightCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Overdraw"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverdrawCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overdraw: Painting regions more than once</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:5</span>: <span class="message">Possible overdraw: Root element paints background <code>#ffffff</code> with a theme that also paints a background (inferred theme is <code>@style/Theme.AndroidTool</code>)</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;android.widget.LinearLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno">  3 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"#ffffff"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"30sp"</span>
<span class="lineno">  7 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  8 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:5</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/app_background_color</code> with a theme that also paints a background (inferred theme is <code>@style/Theme.AndroidTool</code>)</span><br /><pre class="errorlines">
<span class="lineno">   2 </span><span class="tag">&lt;ScrollView</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno">   3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   5 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/app_background_color"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"16dp"</span>>
<span class="lineno">   7 </span>
<span class="lineno">   8 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:8</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/app_background_color</code> with a theme that also paints a background (inferred theme is <code>@style/Theme.AndroidTool</code>)</span><br /><pre class="errorlines">
<span class="lineno">   5 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   6 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   7 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   8 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/app_background_color"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   9 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".ui.MainFragment"</span>>
<span class="lineno">  10 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno">  11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_settings.xml">../../src/main/res/layout/fragment_settings.xml</a>:7</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/app_background_color</code> with a theme that also paints a background (inferred theme is <code>@style/Theme.AndroidTool</code>)</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/app_background_color"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".ui.SettingsFragment"</span>>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;ScrollView</span><span class="attribute">
</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOverdraw" style="display: none;">
If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called "overdraw".<br/>
<br/>
NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it's currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.<br/>
<br/>
If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.<br/>
<br/>
Of course it's possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead.<br/>To suppress this error, use the issue id "Overdraw" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Overdraw</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOverdrawLink" onclick="reveal('explanationOverdraw');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverdrawCardLink" onclick="hideid('OverdrawCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/arrays.xml">../../src/main/res/values/arrays.xml</a>:3</span>: <span class="message">The resource <code>R.array.reply_entries</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="tag">&lt;resources></span>
<span class="lineno">  2 </span>    <span class="comment">&lt;!-- Reply Preference --></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"reply_entries"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>        <span class="tag">&lt;item></span>Reply<span class="tag">&lt;/item></span>
<span class="lineno">  5 </span>        <span class="tag">&lt;item></span>Reply to all<span class="tag">&lt;/item></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;/string-array></span>
</pre>

<span class="location"><a href="../../src/main/res/values/arrays.xml">../../src/main/res/values/arrays.xml</a>:8</span>: <span class="message">The resource <code>R.array.reply_values</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>        <span class="tag">&lt;item></span>Reply to all<span class="tag">&lt;/item></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;/string-array></span>
<span class="lineno">  7 </span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"reply_values"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>        <span class="tag">&lt;item></span>reply<span class="tag">&lt;/item></span>
<span class="lineno"> 10 </span>        <span class="tag">&lt;item></span>reply_all<span class="tag">&lt;/item></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;/string-array></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:3</span>: <span class="message">The resource <code>R.color.black</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"black"</span></span>>#FF000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"green"</span>>#4CAF50<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"app_background_color"</span>>#ECF5EE<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:2</span>: <span class="message">The resource <code>R.dimen.fab_margin</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno"> 2 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"fab_margin"</span></span>>16dp<span class="tag">&lt;/dimen></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 3 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/drawable-anydpi/ic_action_home.xml">../../src/main/res/drawable-anydpi/ic_action_home.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.ic_action_home</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UnusedResourcesDivLink" onclick="reveal('UnusedResourcesDiv');" />+ 17 More Occurrences...</button>
<div id="UnusedResourcesDiv" style="display: none">
<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:3</span>: <span class="message">The resource <code>R.string.action_settings</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="tag">&lt;resources></span>
<span class="lineno">  2 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_name"</span>>android-tool<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"action_settings"</span></span>>Settings<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>    <span class="comment">&lt;!-- Strings used for fragments for navigation --></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:5</span>: <span class="message">The resource <code>R.string.first_fragment_label</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  2 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_name"</span>>android-tool<span class="tag">&lt;/string></span>
<span class="lineno">  3 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"action_settings"</span>>Settings<span class="tag">&lt;/string></span>
<span class="lineno">  4 </span>    <span class="comment">&lt;!-- Strings used for fragments for navigation --></span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"first_fragment_label"</span></span>>First Fragment<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:6</span>: <span class="message">The resource <code>R.string.second_fragment_label</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"action_settings"</span>>Settings<span class="tag">&lt;/string></span>
<span class="lineno">  4 </span>    <span class="comment">&lt;!-- Strings used for fragments for navigation --></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"second_fragment_label"</span></span>>Second Fragment<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
<span class="lineno">  9 </span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:7</span>: <span class="message">The resource <code>R.string.next</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="comment">&lt;!-- Strings used for fragments for navigation --></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"next"</span></span>>Next<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"lorem_ipsum"</span>>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:8</span>: <span class="message">The resource <code>R.string.previous</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"first_fragment_label"</span>>First Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  6 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"second_fragment_label"</span>>Second Fragment<span class="tag">&lt;/string></span>
<span class="lineno">  7 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  8 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"previous"</span></span>>Previous<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>
<span class="lineno"> 10 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"lorem_ipsum"</span>>
<span class="lineno"> 11 </span>      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:10</span>: <span class="message">The resource <code>R.string.lorem_ipsum</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"next"</span>>Next<span class="tag">&lt;/string></span>
<span class="lineno">  8 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"previous"</span>>Previous<span class="tag">&lt;/string></span>
<span class="lineno">  9 </span>
<span class="caretline"><span class="lineno"> 10 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"lorem_ipsum"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris
<span class="lineno"> 12 </span>      volutpat, dolor id interdum ullamcorper, risus dolor egestas lectus, sit amet mattis purus
<span class="lineno"> 13 </span>      dui nec risus. Maecenas non sodales nisi, vel dictum dolor. Class aptent taciti sociosqu ad
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:46</span>: <span class="message">The resource <code>R.string.title_activity_settings</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 43 </span>      libero vel nunc consequat, quis tincidunt nisl eleifend. Cras bibendum enim a justo luctus
<span class="lineno"> 44 </span>      vestibulum. Fusce dictum libero quis erat maximus, vitae volutpat diam dignissim.
<span class="lineno"> 45 </span>  <span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 46 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"title_activity_settings"</span></span>>SettingsActivity<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>  <span class="comment">&lt;!-- Preference Titles --></span>
<span class="lineno"> 49 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"messages_header"</span>>Messages<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:49</span>: <span class="message">The resource <code>R.string.messages_header</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 46 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"title_activity_settings"</span>>SettingsActivity<span class="tag">&lt;/string></span>
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>    <span class="comment">&lt;!-- Preference Titles --></span>
<span class="caretline"><span class="lineno"> 49 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"messages_header"</span></span>>Messages<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 50 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sync_header"</span>>Sync<span class="tag">&lt;/string></span>
<span class="lineno"> 51 </span>
<span class="lineno"> 52 </span>    <span class="comment">&lt;!-- Messages Preferences --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:50</span>: <span class="message">The resource <code>R.string.sync_header</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>    <span class="comment">&lt;!-- Preference Titles --></span>
<span class="lineno"> 49 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"messages_header"</span>>Messages<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 50 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"sync_header"</span></span>>Sync<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>
<span class="lineno"> 52 </span>    <span class="comment">&lt;!-- Messages Preferences --></span>
<span class="lineno"> 53 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"signature_title"</span>>Your signature<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:53</span>: <span class="message">The resource <code>R.string.signature_title</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 50 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sync_header"</span>>Sync<span class="tag">&lt;/string></span>
<span class="lineno"> 51 </span>
<span class="lineno"> 52 </span>    <span class="comment">&lt;!-- Messages Preferences --></span>
<span class="caretline"><span class="lineno"> 53 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"signature_title"</span></span>>Your signature<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 54 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"reply_title"</span>>Default reply action<span class="tag">&lt;/string></span>
<span class="lineno"> 55 </span>
<span class="lineno"> 56 </span>    <span class="comment">&lt;!-- Sync Preferences --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:54</span>: <span class="message">The resource <code>R.string.reply_title</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 51 </span>
<span class="lineno"> 52 </span>    <span class="comment">&lt;!-- Messages Preferences --></span>
<span class="lineno"> 53 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"signature_title"</span>>Your signature<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 54 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"reply_title"</span></span>>Default reply action<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 55 </span>
<span class="lineno"> 56 </span>    <span class="comment">&lt;!-- Sync Preferences --></span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sync_title"</span>>Sync email periodically<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:57</span>: <span class="message">The resource <code>R.string.sync_title</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 54 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"reply_title"</span>>Default reply action<span class="tag">&lt;/string></span>
<span class="lineno"> 55 </span>
<span class="lineno"> 56 </span>  <span class="comment">&lt;!-- Sync Preferences --></span>
<span class="caretline"><span class="lineno"> 57 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"sync_title"</span></span>>Sync email periodically<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 58 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_title"</span>>Download incoming attachments<span class="tag">&lt;/string></span>
<span class="lineno"> 59 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_summary_on"</span>>Automatically download attachments for incoming emails
<span class="lineno"> 60 </span>  <span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:58</span>: <span class="message">The resource <code>R.string.attachment_title</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 55 </span>
<span class="lineno"> 56 </span>  <span class="comment">&lt;!-- Sync Preferences --></span>
<span class="lineno"> 57 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sync_title"</span>>Sync email periodically<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 58 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"attachment_title"</span></span>>Download incoming attachments<span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 59 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_summary_on"</span>>Automatically download attachments for incoming emails
<span class="lineno"> 60 </span>  <span class="tag">&lt;/string></span>
<span class="lineno"> 61 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_summary_off"</span>>Only download attachments when manually requested<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:59</span>: <span class="message">The resource <code>R.string.attachment_summary_on</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span>    <span class="comment">&lt;!-- Sync Preferences --></span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"sync_title"</span>>Sync email periodically<span class="tag">&lt;/string></span>
<span class="lineno"> 58 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_title"</span>>Download incoming attachments<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 59 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"attachment_summary_on"</span></span>>Automatically download attachments for incoming emails
&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 60 </span>    <span class="tag">&lt;/string></span>
<span class="lineno"> 61 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_summary_off"</span>>Only download attachments when manually requested<span class="tag">&lt;/string></span>
<span class="lineno"> 62 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:61</span>: <span class="message">The resource <code>R.string.attachment_summary_off</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 58 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_title"</span>>Download incoming attachments<span class="tag">&lt;/string></span>
<span class="lineno"> 59 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"attachment_summary_on"</span>>Automatically download attachments for incoming emails
<span class="lineno"> 60 </span>    <span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno"> 61 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"attachment_summary_off"</span></span>>Only download attachments when manually requested<span class="tag">&lt;/string></span>
</span>
<span class="lineno"> 62 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values-night/themes.xml">../../src/main/res/values-night/themes.xml</a>:3</span>: <span class="message">The resource <code>R.style.Base_Theme_Snettool</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;resources</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>>
<span class="lineno"> 2 </span>    <span class="comment">&lt;!-- Base application theme. --></span>
<span class="caretline"><span class="lineno"> 3 </span>    <span class="tag">&lt;style</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"Base.Theme.Snettool"</span></span> <span class="attribute">parent</span>=<span class="value">"Theme.Material3.DayNight.NoActionBar"</span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>        <span class="comment">&lt;!-- Customize your dark theme here. --></span>
<span class="lineno"> 5 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>#F5F5F5<span class="tag">&lt;/item></span>
<span class="lineno"> 6 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span> <span class="prefix">tools:</span><span class="attribute">targetApi</span>=<span class="value">"l"</span>>#000000<span class="tag">&lt;/item></span> <span class="comment">&lt;!-- Black --></span>
</pre>

<span class="location"><a href="../../src/main/res/values-v23/themes.xml">../../src/main/res/values-v23/themes.xml</a>:3</span>: <span class="message">The resource <code>R.style.Theme_Snettool</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;resources</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>>
<span class="lineno"> 2 </span>
<span class="caretline"><span class="lineno"> 3 </span>    <span class="tag">&lt;style</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"Theme.Snettool"</span></span> <span class="attribute">parent</span>=<span class="value">"Base.Theme.Snettool"</span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>        <span class="comment">&lt;!-- Transparent system bars for edge-to-edge. --></span>
<span class="lineno"> 5 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:navigationBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
<span class="lineno"> 6 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UselessParent"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UselessParentCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unnecessary parent layout</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:10</span>: <span class="message">This <code>LinearLayout</code> layout or its <code>FrameLayout</code> parent is unnecessary; transfer the <code>background</code> attribute to the other view</span><br /><pre class="errorlines">
<span class="lineno">   7 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">   8 </span>    <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/app_background_color"</span>
<span class="lineno">   9 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".ui.MainFragment"</span>>
<span class="caretline"><span class="lineno">  10 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
<span class="lineno">  12 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  13 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUselessParent" style="display: none;">
A layout with children that has no siblings, is not a scrollview or a root layout, and does not have a background, can be removed and have its children moved directly into the parent for a flatter and more efficient layout hierarchy.<br/>To suppress this error, use the issue id "UselessParent" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UselessParent</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUselessParentLink" onclick="reveal('explanationUselessParent');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UselessParentCardLink" onclick="hideid('UselessParentCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RedundantNamespace"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RedundantNamespaceCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Redundant namespace</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:12</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>    <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"3dp"</span>>
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="caretline"><span class="lineno"> 12 </span><span class="attribute">        </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno"> 14 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:13</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 12 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="caretline"><span class="lineno"> 13 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:14</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 12 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno"> 13 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="caretline"><span class="lineno"> 14 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:52</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno"> 49 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 50 </span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="caretline"><span class="lineno"> 52 </span><span class="attribute">        </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 53 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno"> 54 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:53</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno"> 50 </span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 52 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="caretline"><span class="lineno"> 53 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 54 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 56 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:54</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno"> 51 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 52 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno"> 53 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="caretline"><span class="lineno"> 54 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 56 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 57 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRedundantNamespace" style="display: none;">
In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RedundantNamespace" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RedundantNamespace</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 1/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRedundantNamespaceLink" onclick="reveal('explanationRedundantNamespace');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RedundantNamespaceCardLink" onclick="hideid('RedundantNamespaceCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedNamespace"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedNamespaceCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused namespace</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:12</span>: <span class="message">Unused namespace declaration xmlns:android; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>    <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"3dp"</span>>
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="caretline"><span class="lineno"> 12 </span><span class="attribute">        </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno"> 14 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:13</span>: <span class="message">Unused namespace declaration xmlns:app; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 12 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="caretline"><span class="lineno"> 13 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:14</span>: <span class="message">Unused namespace declaration xmlns:tools; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 12 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno"> 13 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="caretline"><span class="lineno"> 14 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:52</span>: <span class="message">Unused namespace declaration xmlns:android; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno"> 49 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 50 </span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="caretline"><span class="lineno"> 52 </span><span class="attribute">        </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 53 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno"> 54 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:53</span>: <span class="message">Unused namespace declaration xmlns:app; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno"> 50 </span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 52 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="caretline"><span class="lineno"> 53 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 54 </span>        <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 56 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:54</span>: <span class="message">Unused namespace declaration xmlns:tools; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno"> 51 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span><span class="lineno"> 52 </span><span class="attribute">        </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno"> 53 </span>        <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="caretline"><span class="lineno"> 54 </span>        <span class="warning"><span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 56 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 57 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedNamespace" style="display: none;">
Unused namespace declarations take up space and require processing that is not necessary<br/>To suppress this error, use the issue id "UnusedNamespace" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedNamespace</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 1/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedNamespaceLink" onclick="reveal('explanationUnusedNamespace');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedNamespaceCardLink" onclick="hideid('UnusedNamespaceCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability:Icons"></a>
<a name="IconXmlAndPng"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconXmlAndPngCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Icon is specified both as .xml file and as a bitmap</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable-xxhdpi/ic_action_home.png">../../src/main/res/drawable-xxhdpi/ic_action_home.png</a></span>: <span class="message">The following images appear both as density independent <code>.xml</code> files and as bitmap files: srcmainresdrawable-anydpiic_action_home.xml, srcmainresdrawable-hdpiic_action_home.png</span><br />
<ul></ul><button id="Location1DivLink" onclick="reveal('Location1Div');" />+ 4 Additional Locations...</button>
<div id="Location1Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-xhdpi/ic_action_home.png">../../src/main/res/drawable-xhdpi/ic_action_home.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-mdpi/ic_action_home.png">../../src/main/res/drawable-mdpi/ic_action_home.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-hdpi/ic_action_home.png">../../src/main/res/drawable-hdpi/ic_action_home.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-anydpi/ic_action_home.xml">../../src/main/res/drawable-anydpi/ic_action_home.xml</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-mdpi/ic_action_home.png"><img border="0" align="top" src="../../src/main/res/drawable-mdpi/ic_action_home.png" /></a>
</td><td><a href="../../src/main/res/drawable-hdpi/ic_action_home.png"><img border="0" align="top" src="../../src/main/res/drawable-hdpi/ic_action_home.png" /></a>
</td><td><a href="../../src/main/res/drawable-xhdpi/ic_action_home.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/ic_action_home.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxhdpi/ic_action_home.png"><img border="0" align="top" src="../../src/main/res/drawable-xxhdpi/ic_action_home.png" /></a>
</td></tr><tr><th>mdpi</th><th>hdpi</th><th>xhdpi</th><th>xxhdpi</th></tr>
</table>
</div>
<div class="metadata"><div class="explanation" id="explanationIconXmlAndPng" style="display: none;">
If a drawable resource appears as an <code>.xml</code> file in the <code>drawable/</code> folder, it's usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.<br/>To suppress this error, use the issue id "IconXmlAndPng" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconXmlAndPng</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 7/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconXmlAndPngLink" onclick="reveal('explanationIconXmlAndPng');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconXmlAndPngCardLink" onclick="hideid('IconXmlAndPngCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="IconColors"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconColorsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Icon colors do not follow the recommended visual style</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable-hdpi/ic_action_ok.png">../../src/main/res/drawable-hdpi/ic_action_ok.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/ic_action_ok.png" /><span class="message">Action Bar icons should use a single gray color (<code>#333333</code> for light themes (with 60%/30% opacity for enabled/disabled), and <code>#FFFFFF</code> with opacity 80%/30% for dark themes</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-mdpi/ic_action_ok.png">../../src/main/res/drawable-mdpi/ic_action_ok.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-mdpi/ic_action_ok.png" /><span class="message">Action Bar icons should use a single gray color (<code>#333333</code> for light themes (with 60%/30% opacity for enabled/disabled), and <code>#FFFFFF</code> with opacity 80%/30% for dark themes</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-xhdpi/ic_action_ok.png">../../src/main/res/drawable-xhdpi/ic_action_ok.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-xhdpi/ic_action_ok.png" /><span class="message">Action Bar icons should use a single gray color (<code>#333333</code> for light themes (with 60%/30% opacity for enabled/disabled), and <code>#FFFFFF</code> with opacity 80%/30% for dark themes</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-xxhdpi/ic_action_ok.png">../../src/main/res/drawable-xxhdpi/ic_action_ok.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-xxhdpi/ic_action_ok.png" /><span class="message">Action Bar icons should use a single gray color (<code>#333333</code> for light themes (with 60%/30% opacity for enabled/disabled), and <code>#FFFFFF</code> with opacity 80%/30% for dark themes</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-xxxhdpi/ic_action_ok.png">../../src/main/res/drawable-xxxhdpi/ic_action_ok.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-xxxhdpi/ic_action_ok.png" /><span class="message">Action Bar icons should use a single gray color (<code>#333333</code> for light themes (with 60%/30% opacity for enabled/disabled), and <code>#FFFFFF</code> with opacity 80%/30% for dark themes</span><br clear="right"/>
</div>
<div class="metadata"><div class="explanation" id="explanationIconColors" style="display: none;">
Notification icons and Action Bar icons should only white and shades of gray. See the Android Design Guide for more details. Note that the way Lint decides whether an icon is an action bar icon or a notification icon is based on the filename prefix: <code>ic_menu_</code> for action bar icons, <code>ic_stat_</code> for notification icons etc. These correspond to the naming conventions documented in <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a><br/>To suppress this error, use the issue id "IconColors" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconColors</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconColorsLink" onclick="reveal('explanationIconColors');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconColorsCardLink" onclick="hideid('IconColorsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="IconLocation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconLocationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image defined in density-independent drawable folder</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/debug.png">../../src/main/res/drawable/debug.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/debug.png" /><span class="message">Found bitmap drawable <code>res/drawable/debug.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/debug_select.png">../../src/main/res/drawable/debug_select.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/debug_select.png" /><span class="message">Found bitmap drawable <code>res/drawable/debug_select.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/home.png">../../src/main/res/drawable/home.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/home.png" /><span class="message">Found bitmap drawable <code>res/drawable/home.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/home_select.png">../../src/main/res/drawable/home_select.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/home_select.png" /><span class="message">Found bitmap drawable <code>res/drawable/home_select.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/setting.png">../../src/main/res/drawable/setting.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/setting.png" /><span class="message">Found bitmap drawable <code>res/drawable/setting.png</code> in densityless folder</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/setting_select.png">../../src/main/res/drawable/setting_select.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/setting_select.png" /><span class="message">Found bitmap drawable <code>res/drawable/setting_select.png</code> in densityless folder</span><br clear="right"/>
</div>
<div class="metadata"><div class="explanation" id="explanationIconLocation" style="display: none;">
The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to <code>drawable-mdpi</code> and consider providing higher and lower resolution versions in <code>drawable-ldpi</code>, <code>drawable-hdpi</code> and <code>drawable-xhdpi</code>. If the icon <b>really</b> is density independent (for example a solid color) you can place it in <code>drawable-nodpi</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconLocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconLocation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconLocationLink" onclick="reveal('explanationIconLocation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconLocationCardLink" onclick="hideid('IconLocationCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability"></a>
<a name="ButtonStyle"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ButtonStyleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Button should be borderless</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:57</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno">  54 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno">  55 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>>
<span class="lineno">  56 </span>
<span class="caretline"><span class="lineno">  57 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  58 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnExecuteJS"</span>
<span class="lineno">  59 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno">  60 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:67</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno">  64 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Execute"</span>
<span class="lineno">  65 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>/>
<span class="lineno">  66 </span>
<span class="caretline"><span class="lineno">  67 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  68 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnClearJS"</span>
<span class="lineno">  69 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno">  70 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:95</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno">  92 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno">  93 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"8dp"</span>>
<span class="lineno">  94 </span>
<span class="caretline"><span class="lineno">  95 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  96 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnTestDeviceInfo"</span>
<span class="lineno">  97 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno">  98 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:106</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 103 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="lineno"> 104 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>/>
<span class="lineno"> 105 </span>
<span class="caretline"><span class="lineno"> 106 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 107 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnTestPhones"</span>
<span class="lineno"> 108 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 109 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:118</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 115 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="lineno"> 116 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>/>
<span class="lineno"> 117 </span>
<span class="caretline"><span class="lineno"> 118 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 119 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnTestBattery"</span>
<span class="lineno"> 120 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 121 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_settings.xml">../../src/main/res/layout/fragment_settings.xml</a>:54</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 51 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_horizontal"</span>
<span class="lineno"> 52 </span>                <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 53 </span>
<span class="caretline"><span class="lineno"> 54 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 55 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnLogout"</span>
<span class="lineno"> 56 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 57 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_settings.xml">../../src/main/res/layout/fragment_settings.xml</a>:65</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 62 </span>                    <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"4dp"</span>
<span class="lineno"> 63 </span>                    <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Logout"</span> />
<span class="lineno"> 64 </span>
<span class="caretline"><span class="lineno"> 65 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 66 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnExit"</span>
<span class="lineno"> 67 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 68 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationButtonStyle" style="display: none;">
Button bars typically use a borderless style for the buttons. Set the <code>style="?android:attr/buttonBarButtonStyle"</code> attribute on each of the buttons, and set <code>style="?android:attr/buttonBarStyle"</code> on the parent layout<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/dialogs">https://d.android.com/r/studio-ui/designer/material/dialogs</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ButtonStyle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ButtonStyle</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationButtonStyleLink" onclick="reveal('explanationButtonStyle');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ButtonStyleCardLink" onclick="hideid('ButtonStyleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="TextFields"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="TextFieldsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing inputType</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:27</span>: <span class="message">This text field does not specify an <code>inputType</code></span><br /><pre class="errorlines">
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"User Name"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#2c2c2c"</span>
<span class="lineno"> 26 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
<span class="caretline"><span class="lineno"> 27 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 28 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 29 </span>        <span class="prefix">android:</span><span class="attribute">textColorHint</span>=<span class="value">"#a5b7c6"</span>
<span class="lineno"> 30 </span>        <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/et_account"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationTextFields" style="display: none;">
Providing an <code>inputType</code> attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). <br/>
<br/>
The lint detector also looks at the <code>id</code> of the view, and if the id offers a hint of the purpose of the field (for example, the <code>id</code> contains the phrase <code>phone</code> or <code>email</code>), then lint will also ensure that the <code>inputType</code> contains the corresponding type attributes.<br/>
<br/>
If you really want to keep the text field generic, you can suppress this warning by setting <code>inputType="text"</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TextFields" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">TextFields</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationTextFieldsLink" onclick="reveal('explanationTextFields');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="TextFieldsCardLink" onclick="hideid('TextFieldsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Autofill"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AutofillCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use Autofill</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:27</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"User Name"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#2c2c2c"</span>
<span class="lineno"> 26 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
<span class="caretline"><span class="lineno"> 27 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 28 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 29 </span>        <span class="prefix">android:</span><span class="attribute">textColorHint</span>=<span class="value">"#a5b7c6"</span>
<span class="lineno"> 30 </span>        <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/et_account"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:48</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno"> 45 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Password"</span>
<span class="lineno"> 46 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#2c2c2c"</span>
<span class="lineno"> 47 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
<span class="caretline"><span class="lineno"> 48 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 49 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 50 </span>        <span class="prefix">android:</span><span class="attribute">textColorHint</span>=<span class="value">"#a5b7c6"</span>
<span class="lineno"> 51 </span>        <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/et_password"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:36</span>: <span class="message">Missing <code>autofillHints</code> attribute</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  34 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"8dp"</span>/>
<span class="lineno">  35 </span>
<span class="caretline"><span class="lineno">  36 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">EditText</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  37 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/etJavaScript"</span>
<span class="lineno">  38 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  39 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"120dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAutofill" style="display: none;">
Specify an <code>autofillHints</code> attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.<br/>
<br/>
The hints can have any value, but it is recommended to use predefined values like 'username' for a username or 'creditCardNumber' for a credit card number. For a list of all predefined autofill hint constants, see the <code>AUTOFILL_HINT_</code> constants in the <code>View</code> reference at <a href="https://developer.android.com/reference/android/view/View.html">https://developer.android.com/reference/android/view/View.html</a>.<br/>
<br/>
You can mark a view unimportant for autofill by specifying an <code>importantForAutofill</code> attribute on that view or a parent view. See <a href="https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)">https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)</a>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/text/autofill.html">https://developer.android.com/guide/topics/text/autofill.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Autofill" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Autofill</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAutofillLink" onclick="reveal('explanationAutofill');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AutofillCardLink" onclick="hideid('AutofillCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Accessibility"></a>
<a name="ClickableViewAccessibility"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ClickableViewAccessibilityCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Accessibility in Custom Views</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/ui/ItemViewTouchListener.java">../../src/main/java/com/bm/atool/ui/ItemViewTouchListener.java</a>:30</span>: <span class="message"><code>ItemViewTouchListener#onTouch</code> should call <code>View#performClick</code> when a click is detected</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>    }
<span class="lineno"> 28 </span>
<span class="lineno"> 29 </span>    <span class="annotation">@Override</span>
<span class="caretline"><span class="lineno"> 30 </span>    <span class="keyword">public</span> <span class="keyword">boolean</span> <span class="warning">onTouch</span>(View v, MotionEvent motionEvent) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>        <span class="keyword">switch</span> (motionEvent.getAction()){
<span class="lineno"> 32 </span>            <span class="keyword">case</span> MotionEvent.ACTION_DOWN:{
<span class="lineno"> 33 </span>                x = motionEvent.getRawX();
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationClickableViewAccessibility" style="display: none;">
If a <code>View</code> that overrides <code>onTouchEvent</code> or uses an <code>OnTouchListener</code> does not also implement <code>performClick</code> and call it when clicks are detected, the <code>View</code> may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in <code>View#performClick</code> as some accessibility services invoke <code>performClick</code> when a click action should occur.<br/>To suppress this error, use the issue id "ClickableViewAccessibility" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ClickableViewAccessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationClickableViewAccessibilityLink" onclick="reveal('explanationClickableViewAccessibility');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ClickableViewAccessibilityCardLink" onclick="hideid('ClickableViewAccessibilityCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ContentDescription"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ContentDescriptionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image without contentDescription</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_float_item.xml">../../src/main/res/layout/activity_float_item.xml</a>:7</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>>
<span class="lineno">  6 </span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/iconLogo"</span>
<span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"32dp"</span>
<span class="lineno"> 10 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"32dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:25</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  22 </span>            <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"8dp"</span>
<span class="lineno">  23 </span>            <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"4dp"</span>>
<span class="lineno">  24 </span>
<span class="caretline"><span class="lineno">  25 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  26 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView"</span>
<span class="lineno">  27 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  28 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:46</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  43 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  44 </span>                <span class="prefix">android:</span><span class="attribute">textAppearance</span>=<span class="value">"@style/TextAppearance.AppCompat.Medium"</span>/>
<span class="lineno">  45 </span>
<span class="caretline"><span class="lineno">  46 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  47 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imgStatus"</span>
<span class="lineno">  48 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"30dp"</span>
<span class="lineno">  49 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"30dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/setting_row.xml">../../src/main/res/layout/setting_row.xml</a>:17</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 14 </span>    <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 15 </span>    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span> />
<span class="lineno"> 16 </span>
<span class="caretline"><span class="lineno"> 17 </span><span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 18 </span><span class="attribute">    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imgStatus"</span>
<span class="lineno"> 19 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"36dp"</span>
<span class="lineno"> 20 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"36dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:19</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"1dp"</span>>
<span class="caretline"><span class="lineno"> 19 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 20 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imageView"</span>
<span class="lineno"> 21 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"40dp"</span>
<span class="lineno"> 22 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"40dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationContentDescription" style="display: none;">
Non-textual widgets like ImageViews and ImageButtons should use the <code>contentDescription</code> attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.<br/>
<br/>
Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to <code>@null</code>. If your app's minSdkVersion is 16 or higher, you can instead set these graphical elements' <code>android:importantForAccessibility</code> attributes to <code>no</code>.<br/>
<br/>
Note that for text fields, you should not set both the <code>hint</code> and the <code>contentDescription</code> attributes since the hint will never be shown. Just set the <code>hint</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases">https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ContentDescription" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ContentDescription</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationContentDescriptionLink" onclick="reveal('explanationContentDescription');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ContentDescriptionCardLink" onclick="hideid('ContentDescriptionCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/ui/DebugFragment.java">../../src/main/java/com/bm/atool/ui/DebugFragment.java</a>:157</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 154 </span>
<span class="lineno"> 155 </span>    <span class="keyword">private</span> <span class="keyword">void</span> clearJavaScript() {
<span class="lineno"> 156 </span>        etJavaScript.setText(<span class="string">""</span>);
<span class="caretline"><span class="lineno"> 157 </span>        tvJSResult.setText(<span class="warning"><span class="string">"No result yet..."</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 158 </span>    }
<span class="lineno"> 159 </span>
<span class="lineno"> 160 </span>    <span class="keyword">private</span> <span class="keyword">void</span> loadTestScript(String script) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/ui/adapters/PhoneAdapter.java">../../src/main/java/com/bm/atool/ui/adapters/PhoneAdapter.java</a>:56</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  53 </span>    <span class="keyword">public</span> <span class="keyword">void</span> onBindViewHolder(<span class="annotation">@NonNull</span> ViewHolder holder, <span class="keyword">int</span> position) {
<span class="lineno">  54 </span>        <span class="keyword">final</span> SubscriptionInfoModel currentItem = <span class="keyword">this</span>.dataSet.get(position);
<span class="lineno">  55 </span>
<span class="caretline"><span class="lineno">  56 </span>        holder.txtSlot.setText(<span class="warning"><span class="string">"PHONE"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  57 </span>        holder.txtDisplayName.setText(currentItem.carrier);
<span class="lineno">  58 </span>        holder.txtSubscriptionId.setText(<span class="string">"ID:"</span> + currentItem.subscriptionId);
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/ui/adapters/PhoneAdapter.java">../../src/main/java/com/bm/atool/ui/adapters/PhoneAdapter.java</a>:58</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno">  55 </span>
<span class="lineno">  56 </span>        holder.txtSlot.setText(<span class="string">"PHONE"</span>);
<span class="lineno">  57 </span>        holder.txtDisplayName.setText(currentItem.carrier);
<span class="caretline"><span class="lineno">  58 </span>        holder.txtSubscriptionId.setText(<span class="warning"><span class="string">"ID:"</span> + currentItem.subscriptionId</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  59 </span>
<span class="lineno">  60 </span>        holder.edtPhoneNumber.setText(currentItem.phoneNumber);
<span class="lineno">  61 </span>        <span class="keyword">if</span> (!currentItem.isEditable) {
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/ui/adapters/PhoneAdapter.java">../../src/main/java/com/bm/atool/ui/adapters/PhoneAdapter.java</a>:58</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  55 </span>
<span class="lineno">  56 </span>        holder.txtSlot.setText(<span class="string">"PHONE"</span>);
<span class="lineno">  57 </span>        holder.txtDisplayName.setText(currentItem.carrier);
<span class="caretline"><span class="lineno">  58 </span>        holder.txtSubscriptionId.setText(<span class="warning"><span class="string">"ID:"</span></span> + currentItem.subscriptionId);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  59 </span>
<span class="lineno">  60 </span>        holder.edtPhoneNumber.setText(currentItem.phoneNumber);
<span class="lineno">  61 </span>        <span class="keyword">if</span> (!currentItem.isEditable) {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:24</span>: <span class="message">Hardcoded string "User Name", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 22 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"10sp"</span>
<span class="lineno"> 23 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"10dp"</span>
<span class="caretline"><span class="lineno"> 24 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"User Name"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#2c2c2c"</span>
<span class="lineno"> 26 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
<span class="lineno"> 27 </span>    <span class="tag">&lt;EditText</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:36</span>: <span class="message">Hardcoded string "Login ID", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"5dp"</span>
<span class="caretline"><span class="lineno"> 36 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"Login ID"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">singleLine</span>=<span class="value">"true"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">imeOptions</span>=<span class="value">"actionNext"</span>/>
<span class="lineno"> 39 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:45</span>: <span class="message">Hardcoded string "Password", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 42 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 43 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"10sp"</span>
<span class="lineno"> 44 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"10dp"</span>
<span class="caretline"><span class="lineno"> 45 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Password"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 46 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#2c2c2c"</span>
<span class="lineno"> 47 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
<span class="lineno"> 48 </span>    <span class="tag">&lt;EditText</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:57</span>: <span class="message">Hardcoded string "Password", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 54 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 56 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"5dp"</span>
<span class="caretline"><span class="lineno"> 57 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"Password"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 58 </span>        <span class="prefix">android:</span><span class="attribute">singleLine</span>=<span class="value">"true"</span>
<span class="lineno"> 59 </span>        <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"textPassword"</span>
<span class="lineno"> 60 </span>        <span class="prefix">android:</span><span class="attribute">imeOptions</span>=<span class="value">"actionDone"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:68</span>: <span class="message">Hardcoded string "Login", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 65 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56sp"</span>
<span class="lineno"> 66 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"25dp"</span>
<span class="lineno"> 67 </span>        <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#FFC107"</span>
<span class="caretline"><span class="lineno"> 68 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Login"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 69 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 70 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20sp"</span> />
<span class="lineno"> 71 </span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="HardcodedTextDivLink" onclick="reveal('HardcodedTextDiv');" />+ 21 More Occurrences...</button>
<div id="HardcodedTextDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:17</span>: <span class="message">Hardcoded string "Debug &amp; JavaScript Test", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  14 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/debugTitle"</span>
<span class="lineno">  15 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  16 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  17 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Debug &amp;amp; JavaScript Test"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  18 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  19 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno">  20 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:30</span>: <span class="message">Hardcoded string "JavaScript Test", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  27 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  28 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  29 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  30 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"JavaScript Test"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  31 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  32 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno">  33 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:43</span>: <span class="message">Hardcoded string "Enter JavaScript code here...", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  40 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/input_background"</span>
<span class="lineno">  41 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  42 </span>            <span class="prefix">android:</span><span class="attribute">textColorHint</span>=<span class="value">"@color/text_secondary"</span>
<span class="caretline"><span class="lineno">  43 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"Enter JavaScript code here..."</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  44 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"top|start"</span>
<span class="lineno">  45 </span>            <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"textMultiLine"</span>
<span class="lineno">  46 </span>            <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"12dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:64</span>: <span class="message">Hardcoded string "Execute", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  61 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno">  62 </span>                <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"@color/button_primary"</span>
<span class="lineno">  63 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="caretline"><span class="lineno">  64 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Execute"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  65 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>/>
<span class="lineno">  66 </span>
<span class="lineno">  67 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:74</span>: <span class="message">Hardcoded string "Clear", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  71 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno">  72 </span>                <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"@color/button_secondary"</span>
<span class="lineno">  73 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="caretline"><span class="lineno">  74 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Clear"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  75 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>/>
<span class="lineno">  76 </span>
<span class="lineno">  77 </span>        <span class="tag">&lt;/LinearLayout></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:83</span>: <span class="message">Hardcoded string "Quick Tests", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  81 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  82 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  83 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Quick Tests"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  85 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno">  86 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:102</span>: <span class="message">Hardcoded string "Device Info", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  99 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 100 </span>                <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"@color/button_accent"</span>
<span class="lineno"> 101 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="caretline"><span class="lineno"> 102 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Device Info"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 103 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="lineno"> 104 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>/>
<span class="lineno"> 105 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:113</span>: <span class="message">Hardcoded string "Phone Numbers", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 110 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 111 </span>                <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"@color/button_accent"</span>
<span class="lineno"> 112 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="caretline"><span class="lineno"> 113 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Phone Numbers"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 114 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="lineno"> 115 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="lineno"> 116 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:125</span>: <span class="message">Hardcoded string "Battery", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 122 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 123 </span>                <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"@color/button_accent"</span>
<span class="lineno"> 124 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="caretline"><span class="lineno"> 125 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Battery"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 126 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="lineno"> 127 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>/>
<span class="lineno"> 128 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:135</span>: <span class="message">Hardcoded string "Execution Result", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 132 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 133 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 134 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 135 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Execution Result"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 136 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno"> 137 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 138 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:151</span>: <span class="message">Hardcoded string "No result yet...", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 148 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="lineno"> 149 </span>            <span class="prefix">android:</span><span class="attribute">fontFamily</span>=<span class="value">"monospace"</span>
<span class="lineno"> 150 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"top|start"</span>
<span class="caretline"><span class="lineno"> 151 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"No result yet..."</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 152 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>/>
<span class="lineno"> 153 </span>
<span class="lineno"> 154 </span>        <span class="comment">&lt;!-- 系统控制按钮 --></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:158</span>: <span class="message">Hardcoded string "System Control", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 155 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 156 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 157 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 158 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"System Control"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 159 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno"> 160 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 161 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_debug.xml">../../src/main/res/layout/fragment_debug.xml</a>:173</span>: <span class="message">Hardcoded string "Stop All Services", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 170 </span>            <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"4dp"</span>
<span class="lineno"> 171 </span>            <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"12dp"</span>
<span class="lineno"> 172 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="caretline"><span class="lineno"> 173 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Stop All Services"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 174 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>/>
<span class="lineno"> 175 </span>
<span class="lineno"> 176 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:40</span>: <span class="message">Hardcoded string "username", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  37 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  38 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno">  39 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno">  40 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"username"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  41 </span>                <span class="prefix">android:</span><span class="attribute">textAlignment</span>=<span class="value">"viewStart"</span>
<span class="lineno">  42 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20sp"</span>
<span class="lineno">  43 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:88</span>: <span class="message">Hardcoded string "SMS:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  85 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  86 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  87 </span>                <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"text"</span>
<span class="caretline"><span class="lineno">  88 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"SMS:"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  89 </span>                <span class="prefix">android:</span><span class="attribute">paddingStart</span>=<span class="value">"8dp"</span>
<span class="lineno">  90 </span>                <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"8dp"</span>
<span class="lineno">  91 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_settings.xml">../../src/main/res/layout/fragment_settings.xml</a>:25</span>: <span class="message">Hardcoded string "Permissions", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 23 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 24 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 25 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Permissions"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno"> 27 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 28 </span>                <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_settings.xml">../../src/main/res/layout/fragment_settings.xml</a>:63</span>: <span class="message">Hardcoded string "Logout", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 60 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"3dp"</span>
<span class="lineno"> 61 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"3dp"</span>
<span class="lineno"> 62 </span>                    <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"4dp"</span>
<span class="caretline"><span class="lineno"> 63 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Logout"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 64 </span>
<span class="lineno"> 65 </span>                <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno"> 66 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnExit"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_settings.xml">../../src/main/res/layout/fragment_settings.xml</a>:74</span>: <span class="message">Hardcoded string "Exit", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 71 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"3dp"</span>
<span class="lineno"> 72 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"3dp"</span>
<span class="lineno"> 73 </span>                    <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"4dp"</span>
<span class="caretline"><span class="lineno"> 74 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Exit"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 75 </span>            <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 76 </span>        <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 77 </span>    <span class="tag">&lt;/ScrollView></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/phone_row.xml">../../src/main/res/layout/phone_row.xml</a>:32</span>: <span class="message">Hardcoded string "&#35831;&#36755;&#20837;&#25163;&#26426;&#21495;&#30721;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 29 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 30 </span>        <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 32 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"请输入手机号码"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"phone"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/setting_row.xml">../../src/main/res/layout/setting_row.xml</a>:13</span>: <span class="message">Hardcoded string "Android 6.0", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span><span class="attribute">    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/txtName"</span>
<span class="lineno"> 11 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 12 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Android 6.0"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>    <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 15 </span>    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span> />
<span class="lineno"> 16 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/setting_row.xml">../../src/main/res/layout/setting_row.xml</a>:42</span>: <span class="message">Hardcoded string "Grant", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>        <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"6dp"</span>
<span class="lineno"> 40 </span>        <span class="prefix">android:</span><span class="attribute">paddingBottom</span>=<span class="value">"2dp"</span>
<span class="lineno"> 41 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/surface_background"</span>
<span class="caretline"><span class="lineno"> 42 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Grant"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 43 </span>
<span class="lineno"> 44 </span><span class="tag">&lt;/RelativeLayout></span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization:Bidirectional Text"></a>
<a name="RtlSymmetry"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlSymmetryCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Padding and margin symmetry</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:32</span>: <span class="message">When you define <code>paddingLeft</code> you should probably also define <code>paddingRight</code> for right-to-left symmetry</span><br /><pre class="errorlines">
<span class="lineno"> 29 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"120dp"</span>
<span class="lineno"> 30 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 31 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 32 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingLeft</span></span>=<span class="value">"6dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 33 </span>            <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 34 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 35 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRtlSymmetry" style="display: none;">
If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry.<br/>To suppress this error, use the issue id "RtlSymmetry" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlSymmetry</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlSymmetryLink" onclick="reveal('explanationRtlSymmetry');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlSymmetryCardLink" onclick="hideid('RtlSymmetryCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RtlHardcoded"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlHardcodedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using left/right instead of start/end attributes</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bm/atool/ui/FloatingWindow.java">../../src/main/java/com/bm/atool/ui/FloatingWindow.java</a>:68</span>: <span class="message">Use "<code>Gravity.END</code>" instead of "<code>Gravity.RIGHT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno">  65 </span>      layoutParam.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
<span class="lineno">  66 </span>      layoutParam.width = WindowManager.LayoutParams.WRAP_CONTENT;
<span class="lineno">  67 </span>      layoutParam.height = WindowManager.LayoutParams.WRAP_CONTENT;
<span class="caretline"><span class="lineno">  68 </span>      layoutParam.gravity = Gravity.<span class="warning">RIGHT</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  69 </span>      <span class="keyword">return</span> layoutParam;
<span class="lineno">  70 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/bm/atool/service/singlepixel/SinglePixelActivity.java">../../src/main/java/com/bm/atool/service/singlepixel/SinglePixelActivity.java</a>:37</span>: <span class="message">Use "<code>Gravity.START</code>" instead of "<code>Gravity.LEFT</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno"> 34 </span>    <span class="keyword">protected</span> <span class="keyword">void</span> onCreate(<span class="annotation">@Nullable</span> Bundle savedInstanceState) {
<span class="lineno"> 35 </span>        <span class="keyword">super</span>.onCreate(savedInstanceState);
<span class="lineno"> 36 </span>        Window mWindow = getWindow();
<span class="caretline"><span class="lineno"> 37 </span>        mWindow.setGravity(Gravity.<span class="warning">LEFT</span> | Gravity.TOP);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 38 </span>        WindowManager.LayoutParams attrParams = mWindow.getAttributes();
<span class="lineno"> 39 </span>        attrParams.x = <span class="number">0</span>;
<span class="lineno"> 40 </span>        attrParams.y = <span class="number">0</span>;
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:35</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="5dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"10dp"</span>
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 35 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"5dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"Login ID"</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">singleLine</span>=<span class="value">"true"</span>
<span class="lineno"> 38 </span>        <span class="prefix">android:</span><span class="attribute">imeOptions</span>=<span class="value">"actionNext"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:56</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="5dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 53 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"10dp"</span>
<span class="lineno"> 54 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 55 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 56 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"5dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 57 </span>        <span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"Password"</span>
<span class="lineno"> 58 </span>        <span class="prefix">android:</span><span class="attribute">singleLine</span>=<span class="value">"true"</span>
<span class="lineno"> 59 </span>        <span class="prefix">android:</span><span class="attribute">inputType</span>=<span class="value">"textPassword"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:20</span>: <span class="message">Use "<code>start</code>" instead of "<code>left</code>" to ensure correct behavior in right-to-left locales</span><br /><pre class="errorlines">
<span class="lineno">  17 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  18 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"60dp"</span>
<span class="lineno">  19 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/header_background"</span>
<span class="caretline"><span class="lineno">  20 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"</span><span class="warning"><span class="value">left|center_vertical</span></span><span class="value">"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  21 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
<span class="lineno">  22 </span>            <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"8dp"</span>
<span class="lineno">  23 </span>            <span class="prefix">android:</span><span class="attribute">elevation</span>=<span class="value">"4dp"</span>>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="RtlHardcodedDivLink" onclick="reveal('RtlHardcodedDiv');" />+ 6 More Occurrences...</button>
<div id="RtlHardcodedDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/fragment_main.xml">../../src/main/res/layout/fragment_main.xml</a>:30</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="0dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  27 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  28 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  29 </span>                <span class="prefix">android:</span><span class="attribute">layout_gravity</span>=<span class="value">"center_vertical"</span>
<span class="caretline"><span class="lineno">  30 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"0dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  31 </span>                <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@mipmap/ic_launcher"</span>
<span class="lineno">  32 </span>                <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@mipmap/ic_launcher_round"</span> />
<span class="lineno">  33 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/setting_row.xml">../../src/main/res/layout/setting_row.xml</a>:21</span>: <span class="message">Consider replacing <code>android:layout_alignParentRight</code> with <code>android:layout_alignParentEnd="true"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 18 </span><span class="attribute">    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/imgStatus"</span>
<span class="lineno"> 19 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"36dp"</span>
<span class="lineno"> 20 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"36dp"</span>
<span class="caretline"><span class="lineno"> 21 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 22 </span>    <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 23 </span>    <span class="prefix">android:</span><span class="attribute">scaleType</span>=<span class="value">"centerInside"</span>
<span class="lineno"> 24 </span>    <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@drawable/ic_action_ok"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/setting_row.xml">../../src/main/res/layout/setting_row.xml</a>:33</span>: <span class="message">Redundant attribute <code>layout_alignParentRight</code>; already defining <code>layout_alignParentEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 30 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"32dp"</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentEnd</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 33 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"@color/accent_color"</span>
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">maxHeight</span>=<span class="value">"32dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:24</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="0dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"40dp"</span>
<span class="lineno"> 22 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"40dp"</span>
<span class="lineno"> 23 </span>            <span class="prefix">android:</span><span class="attribute">layout_gravity</span>=<span class="value">"center_horizontal|center_vertical"</span>
<span class="caretline"><span class="lineno"> 24 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"0dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>            <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@mipmap/ic_launcher"</span>
<span class="lineno"> 26 </span>            <span class="prefix">app:</span><span class="attribute">srcCompat</span>=<span class="value">"@mipmap/ic_launcher_round"</span> />
<span class="lineno"> 27 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:32</span>: <span class="message">Consider replacing <code>android:paddingLeft</code> with <code>android:paddingStart="6dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 29 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"120dp"</span>
<span class="lineno"> 30 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 31 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 32 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingLeft</span></span>=<span class="value">"6dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 33 </span>            <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 34 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 35 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/sms_row.xml">../../src/main/res/layout/sms_row.xml</a>:65</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="48dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 62 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 63 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 64 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="caretline"><span class="lineno"> 65 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"48dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 66 </span>            <span class="prefix">android:</span><span class="attribute">layout_centerVertical</span>=<span class="value">"true"</span>
<span class="lineno"> 67 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/black"</span> />
<span class="lineno"> 68 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationRtlHardcoded" style="display: none;">
Using <code>Gravity#LEFT</code> and <code>Gravity#RIGHT</code> can lead to problems when a layout is rendered in locales where text flows from right to left. Use <code>Gravity#START</code> and <code>Gravity#END</code> instead. Similarly, in XML <code>gravity</code> and <code>layout_gravity</code> attributes, use <code>start</code> rather than <code>left</code>.<br/>
<br/>
For XML attributes such as paddingLeft and <code>layout_marginLeft</code>, use <code>paddingStart</code> and <code>layout_marginStart</code>. <b>NOTE</b>: If your <code>minSdkVersion</code> is less than 17, you should add <b>both</b> the older left/right attributes <b>as well as</b> the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.<br/>
<br/>
(Note: For <code>Gravity#LEFT</code> and <code>Gravity#START</code>, you can use these constants even when targeting older platforms, because the <code>start</code> bitmask is a superset of the <code>left</code> bitmask. Therefore, you can use <code>gravity="start"</code> rather than <code>gravity="left|start"</code>.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlHardcoded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlHardcoded</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlHardcodedLink" onclick="reveal('explanationRtlHardcoded');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlHardcodedCardLink" onclick="hideid('RtlHardcodedCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">DeepLinkInActivityDestination<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attaching a &lt;deeplink> to an &lt;activity> destination will never give                 the right behavior when using an implicit deep link on another app's task                 (where the system back should immediately take the user back to the app that                 triggered the deep link). Instead, attach the deep link directly to                 the second activity (either by manually writing the appropriate &lt;intent-filter>                 or by adding the &lt;deeplink> to the start destination of a nav host in that second                 activity).<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DetachAndAttachSameFragment<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When doing a FragmentTransaction that includes both attach()                 and detach() operations being committed on the same fragment instance, it is a                 no-op. The reason for this is that the FragmentManager optimizes all operations                 within a single transaction so the attach() and detach() cancel each other out                 and neither is actually executed. To get the desired behavior, you should separate                 the attach() and detach() calls into separate FragmentTransactions.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DialogFragmentCallbacksDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using a <code>DialogFragment</code>, the <code>setOnCancelListener</code> and                 <code>setOnDismissListener</code> callback functions within the <code>onCreateDialog</code> function                  __must not be used__ because the <code>DialogFragment</code> owns these callbacks.                  Instead the respective <code>onCancel</code> and <code>onDismiss</code> functions can be used to                  achieve the desired effect.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EmptyNavDeepLink<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attempting to create an empty NavDeepLink will result in an IllegalStateException at runtime. You may set these arguments within the lambda of the call to navDeepLink.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentAddMenuProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeRepeatOnLifecycleDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used with the viewLifecycleOwner                 in Fragments as opposed to lifecycleOwner.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAndroidAlpha<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ColorStateList</code> uses app:alpha without <code>android:alpha</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAppTint<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForColorStateLists<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of color state lists<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableApis<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of compound text view drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>TextView</code> uses <code>android:</code> compound drawable attributes instead of <code>app:</code> ones<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseGetLayoutInflater<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Using LayoutInflater.from(Context) can return a LayoutInflater                  that does not have the correct theme.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSupportActionBar<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>AppCompatActivity.setSupportActionBar</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialCode<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingOnClickInXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Old versions of the platform do not properly support resolving <code>android:onClick</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongRequiresOptIn<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental features defined in Kotlin source code must be annotated with the Kotlin<br/>
<code>@RequiresOptIn</code> annotation. Using <code>androidx.annotation.RequiresOptIn</code> will prevent the<br/>
Kotlin compiler from enforcing its opt-in policies.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImplicitSamInstance<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Kotlin's support for SAM (single accessor method) interfaces lets you pass a lambda to the interface. This will create a new instance on the fly even though there is no explicit constructor call. If you pass one of these lambdas or method references into a method which (for example) stores or compares the object identity, unexpected results may happen.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>isEquivalentTo(PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). This can make the text more readable. Note that you should never use grave accents and apostrophes to quote, `like this'. (Also note that you should not use curvy quotes for code fragments.)<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>