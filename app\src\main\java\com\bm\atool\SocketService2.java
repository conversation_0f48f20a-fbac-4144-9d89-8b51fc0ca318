//package com.bm.atool;
//
//
//import android.app.Notification;
//import android.app.NotificationChannel;
//import android.app.NotificationManager;
//import android.app.PendingIntent;
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.content.IntentFilter;
//import android.os.BatteryManager;
//import android.os.Build;
//import android.os.Handler;
//import android.os.IBinder;
//import android.os.Message;
//import android.os.Messenger;
//import android.util.Log;
//
//import androidx.annotation.Nullable;
//import androidx.annotation.RequiresApi;
//import androidx.core.app.NotificationCompat;
//
//import com.bm.atool.model.PingModel;
//import com.bm.atool.model.PongModel;
//import com.bm.atool.model.SendSmsRequest;
//import com.bm.atool.ui.FloatingWindow;
//import com.google.gson.Gson;
//
//import java.net.URI;
//import java.net.URLEncoder;
//import java.util.Objects;
//import java.util.Timer;
//import java.util.TimerTask;
//
//import io.socket.client.IO;
//import io.socket.client.Socket;
//import io.socket.emitter.Emitter;
//
//public final class SocketService2 extends PlayerMusicService {
//    public   static int NOTICE_ID = 1;
//    private NotificationManager notificationManager;
//
//    private BatteryManager batteryManager;
//    private static int counter = 0;
//    private String notificationId = "integrity";
//    private String notificationName = "integrity";
//    private String token;
//    private static final String TAG = SocketService2.class.getSimpleName();
//    TimerTask timerTask;
//    private  FloatingWindow floatingWindow = null;
//
//    // SocketIO
//    private IO.Options options = new IO.Options();
//    private Socket socket = null;
//    private Timer timer = null;
//
//    private final BroadcastReceiver accessibilityReceiver = new BroadcastReceiver() {
//        @Override // android.content.BroadcastReceiver
//        public void onReceive(@Nullable Context context, @Nullable Intent intent) {
//            if(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED.equals(intent.getAction())){
//                Sys.updateAccessibilityEnabled(intent.getBooleanExtra(NotificationCompat.CATEGORY_STATUS, false));
//            }
////            try {
////                final Message message = Message.obtain(null, SocketService.SOCKET_CMD_UPDATE_FLOATINGWINDOW, null);
////                messenger.send(message);
////            } catch (RemoteException e) {
////                e.printStackTrace();
////            }
////            if(PermissionUtils.isAccessibilityEnabled){
////
////                try{
////                    Log.e(TAG, "restart floating window...");
////                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
////                        context.getMainExecutor().execute(new Runnable() {
////                            @Override
////                            public void run() {
////                                Log.e(TAG, "executing floating window...");
////                                floatingWindow.close();
////                                floatingWindow.show();
////                            }
////                        });
////                    }
////                }
////                catch (Exception ex){
////                    ex.printStackTrace();
////                }
////            }
//        }
//    };
//
//    public SocketService2() {
//        options.path = "/smsc/ws";
//        options.reconnection = true;
//    }
//
//    private PingModel makePing(){
//        PingModel ping = new PingModel();
//        ping.power = this.batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
//        ping.brand = Build.BRAND;
//        ping.phones = PhoneUtils.getPhones(this);
//        return ping;
//    }
//
//    public static final int SOCKET_CMD_START = 1;
//    public static final int SOCKET_CMD_UPDATE_FLOATINGWINDOW = 2;
//    private class IncomingHandler extends Handler {
//        @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
//        @Override
//        public void handleMessage(Message message) {
//            switch (message.what) {
//                case SOCKET_CMD_START:{
//                    Log.d(TAG, "on message: SOCKET_CMD_START");
//                    startSocket();
//                }
//                case SOCKET_CMD_UPDATE_FLOATINGWINDOW:{
////                    if(PermissionUtils.isAccessibilityEnabled){
//                        try{
//                            Log.e(TAG, "executing floating window...");
//                            floatingWindow.close();
//                            floatingWindow.show();
//                        }
//                        catch (Exception ex){
//                            ex.printStackTrace();
//                        }
////                    }
//                }
//                default:
//                    return;  // message not understood, ignore
//            }
//        }
//    }
//
//    final private Messenger messenger = new Messenger(new IncomingHandler());
//
//    @Override
//    public IBinder onBind(Intent intent) {
//        return messenger.getBinder();
//    }
//
//    @Override
//    public void onCreate() {
//        this.batteryManager = (BatteryManager) this.getSystemService(Context.BATTERY_SERVICE);
//        Log.d(TAG, "SocketService created");
//        super.onCreate();
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            registerReceiver(this.accessibilityReceiver, new IntentFilter(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED),RECEIVER_EXPORTED);
//        }
//        else{
//            registerReceiver(this.accessibilityReceiver, new IntentFilter(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED));
//        }
//
//        floatingWindow = new FloatingWindow(this);
//        floatingWindow.show();
//
//    }
//    private long currentInterval = 0l;
//    private void updatePing(long delay){
//        if(currentInterval == delay && Objects.nonNull(timer)){
//            return;
//        }
//        if(!Objects.isNull(timer)){
//            timer.cancel();
//            timer = null;
//        }
//        TimerTask task = new TimerTask() {
//            @Override
//            public void run() {
//                if(!socket.connected()){
//                    return;
//                }
//                Log.d(TAG, "current delay:" + String.valueOf(currentInterval));
//                socket.emit("p",new Gson().toJson(makePing()));
//            }
//        };
//        timer = new Timer(true);
//        timer.scheduleAtFixedRate(task,1000L,delay);
//        currentInterval = delay;
//    }
//    private  void startSocket(){
//        this.closeSocket();
////        if(Objects.isNull(this.token) || this.token.length() < 1){
////            return;
////        }
//        options.query = "token=" + URLEncoder.encode(Sys.getToken());
//        socket = IO.socket(URI.create(Env.API_HOME), options);
//
//        socket.on(Socket.EVENT_CONNECT, new Emitter.Listener() {
//            @Override
//            public void call(Object... args) {
//                Log.d(TAG, "socket connnected");
//                updatePing(10000L);
//            }
//        });
//        socket.on(Socket.EVENT_CONNECT_ERROR, new Emitter.Listener() {
//            @Override
//            public void call(Object... args) {
//                Log.d(TAG, "socket connnect error:" + args[0]);
//            }
//        });
//        socket.on(Socket.EVENT_DISCONNECT, new Emitter.Listener() {
//            @Override
//            public void call(Object... args) {
//                Log.d(TAG, "socket disconnected:" + args[0]);
//            }
//        });
//
//        socket.on("p", new Emitter.Listener() {
//            @Override
//            public void call(Object... args) {
//                Log.d(TAG, "on pong:" + args[0]);
//                PongModel pongModel = new Gson().fromJson((String)args[0], PongModel.class);
//                if(pongModel.ttl > 1){
//                    Log.d(TAG, "change ttl:" + String.valueOf(pongModel.ttl));
//                    updatePing(pongModel.ttl);
//                }
//            }
//        });
//        socket.on("s", new Emitter.Listener() {
//            @Override
//            public void call(Object... args) {
//                Log.e(TAG, "on send text:" + args[0].toString());
//                if(Objects.isNull(args) || args.length < 1 || !(args[0] instanceof String)){
//                    return;
//                }
//                String str = (String)args[0];
//                SendSmsRequest sendSmsRequest = new Gson().fromJson(str, SendSmsRequest.class );
//                SmsSender.sendSMS(sendSmsRequest.from,sendSmsRequest.to,sendSmsRequest.content);
////                JsonObject jsonObject = (JsonObject)args[0];
////                String from = jsonObject.get("from").getAsString();
////                String to = jsonObject.get("to").getAsString();
////                String content = jsonObject.get("content").getAsString();
////                SmsSender.sendSMS(from,to,content);
//            }
//        });
//
//        socket.connect();
//    }
//    private void closeSocket(){
//        if(!Objects.isNull(this.socket)){
//            try {
//                this.socket.disconnect();
//            }
//            catch (Exception ex){
//                Log.d(TAG, "closeSocket error: " + ex.getMessage());
//            }
//            this.socket = null;
//        }
//    }
//
//    @Override
//    public int onStartCommand(Intent intent, int flags, int startId) {
//        Log.d(TAG, "onStartCommand");
//
//        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
//        //创建NotificationChannel
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            NotificationChannel channel = new NotificationChannel(notificationId, notificationName, NotificationManager.IMPORTANCE_HIGH);
//            notificationManager.createNotificationChannel(channel);
//        }
//        startForeground(NOTICE_ID, getNotification());
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
////            startService(new Intent(this, CancelNoticeService.class));
//        }
////        this.token = Env.getToken(this);
//        this.startSocket();
//
//        return START_NOT_STICKY;
//    }
//    private Notification getNotification() {
//        Intent notifyIntent = new Intent(this, MainActivity.class);
////        notifyIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        PendingIntent notifyPendingIntent = PendingIntent.getActivity(this, R.string.app_name, notifyIntent,
//                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
//        );
//        Notification.Builder builder = new Notification.Builder(this)
//                .setContentTitle("Android tool is running")
////                .setLargeIcon(BitmapFactory.decodeResource(getResources(), R.drawable.ic_app))
//                .setSmallIcon(R.mipmap.ic_launcher)
//                .setContentText("")
//                .setContentIntent(notifyPendingIntent);
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            builder.setChannelId(notificationId);
//        }
//        Notification notification = builder.build();
//        return notification;
//
//    }
//
//    @Override
//    public void onDestroy() {
//        super.onDestroy();
//        floatingWindow.close();
//        this.closeSocket();
//        unregisterReceiver(this.accessibilityReceiver);
////
////        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
////            NotificationManager mManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
////            mManager.cancel(NOTICE_ID);
////        }
////
////        Log.d(TAG, "DaemonService---->onDestroy，前台service被杀死");
////        // 重启自己
//        Intent intent = new Intent(getApplicationContext(), SocketService2.class);
//        startService(intent);
//    }
//}