package com.bm.atool.ui;

import androidx.annotation.DrawableRes;
import androidx.fragment.app.Fragment;

public abstract class BaseFragment extends Fragment {

    private CharSequence title;

    public BaseFragment(){
        super();
    }
    public CharSequence getTitle() {
        return title;
    }

    public void setTitle(CharSequence title) {
        this.title = title;
    }

    @DrawableRes
    public abstract int getIconResourceId();
}
