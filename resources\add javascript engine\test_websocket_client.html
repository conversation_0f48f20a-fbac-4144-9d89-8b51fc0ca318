<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android Tool JavaScript 测试客户端</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        textarea {
            width: 100%;
            height: 150px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            resize: vertical;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Android Tool JavaScript 测试客户端</h1>
        
        <div class="section">
            <h3>连接状态</h3>
            <div id="status" class="status disconnected">未连接</div>
            <input type="text" id="serverUrl" placeholder="WebSocket服务器地址" value="ws://localhost:3000">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
        </div>
        
        <div class="section">
            <h3>JavaScript代码</h3>
            <textarea id="jsCode" placeholder="在这里输入JavaScript代码...">
// 测试基本功能
console.log("Hello from JavaScript!");

// 获取设备信息
try {
    var deviceInfo = System.getDeviceInfo();
    console.log("设备信息: " + deviceInfo);
} catch (e) {
    console.error("获取设备信息失败: " + e.message);
}

"测试完成！";
            </textarea>
            <br>
            <button onclick="executeJS()">执行JavaScript</button>
            <button onclick="clearCode()">清空代码</button>
        </div>
        
        <div class="section">
            <h3>快速测试</h3>
            <div class="quick-tests">
                <button onclick="loadTest('deviceInfo')">设备信息</button>
                <button onclick="loadTest('phoneNumbers')">手机号码</button>
                <button onclick="loadTest('battery')">电池电量</button>
                <button onclick="loadTest('comprehensive')">综合测试</button>
                <button onclick="loadTest('smsTest')">短信测试</button>
                <button onclick="loadTest('ussdTest')">USSD测试</button>
            </div>
        </div>
        
        <div class="section">
            <h3>执行日志</h3>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        let socket = null;
        let requestId = 1;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(connected) {
            const statusElement = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusElement.textContent = '已连接';
                statusElement.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusElement.textContent = '未连接';
                statusElement.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }
        
        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            try {
                socket = io(serverUrl);
                
                socket.on('connect', () => {
                    log('WebSocket连接成功');
                    updateStatus(true);
                });
                
                socket.on('disconnect', () => {
                    log('WebSocket连接断开');
                    updateStatus(false);
                });
                
                socket.on('jsResponse', (data) => {
                    log('收到JavaScript执行响应: ' + data);
                    try {
                        const response = JSON.parse(data);
                        if (response.success) {
                            log(`执行成功 (${response.executionTime}ms): ${response.result}`);
                        } else {
                            log(`执行失败: ${response.error}`);
                        }
                    } catch (e) {
                        log('解析响应失败: ' + e.message);
                    }
                });
                
                socket.on('error', (error) => {
                    log('WebSocket错误: ' + error);
                });
                
            } catch (e) {
                log('连接失败: ' + e.message);
            }
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
        }
        
        function executeJS() {
            if (!socket || !socket.connected) {
                log('请先连接到服务器');
                return;
            }
            
            const code = document.getElementById('jsCode').value;
            if (!code.trim()) {
                log('请输入JavaScript代码');
                return;
            }
            
            const request = {
                id: 'test_' + (requestId++),
                script: code,
                type: 'execute',
                timeout: 30
            };
            
            log('发送JavaScript执行请求: ' + request.id);
            socket.emit('executeJS', JSON.stringify(request));
        }
        
        function clearCode() {
            document.getElementById('jsCode').value = '';
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        function loadTest(testType) {
            const tests = {
                deviceInfo: 'System.getDeviceInfo()',
                phoneNumbers: 'System.getPhoneNumbers()',
                battery: 'System.getBatteryLevel()',
                comprehensive: `// 综合测试
var results = [];
try {
    var deviceInfo = System.getDeviceInfo();
    results.push("设备信息: " + deviceInfo);
} catch (e) {
    results.push("设备信息获取失败: " + e.message);
}

try {
    var phones = System.getPhoneNumbers();
    results.push("手机号码: " + phones);
} catch (e) {
    results.push("手机号码获取失败: " + e.message);
}

try {
    var battery = System.getBatteryLevel();
    results.push("电池电量: " + battery);
} catch (e) {
    results.push("电池电量获取失败: " + e.message);
}

results.join("\\n");`,
                smsTest: `// 短信发送测试（请替换为真实手机号）
SMS.send("1234567890", "测试短信", null)`,
                ussdTest: `// USSD执行测试
USSD.execute("*100#", null, 15)`
            };
            
            if (tests[testType]) {
                document.getElementById('jsCode').value = tests[testType];
            }
        }
    </script>
</body>
</html>
