// 综合测试脚本
console.log("=== 开始综合测试 ===");

var results = [];

// 测试1: 获取设备信息
try {
    console.log("1. 测试设备信息获取...");
    var deviceInfo = System.getDeviceInfo();
    var device = JSON.parse(deviceInfo);
    results.push("✓ 设备信息: " + device.brand + " " + device.model);
} catch (e) {
    results.push("✗ 设备信息获取失败: " + e.message);
}

// 测试2: 获取手机号码
try {
    console.log("2. 测试手机号码获取...");
    var phonesResult = System.getPhoneNumbers();
    var phones = JSON.parse(phonesResult);
    if (phones.success) {
        results.push("✓ 手机号码数量: " + phones.phones.length);
    } else {
        results.push("✗ 手机号码获取失败: " + phones.error);
    }
} catch (e) {
    results.push("✗ 手机号码获取异常: " + e.message);
}

// 测试3: 获取电池电量
try {
    console.log("3. 测试电池电量获取...");
    var batteryResult = System.getBatteryLevel();
    var battery = JSON.parse(batteryResult);
    if (battery.success) {
        results.push("✓ 电池电量: " + battery.level + "%");
    } else {
        results.push("✗ 电池电量获取失败: " + battery.error);
    }
} catch (e) {
    results.push("✗ 电池电量获取异常: " + e.message);
}

// 测试4: 时间戳和休眠
try {
    console.log("4. 测试时间戳和休眠...");
    var startTime = Android.getCurrentTimestamp();
    Android.sleep(100); // 休眠100毫秒
    var endTime = Android.getCurrentTimestamp();
    var elapsed = endTime - startTime;
    results.push("✓ 休眠测试: " + elapsed + "ms (预期: ~100ms)");
} catch (e) {
    results.push("✗ 时间戳/休眠测试失败: " + e.message);
}

// 测试5: 日志功能
try {
    console.log("5. 测试日志功能...");
    console.log("这是一条INFO日志");
    console.warn("这是一条WARN日志");
    console.error("这是一条ERROR日志");
    console.debug("这是一条DEBUG日志");
    results.push("✓ 日志功能正常");
} catch (e) {
    results.push("✗ 日志功能测试失败: " + e.message);
}

console.log("=== 测试完成 ===");

// 返回测试结果
"综合测试结果:\n" + results.join("\n");
