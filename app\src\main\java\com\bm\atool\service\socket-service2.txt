package com.bm.atool.service;

import static android.app.Service.START_STICKY;

import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.net.VpnService;
import android.os.IBinder;
import android.os.ParcelFileDescriptor;
import android.util.Log;

import com.bm.atool.MainActivity;

import java.io.IOException;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.channels.DatagramChannel;

import android.os.Build;

import androidx.annotation.Nullable;

public class SocketService extends Service {

    public static final int SOCKET_CMD_START = 1;
    public static final int SOCKET_CMD_UPDATE_FLOATINGWINDOW = 2;
    private static final String TAG = "MyVpnService";
    private Thread mThread = null;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Log.e(TAG,"onbind: "+intent.getAction());
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.e(TAG, "Service started");

        if (mThread != null) {
            mThread.interrupt();
            mThread = null;
        }
        mThread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
//                    mInterface = establishVpn();
                    Integer i = 0;
                    while (!Thread.currentThread().isInterrupted()) {
                        i++;
//                        if(i>60){
//                            stopSelf();
//                            return;
//                        }
                        Log.e(TAG, Thread.currentThread().getName()+":" + String.valueOf(Thread.currentThread().getId())+",i =" + String.valueOf(i));
                        Thread.sleep(1000L);
                    }
                }  catch (InterruptedException e) {
                    e.printStackTrace();
                } finally {
//                    stopSelf();
                    Log.d(TAG, "Service stopped");
                }
            }
        }, "MyVpnThread");

        mThread.start();
        return START_STICKY;
    }

//    private ParcelFileDescriptor establishVpn() throws IOException {
//        Builder builder = new Builder();
//        builder.setMtu(1500);
//        builder.addAddress("********", 24);
//        builder.addRoute("0.0.0.0", 0);
//
//        Intent intent = new Intent(this, MainActivity.class);
//        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_IMMUTABLE);
//        builder.setConfigureIntent(pendingIntent);
//
//        return builder.establish();
//    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "Service destroyed");
        if (mThread != null) {
            mThread.interrupt();
            mThread = null;
        }
        super.onDestroy();
//        System.exit(0);
    }
}