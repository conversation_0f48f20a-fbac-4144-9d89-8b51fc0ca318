<dependencies>
  <compile
      roots="androidx.databinding:viewbinding:8.2.0@aar,androidx.navigation:navigation-common:2.7.7@aar,androidx.navigation:navigation-runtime:2.7.7@aar,androidx.navigation:navigation-fragment:2.7.7@aar,androidx.navigation:navigation-ui:2.7.7@aar,com.google.android.material:material:1.5.0@aar,androidx.preference:preference:1.2.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.squareup.retrofit2:converter-gson:2.11.0@jar,com.google.code.gson:gson:2.11.0@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.googlecode.libphonenumber:libphonenumber:8.13.25@jar,io.socket:socket.io-client:2.1.1@jar,org.javassist:javassist:3.28.0-GA@jar,io.reactivex.rxjava2:rxjava:2.2.21@jar,org.microg:safe-parcel:1.7.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.activity:activity-ktx:1.7.2@aar,androidx.activity:activity:1.7.2@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.transition:transition:1.4.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.core:core-ktx:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,com.tananaev:adblib:1.3@jar,commons-codec:commons-codec:1.3@jar,com.github.tiann:FreeReflection:3.2.2@aar,com.github.megatronking.stringfog:xor:5.0.0@jar,app.cash.quickjs:quickjs-android:0.9.2@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.cardview:cardview:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.collection:collection:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.6.0@jar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar,org.jetbrains:annotations:13.0@jar,com.google.errorprone:error_prone_annotations:2.27.0@jar,io.socket:engine.io-client:2.1.0@jar,com.squareup.okhttp3:okhttp:3.14.9@jar,com.squareup.okio:okio:1.17.2@jar,org.reactivestreams:reactive-streams:1.0.3@jar,com.github.megatronking.stringfog:interface:5.0.0@jar">
    <dependency
        name="androidx.databinding:viewbinding:8.2.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.7.7@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.7.7@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.5.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.preference:preference:1.2.0@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.google.code.gson:gson:2.11.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.googlecode.libphonenumber:libphonenumber:8.13.25@jar"
        simpleName="com.googlecode.libphonenumber:libphonenumber"/>
    <dependency
        name="io.socket:socket.io-client:2.1.1@jar"
        simpleName="io.socket:socket.io-client"/>
    <dependency
        name="org.javassist:javassist:3.28.0-GA@jar"
        simpleName="org.javassist:javassist"/>
    <dependency
        name="io.reactivex.rxjava2:rxjava:2.2.21@jar"
        simpleName="io.reactivex.rxjava2:rxjava"/>
    <dependency
        name="org.microg:safe-parcel:1.7.0@aar"
        simpleName="org.microg:safe-parcel"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.7.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.7.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.core:core:1.13.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="com.tananaev:adblib:1.3@jar"
        simpleName="com.tananaev:adblib"/>
    <dependency
        name="commons-codec:commons-codec:1.3@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.github.tiann:FreeReflection:3.2.2@aar"
        simpleName="com.github.tiann:FreeReflection"/>
    <dependency
        name="com.github.megatronking.stringfog:xor:5.0.0@jar"
        simpleName="com.github.megatronking.stringfog:xor"/>
    <dependency
        name="app.cash.quickjs:quickjs-android:0.9.2@aar"
        simpleName="app.cash.quickjs:quickjs-android"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.27.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="io.socket:engine.io-client:2.1.0@jar"
        simpleName="io.socket:engine.io-client"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.14.9@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio:1.17.2@jar"
        simpleName="com.squareup.okio:okio"/>
    <dependency
        name="org.reactivestreams:reactive-streams:1.0.3@jar"
        simpleName="org.reactivestreams:reactive-streams"/>
    <dependency
        name="com.github.megatronking.stringfog:interface:5.0.0@jar"
        simpleName="com.github.megatronking.stringfog:interface"/>
  </compile>
  <package
      roots="androidx.databinding:viewbinding:8.2.0@aar,androidx.navigation:navigation-common:2.7.7@aar,androidx.navigation:navigation-runtime:2.7.7@aar,androidx.navigation:navigation-fragment:2.7.7@aar,androidx.navigation:navigation-ui:2.7.7@aar,com.google.android.material:material:1.5.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,androidx.preference:preference:1.2.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.squareup.retrofit2:converter-gson:2.11.0@jar,com.google.code.gson:gson:2.11.0@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.googlecode.libphonenumber:libphonenumber:8.13.25@jar,io.socket:socket.io-client:2.1.1@jar,org.javassist:javassist:3.28.0-GA@jar,io.reactivex.rxjava2:rxjava:2.2.21@jar,org.microg:safe-parcel:1.7.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment:1.6.2@aar,androidx.fragment:fragment-ktx:1.6.2@aar,androidx.activity:activity:1.7.2@aar,androidx.activity:activity-ktx:1.7.2@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.core:core-ktx:1.13.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.transition:transition:1.4.1@aar,androidx.window:window:1.0.0@aar,androidx.core:core:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,com.tananaev:adblib:1.3@jar,commons-codec:commons-codec:1.3@jar,com.github.tiann:FreeReflection:3.2.2@aar,com.github.megatronking.stringfog:xor:5.0.0@jar,app.cash.quickjs:quickjs-android:0.9.2@aar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.collection:collection-ktx:1.1.0@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.6.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,com.google.errorprone:error_prone_annotations:2.27.0@jar,io.socket:engine.io-client:2.1.0@jar,com.squareup.okhttp3:okhttp:3.14.9@jar,org.reactivestreams:reactive-streams:1.0.3@jar,com.github.megatronking.stringfog:interface:5.0.0@jar,com.google.guava:listenablefuture:1.0@jar,org.jetbrains:annotations:13.0@jar,com.squareup.okio:okio:1.17.2@jar">
    <dependency
        name="androidx.databinding:viewbinding:8.2.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.navigation:navigation-common:2.7.7@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.7.7@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-fragment:2.7.7@aar"
        simpleName="androidx.navigation:navigation-fragment"/>
    <dependency
        name="androidx.navigation:navigation-ui:2.7.7@aar"
        simpleName="androidx.navigation:navigation-ui"/>
    <dependency
        name="com.google.android.material:material:1.5.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.preference:preference:1.2.0@aar"
        simpleName="androidx.preference:preference"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.google.code.gson:gson:2.11.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.googlecode.libphonenumber:libphonenumber:8.13.25@jar"
        simpleName="com.googlecode.libphonenumber:libphonenumber"/>
    <dependency
        name="io.socket:socket.io-client:2.1.1@jar"
        simpleName="io.socket:socket.io-client"/>
    <dependency
        name="org.javassist:javassist:3.28.0-GA@jar"
        simpleName="org.javassist:javassist"/>
    <dependency
        name="io.reactivex.rxjava2:rxjava:2.2.21@jar"
        simpleName="io.reactivex.rxjava2:rxjava"/>
    <dependency
        name="org.microg:safe-parcel:1.7.0@aar"
        simpleName="org.microg:safe-parcel"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.6.2@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.2@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity:1.7.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.7.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.4.1@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.window:window:1.0.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.core:core:1.13.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="com.tananaev:adblib:1.3@jar"
        simpleName="com.tananaev:adblib"/>
    <dependency
        name="commons-codec:commons-codec:1.3@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.github.tiann:FreeReflection:3.2.2@aar"
        simpleName="com.github.tiann:FreeReflection"/>
    <dependency
        name="com.github.megatronking.stringfog:xor:5.0.0@jar"
        simpleName="com.github.megatronking.stringfog:xor"/>
    <dependency
        name="app.cash.quickjs:quickjs-android:0.9.2@aar"
        simpleName="app.cash.quickjs:quickjs-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.6.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.27.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="io.socket:engine.io-client:2.1.0@jar"
        simpleName="io.socket:engine.io-client"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:3.14.9@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="org.reactivestreams:reactive-streams:1.0.3@jar"
        simpleName="org.reactivestreams:reactive-streams"/>
    <dependency
        name="com.github.megatronking.stringfog:interface:5.0.0@jar"
        simpleName="com.github.megatronking.stringfog:interface"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.squareup.okio:okio:1.17.2@jar"
        simpleName="com.squareup.okio:okio"/>
  </package>
</dependencies>
