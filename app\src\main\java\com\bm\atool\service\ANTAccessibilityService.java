package com.bm.atool.service;

import android.accessibilityservice.AccessibilityService;
import android.content.Intent;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;

import com.bm.atool.Sys;

public class ANTAccessibilityService extends AccessibilityService implements LifecycleOwner {
    private LifecycleRegistry mLifecycleRegistry = new LifecycleRegistry(this);

    private final static String TAG = ANTAccessibilityService.class.getSimpleName();

    @Override
    public void onCreate() {
        super.onCreate();

        Log.e(TAG, "ANTAccessibilityService created: "  + String.valueOf(this.hashCode()));
        try{
            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE);
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
        sendStatusBroadcast(true);
//        SocketServiceConnection.getInstance(this.getApplicationContext()).EnsureSocketService();
    }

    private final void sendStatusBroadcast(boolean running) {
        Intent intent = new Intent(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED);
        intent.putExtra(NotificationCompat.CATEGORY_STATUS, running);
        sendBroadcast(intent);
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
//        Log.d(TAG,"AccessibilityEvent:"  + event.toString());
    }

    @Override
    public void onInterrupt() {
        Log.d(TAG,"onInterrupt" );
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return this.mLifecycleRegistry;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "ANTAccessibilityService onDestroy: "  + String.valueOf(this.hashCode()));
        sendStatusBroadcast(false);
    }
}
