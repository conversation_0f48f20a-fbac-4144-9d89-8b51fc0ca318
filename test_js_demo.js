// JavaScript功能演示脚本

// 1. 基础测试 - 获取设备信息
console.log("=== 开始JavaScript功能演示 ===");

// 测试1：获取设备信息
console.log("测试1：获取设备信息");
try {
    var deviceInfo = System.getDeviceInfo();
    console.log("设备信息:", deviceInfo);
} catch (e) {
    console.log("获取设备信息失败:", e.message);
}

// 测试2：获取手机号码
console.log("\n测试2：获取手机号码");
try {
    var phoneNumbers = System.getPhoneNumbers();
    console.log("手机号码:", phoneNumbers);
} catch (e) {
    console.log("获取手机号码失败:", e.message);
}

// 测试3：获取电池电量
console.log("\n测试3：获取电池电量");
try {
    var battery = System.getBatteryLevel();
    console.log("电池电量:", battery);
} catch (e) {
    console.log("获取电池电量失败:", e.message);
}

// 测试4：时间戳和延时
console.log("\n测试4：时间戳和延时");
try {
    var timestamp1 = Android.getCurrentTimestamp();
    console.log("当前时间戳:", timestamp1);
    
    console.log("等待2秒...");
    Android.sleep(2000);
    
    var timestamp2 = Android.getCurrentTimestamp();
    console.log("2秒后时间戳:", timestamp2);
    console.log("时间差:", timestamp2 - timestamp1, "毫秒");
} catch (e) {
    console.log("时间戳测试失败:", e.message);
}

// 测试5：JSON处理
console.log("\n测试5：JSON处理");
try {
    var testData = {
        name: "JavaScript测试",
        version: "1.0",
        timestamp: Android.getCurrentTimestamp(),
        features: ["SMS", "USSD", "DeviceInfo"]
    };
    
    var jsonStr = JSON.stringify(testData);
    console.log("JSON字符串:", jsonStr);
    
    var parsed = JSON.parse(jsonStr);
    console.log("解析后的数据:", parsed.name, "版本:", parsed.version);
} catch (e) {
    console.log("JSON处理失败:", e.message);
}

console.log("\n=== JavaScript功能演示完成 ===");

// 返回测试结果
"JavaScript引擎测试完成！所有基础功能正常工作。";
