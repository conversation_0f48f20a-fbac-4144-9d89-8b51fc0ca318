package com.bm.atool.js;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * JavaScript引擎测试类
 * 测试JavaScript引擎的基本功能（不依赖Android环境）
 */
public class JavaScriptEngineTest {

    @Test
    public void testAndroidBridgeInterfaceExists() {
        // 测试AndroidBridge类是否存在
        try {
            Class<?> bridgeClass = AndroidBridge.class;
            assertNotNull("AndroidBridge类应该存在", bridgeClass);

            // 检查类方法
            assertNotNull("log方法应该存在", bridgeClass.getMethod("log", String.class));
            assertNotNull("getCurrentTimestamp方法应该存在", bridgeClass.getMethod("getCurrentTimestamp"));
            assertNotNull("getDeviceInfo方法应该存在", bridgeClass.getMethod("getDeviceInfo"));

        } catch (Exception e) {
            fail("AndroidBridgeInterface接口测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testAndroidBridgeImplementation() {
        // 测试AndroidBridge是否可以正常创建
        try {
            AndroidBridge bridge = new AndroidBridge(null, null);
            assertNotNull("AndroidBridge应该可以创建", bridge);

            // 测试接口方法是否可调用
            String timestamp = bridge.getCurrentTimestamp();
            assertNotNull("时间戳不应该为空", timestamp);
            assertTrue("时间戳应该是数字", timestamp.matches("\\d+"));
            assertTrue("时间戳应该大于0", Long.parseLong(timestamp) > 0);

        } catch (Exception e) {
            fail("AndroidBridge实现测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testAndroidBridgeMethodSignatures() {
        // 测试AndroidBridge方法签名
        try {
            Class<?> bridgeClass = AndroidBridge.class;

            // 检查关键方法的返回类型
            assertEquals("log方法返回类型应该是void",
                        void.class, bridgeClass.getMethod("log", String.class).getReturnType());
            assertEquals("getCurrentTimestamp方法返回类型应该是String",
                        String.class, bridgeClass.getMethod("getCurrentTimestamp").getReturnType());
            assertEquals("getDeviceInfo方法返回类型应该是String",
                        String.class, bridgeClass.getMethod("getDeviceInfo").getReturnType());

        } catch (Exception e) {
            fail("接口方法签名测试失败: " + e.getMessage());
        }
    }
}
