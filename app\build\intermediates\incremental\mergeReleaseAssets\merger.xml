<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets"><file name="example_scripts/quick_test.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\example_scripts\quick_test.js"/><file name="example_scripts/sms_example.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\example_scripts\sms_example.js"/><file name="example_scripts/system_example.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\example_scripts\system_example.js"/><file name="example_scripts/ussd_example.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\example_scripts\ussd_example.js"/><file name="js_examples/comprehensive_test.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\js_examples\comprehensive_test.js"/><file name="js_examples/test_device_info.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\js_examples\test_device_info.js"/><file name="js_examples/test_sms.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\js_examples\test_sms.js"/><file name="js_examples/test_ussd.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\assets\js_examples\test_ussd.js"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\release\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\build\intermediates\shader_assets\release\out"/></dataSet></merger>