#!/bin/bash

# JavaScript引擎Stack Overflow修复验证脚本

echo "=========================================="
echo "JavaScript引擎Stack Overflow修复验证"
echo "=========================================="

# 检查设备连接
echo "1. 检查Android设备连接..."
adb devices | grep -q "device$"
if [ $? -ne 0 ]; then
    echo "❌ 没有找到连接的Android设备"
    echo "请确保："
    echo "- 设备已连接并开启USB调试"
    echo "- 运行 'adb devices' 确认设备状态"
    exit 1
fi
echo "✅ Android设备已连接"

# 构建应用
echo "2. 构建应用..."
./gradlew assembleDebug --quiet
if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi
echo "✅ 构建成功"

# 安装应用
echo "3. 安装应用..."
adb install -r app/build/outputs/apk/debug/app-debug.apk > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 安装失败"
    exit 1
fi
echo "✅ 安装成功"

# 清除日志
echo "4. 清除旧日志..."
adb logcat -c

# 启动应用并触发JavaScript引擎初始化
echo "5. 启动应用并测试JavaScript引擎..."
adb shell am start -n com.bm.atool/.MainActivity > /dev/null 2>&1
sleep 2

# 启动Debug页面来触发JavaScript引擎初始化
adb shell am start -n com.bm.atool/.ui.DebugActivity > /dev/null 2>&1
sleep 3

# 检查日志中是否有stack overflow错误
echo "6. 检查Stack Overflow错误..."
STACK_OVERFLOW_COUNT=$(adb logcat -d | grep -c "stack overflow")
if [ $STACK_OVERFLOW_COUNT -gt 0 ]; then
    echo "❌ 发现 $STACK_OVERFLOW_COUNT 个stack overflow错误"
    echo "相关日志："
    adb logcat -d | grep -A 5 -B 5 "stack overflow"
    exit 1
else
    echo "✅ 没有发现stack overflow错误"
fi

# 检查JavaScript引擎初始化状态
echo "7. 检查JavaScript引擎初始化状态..."
INIT_SUCCESS=$(adb logcat -d | grep -c "JavaScript引擎初始化成功")
if [ $INIT_SUCCESS -gt 0 ]; then
    echo "✅ JavaScript引擎初始化成功"
else
    echo "⚠️  未找到JavaScript引擎初始化成功的日志"
fi

# 检查基本测试结果
echo "8. 检查基本功能测试..."
BASIC_TEST=$(adb logcat -d | grep "QuickJS基本测试结果")
if [ -n "$BASIC_TEST" ]; then
    echo "✅ 基本功能测试: $BASIC_TEST"
else
    echo "⚠️  未找到基本功能测试结果"
fi

# 显示相关日志
echo "9. 显示JavaScript引擎相关日志..."
echo "----------------------------------------"
adb logcat -d | grep -E "(JavaScriptEngine|QuickJS|AndroidBridge)" | tail -15
echo "----------------------------------------"

echo ""
echo "=========================================="
echo "验证完成！"
echo "=========================================="

# 总结结果
if [ $STACK_OVERFLOW_COUNT -eq 0 ] && [ $INIT_SUCCESS -gt 0 ]; then
    echo "🎉 修复成功！"
    echo "- ✅ 没有stack overflow错误"
    echo "- ✅ JavaScript引擎初始化成功"
    echo "- ✅ 基本功能正常"
else
    echo "⚠️  修复可能不完整，请检查上面的日志"
fi

echo ""
echo "下一步测试建议："
echo "1. 在应用的Debug页面手动执行JavaScript代码"
echo "2. 测试复杂的JavaScript脚本"
echo "3. 测试Android API调用功能"
