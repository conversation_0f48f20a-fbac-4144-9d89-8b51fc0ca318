package com.bm.atool.ui.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bm.atool.R;
import com.bm.atool.events.SMSEvent;

import java.util.ArrayList;
import java.util.Objects;

public class SmsAdapter extends RecyclerView.Adapter<SmsAdapter.ViewHolder>{

    private ArrayList<SMSEvent> dataSet;
    // View lookup cache
    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView txtTime;
        TextView txtFrom;
        TextView txtStatus;
        TextView txtContent;

        public ViewHolder(View view) {
            super(view);
            txtTime = (TextView) view.findViewById(R.id.txtTime);
            txtFrom = (TextView) view.findViewById(R.id.txtFrom);
            txtStatus = (TextView) view.findViewById(R.id.txtStatus);
            txtContent= (TextView) view.findViewById(R.id.txtContent);
        }
    }

    public SmsAdapter(ArrayList<SMSEvent> dataSet) {
        this.dataSet = dataSet;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.sms_row, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SMSEvent sms = this.dataSet.get(position);
//        holder.txtTime.setText(this.dataSet.get(position).);
        holder.txtFrom.setText(sms.from);
        holder.txtStatus.setText(getStatus(sms.updateStatus));
        holder.txtContent.setText(sms.content);
    }

    private CharSequence getStatus(int updateStatus) {
        switch (updateStatus){
            case SMSEvent.UPLOAD_STATUS_FAILED:
                return "FAILED";
            case SMSEvent.UPLOAD_STATUS_UPLOADED:
                return "";
            default:
                return "Pending";
        }
    }
    @Override
    public int getItemCount() {
        if(Objects.isNull(this.dataSet)){
            return 0;
        }
        return this.dataSet.size();
    }

}