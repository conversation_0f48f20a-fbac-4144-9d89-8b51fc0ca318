// 测试USSD功能
console.log("开始测试USSD执行...");

try {
    // 注意：这只是测试代码，实际使用时请替换为有效的USSD代码
    var result = USSD.execute("*100#", null, 15);
    console.log("USSD执行结果: " + result);
    
    var response = JSON.parse(result);
    if (response.success) {
        "USSD请求已提交，ID: " + response.id;
    } else {
        "USSD执行失败: " + response.error;
    }
} catch (e) {
    console.error("USSD测试失败: " + e.message);
    "错误: " + e.message;
}
