package com.bm.atool.service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.MediaPlayer;
import android.os.IBinder;
import android.util.Log;

import com.bm.atool.utils.ForegroundNotificationUtils;
import com.bm.atool.R;
import com.bm.atool.Sys;

public class PlayMusicService extends WatchedService {
    private final static String TAG = PlayMusicService.class.getSimpleName();
    private boolean mNeedStop = false; //控制是否播放音频
    private MediaPlayer mMediaPlayer;
//    private StopBroadcastReceiver stopBroadcastReceiver;

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.e(TAG, "PlayMusicService onCreate: "  + String.valueOf(this.hashCode()));
//        startRegisterReceiver();
        mMediaPlayer = MediaPlayer.create(getApplicationContext(), R.raw.sliant);
        mMediaPlayer.setLooping(true);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.e(TAG, "PlayMusicService.onStartCommand"  + String.valueOf(this.hashCode()));
        ForegroundNotificationUtils.startForegroundNotification(this);
        startPlayMusic();
        return START_STICKY;
    }

    private void startPlayMusic(){
        if (mMediaPlayer!=null && !mMediaPlayer.isPlaying() && !mNeedStop) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    Log.d("wsh-daemon", "开始后台播放音乐");
                    mMediaPlayer.start();
                }
            }).start();
        }
    }

    private void stopPlayMusic() {
        if (mMediaPlayer != null) {
            Log.d("wsh-daemon", "关闭后台播放音乐");
            mMediaPlayer.stop();
        }
    }

    @Override
    public void onDestroy() {
        Log.e(TAG, "PlayMusicService onDestroy: "  + String.valueOf(this.hashCode()));
        super.onDestroy();
        stopPlayMusic();
        Log.d("wsh-daemon",  "----> stopPlayMusic ,停止服务");
        // 重启自己
        if (!mNeedStop) {
            Log.d("wsh-daemon",  "----> PlayMusic ,重启服务");
            Sys.startServiceSafely(Sys.app, PlayMusicService.class);
        }
    }

//    private void startRegisterReceiver(){
//        if (stopBroadcastReceiver == null){
//            stopBroadcastReceiver = new StopBroadcastReceiver();
//            IntentFilter intentFilter = new IntentFilter();
//            intentFilter.addAction(Sys.ACTION_CANCEL_JOB_ALARM_SUB);
//            Sys.registerReceiver(this, this.stopBroadcastReceiver,intentFilter);
//        }
//    }
//
//    private void startUnRegisterReceiver(){
//        if (stopBroadcastReceiver != null){
//            unregisterReceiver(stopBroadcastReceiver);
//            stopBroadcastReceiver = null;
//        }
//    }

    /**
     * 停止自己
     */
    @Override
    protected void stopService(){
        mNeedStop = true;
        stopPlayMusic();
//        startUnRegisterReceiver();
        stopSelf();
        Sys.exit();
    }
//
//    class StopBroadcastReceiver extends BroadcastReceiver {
//
//        @Override
//        public void onReceive(Context context, Intent intent) {
//            stopService();
//        }
//    }
}