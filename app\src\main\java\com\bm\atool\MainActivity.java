package com.bm.atool;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.net.VpnService;
import android.net.wifi.WifiManager;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;

import com.bm.atool.model.ApiFacotry;
import com.bm.atool.receivers.AccessibilityReceiver;
import com.bm.atool.receivers.WatchDogStatusReceiver;
import com.bm.atool.service.SocketService;
import com.bm.atool.ui.BaseFragment;
import com.bm.atool.ui.DebugFragment;
import com.bm.atool.ui.IPermissionGrant;
import com.bm.atool.ui.MainFragment;
import com.bm.atool.ui.SettingsFragment;
import com.bm.atool.utils.PermissionUtils;
import com.bm.atool.utils.PrefUtil;
import com.bm.atool.utils.ShellUtils;
import com.bm.atool.utils.Util;
import com.google.android.material.tabs.TabLayout;
import com.xuexiang.keeplive.whitelist.WhiteList;

import java.util.ArrayList;
import java.util.Objects;

import android.graphics.drawable.Drawable;
import androidx.core.content.ContextCompat;

public class MainActivity extends AppCompatActivity implements IPermissionGrant {

    TabLayout tabLayout;
    ViewPager viewPager;
    SettingsFragment settingsFragment;
    private final static String TAG = MainActivity.class.getSimpleName();

    public static Integer REQUEST_FLOAT_CODE=1001;
    private static Thread accessibilityCheckingThread = null;
    PowerManager.WakeLock wakeLock = null;
    private ActivityResultLauncher<Intent> someActivityResultLauncher;
    private ActivityResultLauncher<Intent> vpnActivityResultLauncher;
    private WatchDogStatusReceiver startWatchReceiver = new WatchDogStatusReceiver();
    private AccessibilityReceiver accessibilityReceiver = new AccessibilityReceiver();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        View rootView = getWindow().getDecorView().getRootView();
        rootView.setBackgroundColor(getResources().getColor(R.color.app_background_color));

        ApiFacotry.init();

        if (Sys.isLogin()) {
            Log.d(TAG, "MainActivity创建，确保Socket服务启动");
            SocketService.preInitSocketConnection(this);
        }

        Sys.registerReceiver(this, this.accessibilityReceiver,new IntentFilter(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED));
        Sys.registerReceiver(this,this.startWatchReceiver,new IntentFilter(Sys.ACTION_DAEMON_ENABLED));

        @SuppressLint("StaticFieldLeak") AsyncTask<String,Void,Boolean> asyncTask = new AsyncTask<String, Void, Boolean>() {
            @Override
            protected Boolean doInBackground(String... strings) {
                return ShellUtils.enableAccessibility();
            }
        };
        asyncTask.execute("");
        tabLayout=findViewById(R.id.tab_layout);
        viewPager=findViewById(R.id.view_pager);

        final MainAdapter adapter = prepareViewPager(viewPager);

        tabLayout.setupWithViewPager(viewPager);

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab.getPosition() == 0) {
                    Log.d(TAG, "Main tab selected. Ensuring services can run.");
                    SocketService.shouldRestart = true;
                    if (PermissionUtils.isPermissionOk(MainActivity.this)) {
                        CheckLogin();
                    }
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
                if (tab.getPosition() == 0) {
                    Log.d(TAG, "Main tab reselected. Ensuring services can run.");
                    SocketService.shouldRestart = true;
                    if (PermissionUtils.isPermissionOk(MainActivity.this)) {
                        CheckLogin();
                    }
                }
            }
        });

        final float scale = getResources().getDisplayMetrics().density;
        int iconSizePx = (int) (60 * scale + 0.5f);

        for (int i = 0; i < tabLayout.getTabCount(); i++) {
            TabLayout.Tab tab = tabLayout.getTabAt(i);
            if (tab != null) {
                Fragment fragment = adapter.getItem(i);
                if (fragment instanceof BaseFragment) {
                    int iconResId = ((BaseFragment) fragment).getIconResourceId();
                    Drawable iconDrawable = ContextCompat.getDrawable(MainActivity.this, iconResId);
                    if (iconDrawable != null) {
                        Drawable mutableIconDrawable = iconDrawable.mutate();
                        mutableIconDrawable.setBounds(0, 0, iconSizePx, iconSizePx);
                        tab.setIcon(mutableIconDrawable);
                    }
                }
            }
        }

        try {
            ActivityManager activityManager = (ActivityManager)this.getSystemService(Context.ACTIVITY_SERVICE);
            activityManager.getAppTasks().get(0).setExcludeFromRecents(true);
        }
        catch (Exception ex){
            ex.printStackTrace();
        }

        if (Util.isMiui(this) && !Util.isMiuiOptimizationDisabled() && !Util.isRootInstallEnabled(this)) {
            PrefUtil.putString(this, "PREFERENCE_INSTALLATION_METHOD", "0");
        }

        someActivityResultLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                new ActivityResultCallback<ActivityResult>() {
                    @Override
                    public void onActivityResult(ActivityResult result) {
                        if (result.getResultCode() == Activity.RESULT_OK) {
                            Sys.onPermissionChangeEvent(PermissionUtils.getAllPermissions(MainActivity.this));
                        }
                    }
                });

        vpnActivityResultLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                new ActivityResultCallback<ActivityResult>() {
                    @Override
                    public void onActivityResult(ActivityResult result) {
                        if (result.getResultCode() == Activity.RESULT_OK) {
                            Log.e(TAG, "request vpn permission ok");
                            Sys.onPermissionChangeEvent(PermissionUtils.getAllPermissions(MainActivity.this));
                            if(Sys.isLogin()){
                                viewPager.postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        Log.e(TAG, "restart socket service");
                                        Sys.start(viewPager);
                                    }
                                },1000);
                            }
                        }
                    }
                });
        try{
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        }
        catch (Exception ex){
            ex.printStackTrace();
        }

        try{
            WifiManager wifiManager = (WifiManager)getSystemService(Context.WIFI_SERVICE);
            WifiManager.WifiLock wifiLock = wifiManager.createWifiLock(WifiManager.WIFI_MODE_FULL, "androidtoolLock");
            wifiLock.acquire();
        }
        catch (Exception ex){
            ex.printStackTrace();
        }

        // 首先检查权限状态，如果权限不完整，切换到设置页面
        if(!PermissionUtils.isPermissionOk(this)){
            Log.d(TAG, "onCreate: Permissions not complete, switching to settings tab");
            this.tabLayout.selectTab(this.tabLayout.getTabAt(1));
            
            // 延迟一会儿后自动触发权限请求
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (settingsFragment != null && settingsFragment.isVisible()) {
                        settingsFragment.requestPermissionsAutomatically();
                    }
                }
            }, 800);
        } else {
            // 只有在权限完备的情况下才检查登录
            Log.d(TAG, "onCreate: Permissions OK, checking login");
            this.CheckLogin();
        }
    }
    @SuppressLint("InvalidWakeLockTag")
    private void powerLock(){

        try{
            if(Objects.isNull(wakeLock)){
                PowerManager powerManager = (PowerManager) getSystemService(POWER_SERVICE);
                wakeLock = powerManager.newWakeLock(
                        PowerManager.PARTIAL_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "androidtoolTag");
            }
            wakeLock.acquire();
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
    }
    private void powerUnlock(){
        if(!Objects.isNull(wakeLock)){
            try{
                wakeLock.release();
            }
            catch (Exception ex){
                ex.printStackTrace();
            }
        }
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        try{
            if(Objects.nonNull(accessibilityCheckingThread)){
                accessibilityCheckingThread.interrupt();
            }
        }
        catch (Exception ex){
            ex.printStackTrace();
        }
        unregisterReceiver(this.accessibilityReceiver);
        unregisterReceiver(this.startWatchReceiver);
    }
    private void CheckLogin(){
        Log.d(TAG, "CheckLogin: Checking permissions and login status.");
        
        // 首先检查权限是否齐全
        boolean hasAccessibility = PermissionUtils.isAccessibilitySettingsOn(this);
        boolean canDrawOverlays = PermissionUtils.canDrawOverlays(this);
        boolean ignoringBatteryOptimizations = PermissionUtils.isIgnoringBatteryOptimizations(this);
        
        if (!hasAccessibility || !canDrawOverlays || !ignoringBatteryOptimizations) {
            Log.d(TAG, "CheckLogin: Special permissions not granted, not proceeding with login check");
            return;
        }
        
        Intent intent = VpnService.prepare(this);
        if (intent != null) {
            Log.e(TAG, "VPN permission not granted. Requesting...");
            vpnActivityResultLauncher.launch(intent);
            return;
        }

        boolean isLogin = Sys.isLogin();
        Log.d(TAG, "CheckLogin: Sys.isLogin() returned: " + isLogin);
        if(!isLogin){
            Log.d(TAG, "CheckLogin: Not logged in, starting LoginActivity.");
            this.startActivity(new Intent(this, LoginActivity.class));
        }
        else{
            Log.d(TAG, "CheckLogin: Logged in, calling Sys.start().");
            Sys.start(viewPager);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        powerLock();
        SocketService.shouldRestart = true;
        
        // 检查权限状态并更新UI
        Sys.checkUpdatePermissions();
        
        // 检查特殊权限是否已授予
        boolean hasAccessibility = PermissionUtils.isAccessibilitySettingsOn(this);
        boolean canDrawOverlays = PermissionUtils.canDrawOverlays(this);
        boolean ignoringBatteryOptimizations = PermissionUtils.isIgnoringBatteryOptimizations(this);
        
        Log.d(TAG, "onResume: Permission status - Accessibility: " + hasAccessibility + 
                ", Overlay: " + canDrawOverlays + 
                ", Battery: " + ignoringBatteryOptimizations);
        
        // 如果有任何必要权限未授予，切换到设置页面
        if (!hasAccessibility || !canDrawOverlays || !ignoringBatteryOptimizations) {
            Log.d(TAG, "onResume: Some permissions not granted, switching to settings tab");
            this.tabLayout.selectTab(this.tabLayout.getTabAt(1));
            return; // 权限不完整时不进行登录检查，避免循环
        }
        
        // 只有在权限完备的情况下才检查登录
        this.CheckLogin();
    }

    @Override
    protected void onPause() {
        super.onPause();
        powerUnlock();
    }

    private MainAdapter prepareViewPager(ViewPager viewPager) {
        MainAdapter adapter=new MainAdapter(getSupportFragmentManager());
        adapter.addFragment(new MainFragment());
        adapter.addFragment(settingsFragment=new SettingsFragment());
        adapter.addFragment(new DebugFragment());
        viewPager.setAdapter(adapter);
        return adapter;
    }

    @Override
    public void grant(String permission) {
        Log.d(TAG, "grant: Requesting permission for " + permission);
        if(permission.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)){
            PermissionUtils.manageOverlayToSettingPage(this);
        }
        else if(permission.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)){
            PermissionUtils.batteryOptimizationToSettingPage(this);
        }
        else{
            this.requestPermissions(new String[]{permission},1);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Sys.onPermissionChangeEvent(PermissionUtils.getAllPermissions(this));
    }

    private class MainAdapter extends FragmentPagerAdapter {
        ArrayList<Fragment> fragmentArrayList= new ArrayList<>();
        public void addFragment(Fragment fragment)
        {
            fragmentArrayList.add(fragment);
        }

        public MainAdapter(FragmentManager supportFragmentManager) {
            super(supportFragmentManager);
        }

        @NonNull
        @Override
        public Fragment getItem(int position) {
            return fragmentArrayList.get(position);
        }

        @Override
        public int getCount() {
            return fragmentArrayList.size();
        }

        @Nullable
        @Override
        public CharSequence getPageTitle(int position) {
            return null;
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }
}
