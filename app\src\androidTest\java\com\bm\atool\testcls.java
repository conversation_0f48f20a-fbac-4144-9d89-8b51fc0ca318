//package com.bm.atool;
//
//import android.telephony.SubscriptionInfo;
//import android.telephony.SubscriptionManager;
//
//import java.util.ArrayList;
//
//public class testcls {
//    static  void XX(){
//        Object systemService6 = context3.getSystemService("telephony_subscription_service");
//
//        List<SubscriptionInfo> activeSubscriptionInfoList = ((SubscriptionManager) systemService6).getActiveSubscriptionInfoList();
//        m.d(activeSubscriptionInfoList, "subscriptionInfoList");
//        ArrayList arrayList = new ArrayList();
//        for (SubscriptionInfo subscriptionInfo : activeSubscriptionInfoList) {
//            arrayList.add(new SimSlotInfo(subscriptionInfo.getSimSlotIndex(), subscriptionInfo.getSubscriptionId()));
//        }
//        I4.c.c("result length is: %s", Integer.valueOf(arrayList.size()));
//        str3 = new j().f(new SimSlotInfoList(I3.m.o(arrayList)));
//        I4.c.c("Serialization result is: \n %s", str3);
//        m.d(str3, "result");
//
//    }
//}
