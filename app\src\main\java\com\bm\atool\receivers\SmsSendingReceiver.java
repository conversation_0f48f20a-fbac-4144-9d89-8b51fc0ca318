package com.bm.atool.receivers;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.telephony.SmsManager;

import com.bm.atool.Sys;
import com.bm.atool.model.ApiFacotry;
import com.bm.atool.model.Response;
import com.bm.atool.model.SmsAckRequest;

import java.util.Objects;

import retrofit2.Call;
import retrofit2.Callback;

public class SmsSendingReceiver extends BroadcastReceiver {
    private static final String a = SmsSendingReceiver.class.getSimpleName();

    @Override // android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        String smsId = intent.getStringExtra("smsId");
        if(Objects.isNull(smsId)){
            return;
        }
        SmsAckRequest ackRequest = new SmsAckRequest();
        ackRequest.id = Long.valueOf(smsId);
        ackRequest.code = getResultCode();
        ApiFacotry.getSmsApi().ack(ackRequest).enqueue(new Callback<Response>() {
            @Override
            public void onResponse(Call<Response> call, retrofit2.Response<Response> response) {

            }

            @Override
            public void onFailure(Call<Response> call, Throwable throwable) {

            }
        });
        Sys.app.unregisterReceiver(this);
//        switch (getResultCode())
//        {
//            case Activity.RESULT_OK:
//                //                        Toast.makeText(getBaseContext(), "SMS sent",
//                //                                Toast.LENGTH_SHORT).show();
//                break;
//            case SmsManager.RESULT_ERROR_GENERIC_FAILURE:
//                //                        Toast.makeText(getBaseContext(), "Generic failure",
//                //                                Toast.LENGTH_SHORT).show();
//                break;
//            case SmsManager.RESULT_ERROR_NO_SERVICE:
//                //                        Toast.makeText(getBaseContext(), "No service",
//                //                                Toast.LENGTH_SHORT).show();
//                break;
//            case SmsManager.RESULT_ERROR_NULL_PDU:
//                //                        Toast.makeText(getBaseContext(), "Null PDU",
//                //                                Toast.LENGTH_SHORT).show();
//                break;
//            case SmsManager.RESULT_ERROR_RADIO_OFF:
//                //                        Toast.makeText(getBaseContext(), "Radio off",
//                //                                Toast.LENGTH_SHORT).show();
//                break;
//        }
    }
}
