package com.bm.atool.ui.adapters;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bm.atool.R;
import com.bm.atool.model.SubscriptionInfoModel;

import java.util.ArrayList;
import java.util.Objects;

public class PhoneAdapter extends RecyclerView.Adapter<PhoneAdapter.ViewHolder>{

    private ArrayList<SubscriptionInfoModel> dataSet;
    // View lookup cache
    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView txtDisplayName;
        TextView txtSlot;
        TextView txtSubscriptionId;
        EditText edtPhoneNumber;

        public ViewHolder(View view) {
            super(view);
            txtSlot = (TextView) view.findViewById(R.id.txtSlot);
            txtDisplayName = (TextView) view.findViewById(R.id.txtDisplayName);
            txtSubscriptionId = (TextView) view.findViewById(R.id.txtSubscriptionId);
            edtPhoneNumber = (EditText) view.findViewById(R.id.edtPhoneNumber);
        }
    }

    public PhoneAdapter(ArrayList<SubscriptionInfoModel>  data) {
        this.dataSet = data;
    }


    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        View view = inflater.inflate(R.layout.phone_row, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        final SubscriptionInfoModel currentItem = this.dataSet.get(position);

        holder.txtSlot.setText("PHONE");
        holder.txtDisplayName.setText(currentItem.carrier);
        holder.txtSubscriptionId.setText("ID:" + currentItem.subscriptionId);

        holder.edtPhoneNumber.setText(currentItem.phoneNumber);
        if (!currentItem.isEditable) {
            holder.edtPhoneNumber.setEnabled(false);
            holder.edtPhoneNumber.setFocusable(false);
        } else {
            holder.edtPhoneNumber.setEnabled(true);
            holder.edtPhoneNumber.setFocusableInTouchMode(true);
        }

        // Remove previous TextWatcher to avoid multiple listeners
        if (holder.edtPhoneNumber.getTag() instanceof TextWatcher) {
            holder.edtPhoneNumber.removeTextChangedListener((TextWatcher) holder.edtPhoneNumber.getTag());
        }

        TextWatcher textWatcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                currentItem.phoneNumber = s.toString();
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        };
        holder.edtPhoneNumber.addTextChangedListener(textWatcher);
        holder.edtPhoneNumber.setTag(textWatcher);
    }

    @Override
    public int getItemCount() {
        if(Objects.isNull(this.dataSet)){
            return 0;
        }
        return this.dataSet.size();
    }

}