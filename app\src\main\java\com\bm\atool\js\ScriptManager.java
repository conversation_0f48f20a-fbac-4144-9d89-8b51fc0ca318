package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

import com.bm.atool.utils.PrefUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JavaScript脚本管理器
 * 负责脚本的存储、加载、版本管理等功能
 */
public class ScriptManager {
    private static final String TAG = "ScriptManager";
    private static final String SCRIPTS_DIR = "js_scripts";
    private static final String SCRIPTS_INDEX_KEY = "js_scripts_index";
    
    private Context context;
    private Gson gson;
    private File scriptsDir;
    
    public ScriptManager(Context context) {
        this.context = context;
        this.gson = new Gson();
        this.scriptsDir = new File(context.getFilesDir(), SCRIPTS_DIR);
        
        if (!scriptsDir.exists()) {
            scriptsDir.mkdirs();
        }
    }
    
    /**
     * 保存脚本
     */
    public boolean saveScript(String name, String content, String description) {
        try {
            ScriptInfo scriptInfo = new ScriptInfo();
            scriptInfo.name = name;
            scriptInfo.description = description;
            scriptInfo.version = getNextVersion(name);
            scriptInfo.createdTime = System.currentTimeMillis();
            scriptInfo.fileName = generateFileName(name, scriptInfo.version);
            
            // 保存脚本文件
            File scriptFile = new File(scriptsDir, scriptInfo.fileName);
            try (FileOutputStream fos = new FileOutputStream(scriptFile)) {
                fos.write(content.getBytes("UTF-8"));
            }
            
            // 更新脚本索引
            updateScriptIndex(scriptInfo);
            
            Log.i(TAG, "脚本保存成功: " + name + " v" + scriptInfo.version);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "保存脚本失败: " + name, e);
            return false;
        }
    }
    
    /**
     * 加载脚本
     */
    public String loadScript(String name) {
        return loadScript(name, -1); // -1表示加载最新版本
    }
    
    /**
     * 加载指定版本的脚本
     */
    public String loadScript(String name, int version) {
        try {
            ScriptInfo scriptInfo = getScriptInfo(name, version);
            if (scriptInfo == null) {
                Log.w(TAG, "脚本不存在: " + name + " v" + version);
                return null;
            }
            
            File scriptFile = new File(scriptsDir, scriptInfo.fileName);
            if (!scriptFile.exists()) {
                Log.w(TAG, "脚本文件不存在: " + scriptInfo.fileName);
                return null;
            }
            
            byte[] buffer = new byte[(int) scriptFile.length()];
            try (FileInputStream fis = new FileInputStream(scriptFile)) {
                fis.read(buffer);
            }
            
            String content = new String(buffer, "UTF-8");
            Log.d(TAG, "脚本加载成功: " + name + " v" + scriptInfo.version);
            return content;
        } catch (Exception e) {
            Log.e(TAG, "加载脚本失败: " + name, e);
            return null;
        }
    }
    
    /**
     * 删除脚本
     */
    public boolean deleteScript(String name, int version) {
        try {
            ScriptInfo scriptInfo = getScriptInfo(name, version);
            if (scriptInfo == null) {
                Log.w(TAG, "要删除的脚本不存在: " + name + " v" + version);
                return false;
            }
            
            // 删除脚本文件
            File scriptFile = new File(scriptsDir, scriptInfo.fileName);
            if (scriptFile.exists()) {
                scriptFile.delete();
            }
            
            // 从索引中移除
            removeFromScriptIndex(name, version);
            
            Log.i(TAG, "脚本删除成功: " + name + " v" + version);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "删除脚本失败: " + name + " v" + version, e);
            return false;
        }
    }
    
    /**
     * 获取所有脚本列表
     */
    public List<ScriptInfo> getAllScripts() {
        try {
            String indexJson = PrefUtil.getString(context, SCRIPTS_INDEX_KEY);
            if (indexJson == null || indexJson.isEmpty()) {
                return new ArrayList<>();
            }
            
            Type listType = new TypeToken<List<ScriptInfo>>(){}.getType();
            List<ScriptInfo> scripts = gson.fromJson(indexJson, listType);
            return scripts != null ? scripts : new ArrayList<>();
        } catch (Exception e) {
            Log.e(TAG, "获取脚本列表失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取指定脚本的所有版本
     */
    public List<ScriptInfo> getScriptVersions(String name) {
        List<ScriptInfo> allScripts = getAllScripts();
        List<ScriptInfo> versions = new ArrayList<>();
        
        for (ScriptInfo script : allScripts) {
            if (name.equals(script.name)) {
                versions.add(script);
            }
        }
        
        // 按版本号排序（降序）
        versions.sort((a, b) -> Integer.compare(b.version, a.version));
        
        return versions;
    }
    
    /**
     * 获取脚本信息
     */
    private ScriptInfo getScriptInfo(String name, int version) {
        List<ScriptInfo> allScripts = getAllScripts();
        
        if (version == -1) {
            // 获取最新版本
            ScriptInfo latest = null;
            for (ScriptInfo script : allScripts) {
                if (name.equals(script.name)) {
                    if (latest == null || script.version > latest.version) {
                        latest = script;
                    }
                }
            }
            return latest;
        } else {
            // 获取指定版本
            for (ScriptInfo script : allScripts) {
                if (name.equals(script.name) && script.version == version) {
                    return script;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 获取下一个版本号
     */
    private int getNextVersion(String name) {
        List<ScriptInfo> versions = getScriptVersions(name);
        if (versions.isEmpty()) {
            return 1;
        }
        return versions.get(0).version + 1;
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String name, int version) {
        return name.replaceAll("[^a-zA-Z0-9_-]", "_") + "_v" + version + ".js";
    }
    
    /**
     * 更新脚本索引
     */
    private void updateScriptIndex(ScriptInfo newScript) {
        List<ScriptInfo> allScripts = getAllScripts();
        allScripts.add(newScript);
        
        String indexJson = gson.toJson(allScripts);
        PrefUtil.putString(context, SCRIPTS_INDEX_KEY, indexJson);
    }
    
    /**
     * 从脚本索引中移除
     */
    private void removeFromScriptIndex(String name, int version) {
        List<ScriptInfo> allScripts = getAllScripts();
        allScripts.removeIf(script -> name.equals(script.name) && script.version == version);
        
        String indexJson = gson.toJson(allScripts);
        PrefUtil.putString(context, SCRIPTS_INDEX_KEY, indexJson);
    }
    
    /**
     * 清理所有脚本
     */
    public void clearAllScripts() {
        try {
            // 删除所有脚本文件
            File[] files = scriptsDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    file.delete();
                }
            }
            
            // 清空索引
            PrefUtil.putString(context, SCRIPTS_INDEX_KEY, "");
            
            Log.i(TAG, "所有脚本已清理");
        } catch (Exception e) {
            Log.e(TAG, "清理脚本失败", e);
        }
    }
    
    /**
     * 脚本信息类
     */
    public static class ScriptInfo {
        public String name;
        public String description;
        public int version;
        public long createdTime;
        public String fileName;
        
        @Override
        public String toString() {
            return name + " v" + version + " (" + description + ")";
        }
    }
}
