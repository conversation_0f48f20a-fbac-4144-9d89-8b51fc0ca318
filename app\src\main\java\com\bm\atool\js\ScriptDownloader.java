package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import io.socket.client.Socket;

/**
 * JavaScript脚本下载器
 * 支持HTTP和Socket两种方式下载脚本
 */
public class ScriptDownloader {
    private static final String TAG = "ScriptDownloader";
    private static final int CONNECT_TIMEOUT = 10000; // 10秒
    private static final int READ_TIMEOUT = 30000; // 30秒
    
    private Context context;
    private Socket socket;
    private ExecutorService executorService;
    private Gson gson;
    private ScriptManager scriptManager;
    
    public ScriptDownloader(Context context) {
        this(context, null);
    }
    
    public ScriptDownloader(Context context, Socket socket) {
        this.context = context;
        this.socket = socket;
        this.executorService = Executors.newCachedThreadPool();
        this.gson = new Gson();
        this.scriptManager = new ScriptManager(context);
    }
    
    /**
     * 通过HTTP下载脚本
     */
    public CompletableFuture<DownloadResult> downloadScriptHttp(String url, String scriptName, String description) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Log.d(TAG, "开始HTTP下载脚本: " + url);
                
                URL httpUrl = new URL(url);
                HttpURLConnection connection = (HttpURLConnection) httpUrl.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(CONNECT_TIMEOUT);
                connection.setReadTimeout(READ_TIMEOUT);
                connection.setRequestProperty("User-Agent", "AndroidTool/1.0");
                
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    String scriptContent = readInputStream(connection.getInputStream());
                    
                    // 保存脚本
                    boolean saved = scriptManager.saveScript(scriptName, scriptContent, description);
                    if (saved) {
                        Log.i(TAG, "HTTP脚本下载并保存成功: " + scriptName);
                        return new DownloadResult(true, scriptContent, "脚本下载成功");
                    } else {
                        Log.e(TAG, "脚本保存失败: " + scriptName);
                        return new DownloadResult(false, null, "脚本保存失败");
                    }
                } else {
                    String error = "HTTP错误: " + responseCode;
                    Log.e(TAG, error);
                    return new DownloadResult(false, null, error);
                }
            } catch (Exception e) {
                Log.e(TAG, "HTTP下载脚本失败", e);
                return new DownloadResult(false, null, e.getMessage());
            }
        }, executorService);
    }
    
    /**
     * 下载脚本（简化方法）
     */
    public CompletableFuture<DownloadResult> downloadScript(String url, int timeout) {
        return downloadScriptHttp(url, "downloaded_script", "下载的脚本");
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        close();
    }
    
    /**
     * 通过Socket请求脚本
     */
    public CompletableFuture<DownloadResult> requestScriptSocket(String scriptName, String version) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (socket == null || !socket.connected()) {
                    return new DownloadResult(false, null, "Socket未连接");
                }
                
                Log.d(TAG, "通过Socket请求脚本: " + scriptName + " v" + version);
                
                // 构建请求消息
                ScriptRequest request = new ScriptRequest();
                request.scriptName = scriptName;
                request.version = version;
                request.requestId = System.currentTimeMillis();
                
                // 发送请求
                socket.emit("script_request", gson.toJson(request));
                
                // 注意：这里只是发送请求，实际的脚本接收需要在Socket监听器中处理
                Log.i(TAG, "脚本请求已发送: " + scriptName);
                return new DownloadResult(true, null, "脚本请求已发送，等待服务器响应");
                
            } catch (Exception e) {
                Log.e(TAG, "Socket请求脚本失败", e);
                return new DownloadResult(false, null, e.getMessage());
            }
        }, executorService);
    }
    
    /**
     * 通过Socket请求脚本
     */
    public CompletableFuture<DownloadResult> requestScriptViaSocket(String scriptId, int timeout) {
        return requestScriptSocket(scriptId, "1.0");
    }
    
    /**
     * 处理Socket接收到的脚本
     */
    public DownloadResult handleSocketScript(String scriptData) {
        try {
            Log.d(TAG, "处理Socket接收的脚本数据");
            
            ScriptResponse response = gson.fromJson(scriptData, ScriptResponse.class);
            if (response == null || response.scriptContent == null) {
                return new DownloadResult(false, null, "脚本数据格式错误");
            }
            
            // 保存脚本
            boolean saved = scriptManager.saveScript(
                response.scriptName, 
                response.scriptContent, 
                response.description != null ? response.description : "从服务器下载"
            );
            
            if (saved) {
                Log.i(TAG, "Socket脚本保存成功: " + response.scriptName);
                return new DownloadResult(true, response.scriptContent, "脚本接收并保存成功");
            } else {
                Log.e(TAG, "Socket脚本保存失败: " + response.scriptName);
                return new DownloadResult(false, null, "脚本保存失败");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "处理Socket脚本失败", e);
            return new DownloadResult(false, null, e.getMessage());
        }
    }
    
    /**
     * 读取输入流内容
     */
    private String readInputStream(InputStream inputStream) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }
    
    /**
     * 关闭下载器
     */
    public void close() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
    
    /**
     * 脚本请求类
     */
    public static class ScriptRequest {
        public String scriptName;
        public String version;
        public long requestId;
    }
    
    /**
     * 脚本响应类
     */
    public static class ScriptResponse {
        public String scriptName;
        public String scriptContent;
        public String description;
        public String version;
        public long timestamp;
    }
    
    /**
     * 下载结果类
     */
    public static class DownloadResult {
        public final boolean success;
        public final String scriptContent;
        public final String message;
        public final String script;
        public final String error;
        
        public DownloadResult(boolean success, String scriptContent, String message) {
            this.success = success;
            this.scriptContent = scriptContent;
            this.message = message;
            this.script = scriptContent;
            this.error = success ? null : message;
        }
        
        @Override
        public String toString() {
            return success ? "Success: " + message : "Error: " + message;
        }
    }
}
