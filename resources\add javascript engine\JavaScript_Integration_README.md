# Android Tool JavaScript 集成说明

## 概述

本项目已成功集成 QuickJS JavaScript 引擎，允许通过 WebSocket 接收和执行 JavaScript 脚本，实现动态功能扩展。

## 功能特性

### 1. JavaScript 引擎核心功能
- **脚本执行**: 支持执行任意 JavaScript 代码
- **函数调用**: 支持调用预定义的 JavaScript 函数
- **脚本管理**: 支持脚本的保存、加载和版本管理
- **错误处理**: 完善的异常捕获和错误报告
- **超时控制**: 防止脚本无限执行

### 2. Android API 桥接
JavaScript 环境中可以使用以下 Android 原生功能：

#### 系统信息 API
```javascript
// 获取设备信息
var deviceInfo = System.getDeviceInfo();
console.log(deviceInfo);

// 获取手机号码列表
var phones = System.getPhoneNumbers();
console.log(phones);

// 获取电池电量
var battery = System.getBatteryLevel();
console.log(battery);
```

#### 短信 API
```javascript
// 发送短信
var result = SMS.send("手机号码", "短信内容", "目标手机");
console.log(result);

// 清理短信
var result = SMS.clear("发送方号码");
console.log(result);
```

#### USSD API
```javascript
// 执行USSD代码
var result = USSD.execute("*100#", "目标手机", 15);
console.log(result);
```

#### 日志 API
```javascript
console.log("信息日志");
console.warn("警告日志");
console.error("错误日志");
console.debug("调试日志");
```

#### 工具 API
```javascript
// 获取当前时间戳
var timestamp = Android.getCurrentTimestamp();

// 休眠指定毫秒数
Android.sleep(1000);

// 发送WebSocket消息
Android.emitSocketMessage("eventName", "data");
```

## WebSocket 接口

### 1. 执行 JavaScript (`executeJS`)

发送格式：
```json
{
    "id": "请求ID",
    "script": "JavaScript代码",
    "type": "execute",
    "timeout": 30
}
```

响应格式：
```json
{
    "id": "请求ID",
    "success": true,
    "result": "执行结果",
    "error": null,
    "executionTime": 123,
    "timestamp": 1234567890
}
```

### 2. 调用函数 (`executeJS`)

发送格式：
```json
{
    "id": "请求ID",
    "type": "function",
    "functionName": "函数名",
    "args": ["参数1", "参数2"],
    "timeout": 30
}
```

### 3. 加载脚本 (`executeJS`)

发送格式：
```json
{
    "id": "请求ID",
    "type": "load",
    "scriptName": "脚本名称",
    "scriptVersion": -1
}
```

## 调试界面

应用内置了 JavaScript 调试界面，位于 Debug 标签页：

### 功能包括：
1. **代码编辑器**: 支持多行 JavaScript 代码编辑
2. **快速测试**: 预设的常用测试脚本
3. **执行结果**: 实时显示执行结果和错误信息
4. **性能监控**: 显示脚本执行时间

### 快速测试按钮：
- **Device Info**: 获取设备信息
- **Phone Numbers**: 获取手机号码列表
- **Battery**: 获取电池电量

## 测试方法

### 1. 应用内测试
1. 打开应用的 Debug 标签页
2. 在代码编辑器中输入 JavaScript 代码
3. 点击 "Execute" 按钮执行
4. 查看执行结果

### 2. WebSocket 客户端测试
1. 打开 `test_websocket_client.html` 文件
2. 输入 WebSocket 服务器地址
3. 点击连接按钮
4. 输入 JavaScript 代码并执行

### 3. 示例脚本
项目包含多个示例脚本：
- `test_js_integration.js`: 基本功能测试
- `app/src/main/assets/js_examples/`: 各种功能示例

## 安全注意事项

1. **权限控制**: JavaScript 只能访问预定义的 Android API
2. **超时保护**: 所有脚本执行都有超时限制
3. **异常隔离**: JavaScript 异常不会影响主应用
4. **资源管理**: 引擎会自动管理内存和资源

## 开发指南

### 添加新的 Android API

1. 在 `AndroidBridge.java` 中添加新方法
2. 在 `JavaScriptEngine.java` 中绑定到 JavaScript 环境
3. 更新文档和示例

### 脚本管理

使用 `ScriptManager` 类进行脚本管理：
```java
ScriptManager scriptManager = new ScriptManager(context);

// 保存脚本
scriptManager.saveScript("scriptName", "scriptContent", "description");

// 加载脚本
String script = scriptManager.loadScript("scriptName");

// 获取所有脚本
List<ScriptInfo> scripts = scriptManager.getAllScripts();
```

## 故障排除

### 常见问题

1. **JavaScript 引擎初始化失败**
   - 检查 QuickJS 依赖是否正确添加
   - 确认设备支持 QuickJS

2. **脚本执行超时**
   - 检查脚本是否有无限循环
   - 增加超时时间设置

3. **API 调用失败**
   - 确认应用有相应权限
   - 检查 Android API 是否正确绑定

### 日志调试

使用 Android Studio 的 Logcat 查看详细日志：
- `JavaScriptEngine`: JavaScript 引擎相关日志
- `AndroidBridge`: API 桥接相关日志
- `SocketService`: WebSocket 通信日志

## 版本信息

- QuickJS 版本: 0.9.2
- 支持的 Android 版本: API 24+
- 集成完成时间: 2025-08-01

## 后续计划

1. 添加更多 Android API 支持
2. 实现脚本调试功能
3. 添加脚本性能分析
4. 支持 JavaScript 模块系统
