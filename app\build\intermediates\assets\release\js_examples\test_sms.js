// 测试发送短信功能
console.log("开始测试短信发送...");

try {
    // 注意：这只是测试代码，实际使用时请替换为真实的手机号码
    var result = SMS.send("1234567890", "这是一条测试短信", null);
    console.log("短信发送结果: " + result);
    
    var response = JSON.parse(result);
    if (response.success) {
        "短信发送请求已提交，ID: " + response.id;
    } else {
        "短信发送失败: " + response.error;
    }
} catch (e) {
    console.error("短信发送测试失败: " + e.message);
    "错误: " + e.message;
}
