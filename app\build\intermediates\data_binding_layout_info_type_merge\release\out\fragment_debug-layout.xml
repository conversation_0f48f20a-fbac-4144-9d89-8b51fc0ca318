<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_debug" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_debug.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_debug_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="177" endOffset="12"/></Target><Target id="@+id/debugTitle" view="TextView"><Expressions/><location startLine="12" startOffset="8" endLine="23" endOffset="47"/></Target><Target id="@+id/etJavaScript" view="EditText"><Expressions/><location startLine="35" startOffset="8" endLine="48" endOffset="46"/></Target><Target id="@+id/btnExecuteJS" view="Button"><Expressions/><location startLine="56" startOffset="12" endLine="64" endOffset="47"/></Target><Target id="@+id/btnClearJS" view="Button"><Expressions/><location startLine="66" startOffset="12" endLine="74" endOffset="49"/></Target><Target id="@+id/btnTestDeviceInfo" view="Button"><Expressions/><location startLine="94" startOffset="12" endLine="103" endOffset="47"/></Target><Target id="@+id/btnTestPhones" view="Button"><Expressions/><location startLine="105" startOffset="12" endLine="115" endOffset="47"/></Target><Target id="@+id/btnTestBattery" view="Button"><Expressions/><location startLine="117" startOffset="12" endLine="126" endOffset="49"/></Target><Target id="@+id/tvJSResult" view="TextView"><Expressions/><location startLine="140" startOffset="8" endLine="151" endOffset="47"/></Target><Target id="@+id/btnStopSocket" view="Button"><Expressions/><location startLine="163" startOffset="8" endLine="173" endOffset="47"/></Target></Targets></Layout>