<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <string-array name="reply_entries">
        <item>Reply</item>
        <item>Reply to all</item>
    </string-array>
    <string-array name="reply_values">
        <item>reply</item>
        <item>reply_all</item>
    </string-array>
    <color name="accent_color">#81C784</color>
    <color name="app_background_color">#ECF5EE</color>
    <color name="black">#FF000000</color>
    <color name="button_accent">#81C784</color>
    <color name="button_primary">#66BB6A</color>
    <color name="button_secondary">#90A4AE</color>
    <color name="check_mark_color">#FFECB3</color>
    <color name="green">#4CAF50</color>
    <color name="header_background">#DAEADC</color>
    <color name="input_background">#F5F5F5</color>
    <color name="surface_background">#F8FFF9</color>
    <color name="text_error">#F44336</color>
    <color name="text_primary">#37474F</color>
    <color name="text_secondary">#607D8B</color>
    <color name="text_success">#4CAF50</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="fab_margin">16dp</dimen>
    <string name="action_settings">Settings</string>
    <string name="app_name">android-tool</string>
    <string name="attachment_summary_off">Only download attachments when manually requested</string>
    <string name="attachment_summary_on">Automatically download attachments for incoming emails
    </string>
    <string name="attachment_title">Download incoming attachments</string>
    <string name="first_fragment_label">First Fragment</string>
    <string name="lorem_ipsum">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris
        volutpat, dolor id interdum ullamcorper, risus dolor egestas lectus, sit amet mattis purus
        dui nec risus. Maecenas non sodales nisi, vel dictum dolor. Class aptent taciti sociosqu ad
        litora torquent per conubia nostra, per inceptos himenaeos. Suspendisse blandit eleifend
        diam, vel rutrum tellus vulputate quis. Aliquam eget libero aliquet, imperdiet nisl a,
        ornare ex. Sed rhoncus est ut libero porta lobortis. Fusce in dictum tellus.\n\n
        Suspendisse interdum ornare ante. Aliquam nec cursus lorem. Morbi id magna felis. Vivamus
        egestas, est a condimentum egestas, turpis nisl iaculis ipsum, in dictum tellus dolor sed
        neque. Morbi tellus erat, dapibus ut sem a, iaculis tincidunt dui. Interdum et malesuada
        fames ac ante ipsum primis in faucibus. Curabitur et eros porttitor, ultricies urna vitae,
        molestie nibh. Phasellus at commodo eros, non aliquet metus. Sed maximus nisl nec dolor
        bibendum, vel congue leo egestas.\n\n
        Sed interdum tortor nibh, in sagittis risus mollis quis. Curabitur mi odio, condimentum sit
        amet auctor at, mollis non turpis. Nullam pretium libero vestibulum, finibus orci vel,
        molestie quam. Fusce blandit tincidunt nulla, quis sollicitudin libero facilisis et. Integer
        interdum nunc ligula, et fermentum metus hendrerit id. Vestibulum lectus felis, dictum at
        lacinia sit amet, tristique id quam. Cras eu consequat dui. Suspendisse sodales nunc ligula,
        in lobortis sem porta sed. Integer id ultrices magna, in luctus elit. Sed a pellentesque
        est.\n\n
        Aenean nunc velit, lacinia sed dolor sed, ultrices viverra nulla. Etiam a venenatis nibh.
        Morbi laoreet, tortor sed facilisis varius, nibh orci rhoncus nulla, id elementum leo dui
        non lorem. Nam mollis ipsum quis auctor varius. Quisque elementum eu libero sed commodo. In
        eros nisl, imperdiet vel imperdiet et, scelerisque a mauris. Pellentesque varius ex nunc,
        quis imperdiet eros placerat ac. Duis finibus orci et est auctor tincidunt. Sed non viverra
        ipsum. Nunc quis augue egestas, cursus lorem at, molestie sem. Morbi a consectetur ipsum, a
        placerat diam. Etiam vulputate dignissim convallis. Integer faucibus mauris sit amet finibus
        convallis.\n\n
        Phasellus in aliquet mi. Pellentesque habitant morbi tristique senectus et netus et
        malesuada fames ac turpis egestas. In volutpat arcu ut felis sagittis, in finibus massa
        gravida. Pellentesque id tellus orci. Integer dictum, lorem sed efficitur ullamcorper,
        libero justo consectetur ipsum, in mollis nisl ex sed nisl. Donec maximus ullamcorper
        sodales. Praesent bibendum rhoncus tellus nec feugiat. In a ornare nulla. Donec rhoncus
        libero vel nunc consequat, quis tincidunt nisl eleifend. Cras bibendum enim a justo luctus
        vestibulum. Fusce dictum libero quis erat maximus, vitae volutpat diam dignissim.
    </string>
    <string name="messages_header">Messages</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>
    <string name="reply_title">Default reply action</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="signature_title">Your signature</string>
    <string name="sync_header">Sync</string>
    <string name="sync_title">Sync email periodically</string>
    <string name="title_activity_settings">SettingsActivity</string>
    <style name="Base.Theme.AndroidTool" parent="Theme.Material3.DayNight.NoActionBar">
        
         <item name="colorPrimary">#F5F5F5</item>
        <item name="android:statusBarColor" ns1:targetApi="l">#000000</item> 
        <item name="colorControlNormal">#000000</item>
        <item name="colorControlActivated">@color/green</item>
        <item name="colorControlHighlight">@color/green</item>
    </style>
    <style name="Theme.AndroidTool" parent="Base.Theme.AndroidTool"/>
</resources>