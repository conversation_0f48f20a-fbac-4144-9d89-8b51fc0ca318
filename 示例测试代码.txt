在应用的Debug页面中，你可以尝试以下JavaScript代码：

=== 基础测试 ===

1. 设备信息测试：
System.getDeviceInfo()

2. 手机号码测试：
System.getPhoneNumbers()

3. 电池电量测试：
System.getBatteryLevel()

4. 时间戳测试：
Android.getCurrentTimestamp()

=== 综合测试 ===

console.log("开始JavaScript引擎测试");
var deviceInfo = System.getDeviceInfo();
console.log("设备信息:", deviceInfo);

var phoneNumbers = System.getPhoneNumbers();
console.log("手机号码:", phoneNumbers);

var battery = System.getBatteryLevel();
console.log("电池电量:", battery + "%");

var timestamp = Android.getCurrentTimestamp();
console.log("当前时间:", new Date(timestamp).toLocaleString());

"JavaScript引擎测试完成！";

=== JSON处理测试 ===

var testData = {
    name: "JavaScript测试",
    timestamp: Android.getCurrentTimestamp(),
    battery: System.getBatteryLevel(),
    device: System.getDeviceInfo()
};

var jsonStr = JSON.stringify(testData, null, 2);
console.log("JSON数据:", jsonStr);

var parsed = JSON.parse(jsonStr);
console.log("解析后的名称:", parsed.name);

"JSON处理测试完成";

=== 延时测试 ===

console.log("开始延时测试");
var start = Android.getCurrentTimestamp();
console.log("开始时间:", start);

Android.sleep(2000); // 等待2秒

var end = Android.getCurrentTimestamp();
console.log("结束时间:", end);
console.log("实际耗时:", end - start, "毫秒");

"延时测试完成";
