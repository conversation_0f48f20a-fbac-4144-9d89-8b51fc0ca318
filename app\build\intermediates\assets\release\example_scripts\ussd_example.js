/**
 * USSD 功能示例脚本
 * 演示如何使用 USSD 对象执行各种 USSD 代码
 */

console.log("=== USSD 功能示例 ===");

// 获取设备信息
var deviceInfo = System.getDeviceInfo();
console.log("设备信息:", deviceInfo);

// 获取手机号码列表
var phoneNumbers = System.getPhoneNumbers();
console.log("可用手机号码:", phoneNumbers);

// 示例1: 查询话费余额
function checkBalance() {
    console.log("\n--- 查询话费余额 ---");
    
    var ussdCode = "*100#"; // 常见的余额查询代码
    var result = USSD.execute(ussdCode, null, 30);
    
    console.log("USSD执行结果:", result);
    
    if (result.success) {
        console.log("查询成功!");
        console.log("响应内容:", result.response);
    } else {
        console.log("查询失败:", result.error);
    }
}

// 示例2: 使用指定SIM卡查询余额
function checkBalanceWithSpecificSIM() {
    console.log("\n--- 使用指定SIM卡查询余额 ---");
    
    var phoneNumbers = JSON.parse(System.getPhoneNumbers());
    if (phoneNumbers.length > 0) {
        var targetPhone = phoneNumbers[0].phoneNumber;
        console.log("使用SIM卡:", targetPhone);
        
        var ussdCode = "*100#";
        var result = USSD.execute(ussdCode, targetPhone, 30);
        
        console.log("USSD执行结果:", result);
    } else {
        console.log("没有可用的SIM卡");
    }
}

// 示例3: 查询流量使用情况
function checkDataUsage() {
    console.log("\n--- 查询流量使用情况 ---");
    
    var ussdCode = "*121#"; // 常见的流量查询代码
    var result = USSD.execute(ussdCode, null, 30);
    
    console.log("流量查询结果:", result);
    
    if (result.success) {
        console.log("查询成功!");
        console.log("流量信息:", result.response);
    } else {
        console.log("查询失败:", result.error);
    }
}

// 示例4: 查询套餐信息
function checkPlanInfo() {
    console.log("\n--- 查询套餐信息 ---");
    
    var ussdCode = "*101#"; // 套餐查询代码
    var result = USSD.execute(ussdCode, null, 30);
    
    console.log("套餐查询结果:", result);
    
    if (result.success) {
        console.log("套餐信息:", result.response);
    } else {
        console.log("查询失败:", result.error);
    }
}

// 示例5: 批量查询多个USSD代码
function batchUSSDQuery() {
    console.log("\n--- 批量USSD查询 ---");
    
    var ussdCodes = [
        {code: "*100#", description: "余额查询"},
        {code: "*121#", description: "流量查询"},
        {code: "*101#", description: "套餐查询"},
        {code: "*102#", description: "积分查询"}
    ];
    
    for (var i = 0; i < ussdCodes.length; i++) {
        var ussd = ussdCodes[i];
        console.log("\n执行:", ussd.description, "(" + ussd.code + ")");
        
        var result = USSD.execute(ussd.code, null, 30);
        
        if (result.success) {
            console.log("✓ 成功:", result.response.substring(0, 100) + "...");
        } else {
            console.log("✗ 失败:", result.error);
        }
        
        // 延迟2秒避免请求过快
        Android.sleep(2000);
    }
}

// 示例6: 带超时控制的USSD查询
function ussdWithTimeout() {
    console.log("\n--- 带超时控制的USSD查询 ---");
    
    var ussdCode = "*100#";
    var timeout = 10; // 10秒超时
    
    console.log("执行USSD:", ussdCode, "超时:", timeout + "秒");
    
    var startTime = Date.now();
    var result = USSD.execute(ussdCode, null, timeout);
    var endTime = Date.now();
    
    console.log("执行时间:", (endTime - startTime) + "ms");
    console.log("执行结果:", result);
}

// 示例7: 错误处理演示
function demonstrateErrorHandling() {
    console.log("\n--- 错误处理演示 ---");
    
    // 尝试执行无效USSD代码
    var result1 = USSD.execute("", null, 30);
    console.log("空USSD代码结果:", result1);
    
    // 尝试使用无效SIM卡
    var result2 = USSD.execute("*100#", "invalid_sim", 30);
    console.log("无效SIM卡结果:", result2);
    
    // 尝试使用极短超时
    var result3 = USSD.execute("*100#", null, 1);
    console.log("极短超时结果:", result3);
}

// 示例8: 解析USSD响应
function parseUSSDResponse() {
    console.log("\n--- 解析USSD响应 ---");
    
    var ussdCode = "*100#";
    var result = USSD.execute(ussdCode, null, 30);
    
    if (result.success && result.response) {
        console.log("原始响应:", result.response);
        
        // 尝试提取余额信息（示例解析）
        var response = result.response;
        
        // 查找数字模式（可能是余额）
        var balanceMatch = response.match(/(\d+\.?\d*)\s*(元|yuan|RMB)/i);
        if (balanceMatch) {
            console.log("提取到余额:", balanceMatch[1], balanceMatch[2]);
        }
        
        // 查找流量信息
        var dataMatch = response.match(/(\d+\.?\d*)\s*(MB|GB|KB)/i);
        if (dataMatch) {
            console.log("提取到流量:", dataMatch[1], dataMatch[2]);
        }
        
        // 查找有效期信息
        var dateMatch = response.match(/(\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4})/);
        if (dateMatch) {
            console.log("提取到日期:", dateMatch[1]);
        }
    }
}

// 主函数
function main() {
    try {
        console.log("开始USSD功能演示...");
        
        // 执行各种示例
        checkBalance();
        checkBalanceWithSpecificSIM();
        checkDataUsage();
        checkPlanInfo();
        batchUSSDQuery();
        ussdWithTimeout();
        demonstrateErrorHandling();
        parseUSSDResponse();
        
        console.log("\n=== USSD 功能演示完成 ===");
        
    } catch (error) {
        console.log("脚本执行出错:", error.toString());
    }
}

// 执行主函数
main();
