{"logs": [{"outputFile": "com.bm.atool.app-mergeReleaseResources-43:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\824ec4d3370c7964c2739cc04b83d28a\\transformed\\core-1.13.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3039,3136,3238,3336,3433,3535,3641,8602", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3131,3233,3331,3428,3530,3636,3747,8698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0dd6b178300a398c5583e81b52d2ca55\\transformed\\navigation-ui-2.7.7\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8011,8116", "endColumns": "104,116", "endOffsets": "8111,8228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5f99e54b54e0fd99cba6c075cd2f82e8\\transformed\\preference-1.2.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4042,4177,8233,8390,8703,8872,8955", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "4108,4259,8306,8517,8867,8950,9028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\065662a114d9e980de2eb8602aa46dfe\\transformed\\material-1.5.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,297,393,505,587,651,742,819,880,971,1034,1093,1162,1225,1279,1387,1445,1507,1561,1634,1755,1839,1930,2040,2117,2193,2280,2347,2413,2483,2560,2643,2714,2789,2867,2938,3023,3112,3207,3300,3372,3444,3540,3592,3659,3743,3833,3895,3959,4022,4116,4212,4301,4398", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "215,292,388,500,582,646,737,814,875,966,1029,1088,1157,1220,1274,1382,1440,1502,1556,1629,1750,1834,1925,2035,2112,2188,2275,2342,2408,2478,2555,2638,2709,2784,2862,2933,3018,3107,3202,3295,3367,3439,3535,3587,3654,3738,3828,3890,3954,4017,4111,4207,4296,4393,4472"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2962,3752,3848,3960,4113,4264,4355,4432,4493,4584,4647,4706,4775,4838,4892,5000,5058,5120,5174,5247,5368,5452,5543,5653,5730,5806,5893,5960,6026,6096,6173,6256,6327,6402,6480,6551,6636,6725,6820,6913,6985,7057,7153,7205,7272,7356,7446,7508,7572,7635,7729,7825,7914,8311", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,76,95,111,81,63,90,76,60,90,62,58,68,62,53,107,57,61,53,72,120,83,90,109,76,75,86,66,65,69,76,82,70,74,77,70,84,88,94,92,71,71,95,51,66,83,89,61,63,62,93,95,88,96,78", "endOffsets": "265,3034,3843,3955,4037,4172,4350,4427,4488,4579,4642,4701,4770,4833,4887,4995,5053,5115,5169,5242,5363,5447,5538,5648,5725,5801,5888,5955,6021,6091,6168,6251,6322,6397,6475,6546,6631,6720,6815,6908,6980,7052,7148,7200,7267,7351,7441,7503,7567,7630,7724,7820,7909,8006,8385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\80aca24db86cd42a34c9c5f0872fe864\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,384,483,595,680,786,906,986,1061,1152,1245,1337,1431,1531,1624,1726,1821,1912,2003,2082,2189,2293,2389,2496,2599,2708,2864,8522", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "379,478,590,675,781,901,981,1056,1147,1240,1332,1426,1526,1619,1721,1816,1907,1998,2077,2184,2288,2384,2491,2594,2703,2859,2957,8597"}}]}]}