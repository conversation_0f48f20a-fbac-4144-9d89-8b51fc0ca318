<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.2.0">

    <issue
        id="ScopedStorage"
        severity="Warning"
        message="READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: `READ_MEDIA_IMAGES`, `READ_MEDIA_VIDEO` or `READ_MEDIA_AUDIO`."
        category="Correctness"
        priority="8"
        summary="Affected by scoped storage"
        explanation="Scoped storage is enforced on Android 10+ (or Android 11+ if using `requestLegacyExternalStorage`). In particular, `WRITE_EXTERNAL_STORAGE` will no longer provide write access to all files; it will provide the equivalent of `READ_EXTERNAL_STORAGE` instead.&#xA;&#xA;As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:&#xA;* `android.permission.READ_MEDIA_IMAGES`&#xA;* `android.permission.READ_MEDIA_VIDEO`&#xA;* `android.permission.READ_MEDIA_AUDIO`&#xA;&#xA;and then add `maxSdkVersion=&quot;33&quot;` to the older permission. See the developer guide for how to do this: https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions&#xA;&#xA;The `MANAGE_EXTERNAL_STORAGE` permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at https://goo.gle/android-mediastore-createwriterequest.&#xA;&#xA;To learn more, read these resources: Play policy: https://goo.gle/policy-storage-help Allowable use cases: https://goo.gle/policy-storage-usecases"
        url="https://goo.gle/android-storage-usecases"
        urls="https://goo.gle/android-storage-usecases"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml"
            line="29"
            column="36"/>
    </issue>

    <issue
        id="TextViewEdits"
        severity="Warning"
        message="Attribute `android:inputType` should not be used with `&lt;TextView>`: Change element type to `&lt;EditText>` ?"
        category="Correctness"
        priority="7"
        summary="TextView should probably be an EditText instead"
        explanation="Using a `&lt;TextView>` to input text is generally an error, you should be using `&lt;EditText>` instead.  `EditText` is a subclass of `TextView`, and some of the editing support is provided by `TextView`, so it&apos;s possible to set some input-related properties on a `TextView`. However, using a `TextView` along with input attributes is usually a cut &amp; paste error. To input text you should be using `&lt;EditText>`.&#xA;&#xA;This check also checks subclasses of `TextView`, such as `Button` and `CheckBox`, since these have the same issue: they should not be used with editable attributes."
        errorLine1="                android:inputType=&quot;text&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="87"
            column="17"/>
    </issue>

    <issue
        id="ApplySharedPref"
        severity="Warning"
        message="Consider using `apply()` instead; `commit` writes its data to persistent storage immediately, whereas `apply` will handle it in the background"
        category="Correctness"
        priority="6"
        summary="Use `apply()` on `SharedPreferences`"
        explanation="Consider using `apply()` instead of `commit` on shared preferences. Whereas `commit` blocks and writes its data to persistent storage immediately, `apply` will handle it in the background."
        errorLine1="        editor.commit();"
        errorLine2="               ~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java"
            line="141"
            column="16"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        switch (level.toUpperCase()) {"
        errorLine2="                      ~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\js\AndroidBridge.java"
            line="49"
            column="23"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                String resultText = String.format("
        errorLine2="                                    ^">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\DebugFragment.java"
            line="139"
            column="37"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        boolean isEnabled = enabledServices.toLowerCase().contains(serviceId.toLowerCase());"
        errorLine2="                                            ~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="123"
            column="45"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        boolean isEnabled = enabledServices.toLowerCase().contains(serviceId.toLowerCase());"
        errorLine2="                                                                             ~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="123"
            column="78"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            return String.format(&quot;Task[%s] %s - %s (执行次数: %d, 成功: %d, 失败: %d)&quot;, "
        errorLine2="                   ^">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\js\ScriptScheduler.java"
            line="268"
            column="20"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                Log.d(TAG, &quot;获取到网络国家代码: &quot; + networkCountryIso.toUpperCase());"
        errorLine2="                                                             ~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java"
            line="867"
            column="62"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.US)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                return networkCountryIso.toUpperCase();"
        errorLine2="                                         ~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java"
            line="868"
            column="42"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 28 (current min is 24): `android.Manifest.permission#FOREGROUND_SERVICE`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="        permissions.add(createPermissionModel(context,&quot;Foreground Service&quot;, Manifest.permission.FOREGROUND_SERVICE, false));"
        errorLine2="                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="50"
            column="77"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 33 (current min is 24): `android.Manifest.permission#POST_NOTIFICATIONS`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="        permissions.add(createPermissionModel(context,&quot;Notifications&quot;, Manifest.permission.POST_NOTIFICATIONS, false));"
        errorLine2="                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="51"
            column="72"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 26 (current min is 24): `android.Manifest.permission#READ_PHONE_NUMBERS`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="        permissions.add(createPermissionModel(context,&quot;Read Phone Numbers&quot;, Manifest.permission.READ_PHONE_NUMBERS, false));"
        errorLine2="                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="59"
            column="77"/>
    </issue>

    <issue
        id="InlinedApi"
        severity="Warning"
        message="Field requires API level 33 (current min is 26): `android.content.Context#RECEIVER_EXPORTED`"
        category="Correctness"
        priority="6"
        summary="Using inlined constants on older versions"
        explanation="This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that&apos;s fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it&apos;s safe and can be suppressed or whether the code needs to be guarded.&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level."
        errorLine1="            return context.registerReceiver(receiver, filter,RECEIVER_EXPORTED);"
        errorLine2="                                                             ~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java"
            line="241"
            column="62"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`."
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                &lt;action android:name=&quot;android.net.conn.CONNECTIVITY_CHANGE&quot; />"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml"
            line="96"
            column="39"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`."
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                &lt;action android:name=&quot;android.net.conn.CONNECTIVITY_CHANGE&quot;/>"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml"
            line="110"
            column="39"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Declaring a broadcastreceiver for `android.net.conn.CONNECTIVITY_CHANGE` is deprecated for apps targeting N and higher. In general, apps should not rely on this broadcast and instead use `WorkManager`."
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                &lt;action android:name=&quot;android.net.conn.CONNECTIVITY_CHANGE&quot;/>"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml"
            line="120"
            column="39"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                Intent dozeIntent = new Intent(ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);"
        errorLine2="                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\impl\DefaultWhiteListProvider.java"
            line="56"
            column="48"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                        &amp;&amp; !p.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)"
        errorLine2="                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\LoginActivity.java"
            line="100"
            column="56"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="        else if(permission.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)){"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\MainActivity.java"
            line="350"
            column="44"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="        permissions.add(createPermissionModel(context,&quot;Battery Optimizations&quot;, Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS, false));"
        errorLine2="                                                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="66"
            column="89"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="        } else if(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS.equals(permission)){"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="78"
            column="28"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="199"
            column="53"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                } else if (permission.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)) {"
        errorLine2="                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="241"
            column="64"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="            (permissionModel.fullName != null &amp;&amp; Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS.equals(permissionModel.fullName))) {"
        errorLine2="                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PermissionsAdapter.java"
            line="51"
            column="59"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="                } else if (permission.fullName != null &amp;&amp; permission.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)) {"
        errorLine2="                                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\SettingsFragment.java"
            line="75"
            column="95"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="            if(p.fullName != null &amp;&amp; p.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)"
        errorLine2="                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java"
            line="68"
            column="65"/>
    </issue>

    <issue
        id="InflateParams"
        severity="Warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        category="Correctness"
        priority="5"
        summary="Layout Inflation without a Parent"
        explanation="When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored."
        url="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        urls="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        errorLine1="            floatRootView = LayoutInflater.from(this.context).inflate(R.layout.activity_float_item, null);"
        errorLine2="                                                                                                    ~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\FloatingWindow.java"
            line="88"
            column="101"/>
    </issue>

    <issue
        id="QueryPermissionsNeeded"
        severity="Warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details"
        category="Correctness"
        priority="5"
        summary="Using APIs affected by query permissions"
        explanation="Apps that target Android 11 cannot query or interact with other installed apps by default. If you need to query or interact with other installed apps, you may need to add a `&lt;queries>` declaration in your manifest.&#xA;&#xA;As a corollary, the methods `PackageManager#getInstalledPackages` and `PackageManager#getInstalledApplications` will no longer return information about all installed apps. To query specific apps or types of apps, you can use methods like `PackageManager#getPackageInfo` or `PackageManager#queryIntentActivities`."
        url="https://g.co/dev/packagevisibility"
        urls="https://g.co/dev/packagevisibility"
        errorLine1="        List&lt;ResolveInfo> list = pm.queryIntentActivities(mIntent, PackageManager.MATCH_DEFAULT_ONLY);"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteListIntentWrapper.java"
            line="74"
            column="37"/>
    </issue>

    <issue
        id="RedundantLabel"
        severity="Warning"
        message="Redundant label can be removed"
        category="Correctness"
        priority="5"
        summary="Redundant label on activity"
        explanation="When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted."
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        severity="Error"
        message="`pongReceiver` \&#xA;is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected \&#xA;broadcasts registered for ACTION_SOCKET_PONG"
        category="Correctness"
        priority="5"
        summary="Missing `registerReceiver()` exported flag"
        explanation="In Android U, all receivers registering for non-system broadcasts are required to include a flag indicating the receiver&apos;s exported state. Apps registering for non-system broadcasts should use the `ContextCompat#registerReceiver` APIs with flags set to either `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED`.&#xA;&#xA;If you are not expecting broadcasts from other apps on the device, register your receiver with `RECEIVER_NOT_EXPORTED` to protect your receiver on all platform releases."
        url="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        urls="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        errorLine1="        context.registerReceiver(pongReceiver, filter);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\androidTest\java\com\bm\atool\StressTest.java"
            line="104"
            column="9"/>
    </issue>

    <issue
        id="UnspecifiedRegisterReceiverFlag"
        severity="Warning"
        message="`receiver` \&#xA;is missing `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED` flag for unprotected \&#xA;broadcasts registered for an IntentFilter that cannot be inspected by lint"
        category="Correctness"
        priority="5"
        summary="Missing `registerReceiver()` exported flag"
        explanation="In Android U, all receivers registering for non-system broadcasts are required to include a flag indicating the receiver&apos;s exported state. Apps registering for non-system broadcasts should use the `ContextCompat#registerReceiver` APIs with flags set to either `RECEIVER_EXPORTED` or `RECEIVER_NOT_EXPORTED`.&#xA;&#xA;If you are not expecting broadcasts from other apps on the device, register your receiver with `RECEIVER_NOT_EXPORTED` to protect your receiver on all platform releases."
        url="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        urls="https://developer.android.com/reference/androidx/core/content/ContextCompat#registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,int)"
        errorLine1="            return context.registerReceiver(receiver, filter);"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java"
            line="244"
            column="20"/>
    </issue>

    <issue
        id="PxUsage"
        severity="Warning"
        message="Avoid using &quot;`px`&quot; as units; use &quot;`dp`&quot; instead"
        category="Correctness"
        priority="2"
        summary="Using &apos;px&apos; dimension"
        explanation="For performance reasons and to keep the code simpler, the Android system uses pixels as the standard unit for expressing dimension or coordinate values. That means that the dimensions of a view are always expressed in the code using pixels, but always based on the current screen density. For instance, if `myView.getWidth()` returns 10, the view is 10 pixels wide on the current screen, but on a device with a higher density screen, the value returned might be 15. If you use pixel values in your application code to work with bitmaps that are not pre-scaled for the current screen density, you might need to scale the pixel values that you use in your code to match the un-scaled bitmap source."
        url="https://developer.android.com/guide/practices/screens_support.html#screen-independence"
        urls="https://developer.android.com/guide/practices/screens_support.html#screen-independence"
        errorLine1="        app:tabIndicatorHeight=&quot;4px&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_main.xml"
            line="34"
            column="9"/>
    </issue>

    <issue
        id="UseAppTint"
        severity="Error"
        message="Must use `app:tint` instead of `android:tint`"
        category="Correctness"
        priority="1"
        summary="`app:tint` attribute should be used on `ImageView` and `ImageButton`"
        explanation="`ImageView` or `ImageButton` uses `android:tint` instead of `app:tint`"
        errorLine1="    android:tint=&quot;@color/check_mark_color&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml"
            line="25"
            column="5"/>
    </issue>

    <issue
        id="HardwareIds"
        severity="Warning"
        message="Using `getSubscriberId` to get device identifiers is not recommended"
        category="Security"
        priority="6"
        summary="Hardware Id Usage"
        explanation="Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use `AdvertisingIdClient$Info#getId` and for analytics, use `InstanceId#getId`."
        url="https://developer.android.com/training/articles/user-data-ids.html"
        urls="https://developer.android.com/training/articles/user-data-ids.html"
        errorLine1="            if (telephonyManager.getSubscriberId() != null) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PhoneUtils.java"
            line="43"
            column="17"/>
    </issue>

    <issue
        id="HardwareIds"
        severity="Warning"
        message="Using `getString` to get device identifiers is not recommended"
        category="Security"
        priority="6"
        summary="Hardware Id Usage"
        explanation="Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use `AdvertisingIdClient$Info#getId` and for analytics, use `InstanceId#getId`."
        url="https://developer.android.com/training/articles/user-data-ids.html"
        urls="https://developer.android.com/training/articles/user-data-ids.html"
        errorLine1="        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java"
            line="165"
            column="28"/>
    </issue>

    <issue
        id="HardwareIds"
        severity="Warning"
        message="Using `getSubscriberId` to get device identifiers is not recommended"
        category="Security"
        priority="6"
        summary="Hardware Id Usage"
        explanation="Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use `AdvertisingIdClient$Info#getId` and for analytics, use `InstanceId#getId`."
        url="https://developer.android.com/training/articles/user-data-ids.html"
        urls="https://developer.android.com/training/articles/user-data-ids.html"
        errorLine1="            subscriptionInfoModel.subscriptionId = String.valueOf(telephonyManager.getSubscriberId());"
        errorLine2="                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\model\SubscriptionInfoModel.java"
            line="126"
            column="67"/>
    </issue>

    <issue
        id="HardwareIds"
        severity="Warning"
        message="Using `getSimSerialNumber` to get device identifiers is not recommended"
        category="Security"
        priority="6"
        summary="Hardware Id Usage"
        explanation="Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use `AdvertisingIdClient$Info#getId` and for analytics, use `InstanceId#getId`."
        url="https://developer.android.com/training/articles/user-data-ids.html"
        urls="https://developer.android.com/training/articles/user-data-ids.html"
        errorLine1="            subscriptionInfoModel.iccId = telephonyManager.getSimSerialNumber();"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\model\SubscriptionInfoModel.java"
            line="142"
            column="43"/>
    </issue>

    <issue
        id="HardwareIds"
        severity="Warning"
        message="Using `getLine1Number` to get device identifiers is not recommended"
        category="Security"
        priority="6"
        summary="Hardware Id Usage"
        explanation="Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use `AdvertisingIdClient$Info#getId` and for analytics, use `InstanceId#getId`."
        url="https://developer.android.com/training/articles/user-data-ids.html"
        urls="https://developer.android.com/training/articles/user-data-ids.html"
        errorLine1="            subscriptionInfoModel.phoneNumber = telephonyManager.getLine1Number();"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\model\SubscriptionInfoModel.java"
            line="157"
            column="49"/>
    </issue>

    <issue
        id="UnsafeProtectedBroadcastReceiver"
        severity="Warning"
        message="This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver&apos;s onReceive method does not appear to call getAction to ensure that the received Intent&apos;s action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored."
        category="Security"
        priority="6"
        summary="Unsafe Protected `BroadcastReceiver`"
        explanation="`BroadcastReceiver`s that declare an intent-filter for a protected-broadcast action string must check that the received intent&apos;s action string matches the expected value, otherwise it is possible for malicious actors to spoof intents."
        url="https://goo.gle/UnsafeProtectedBroadcastReceiver"
        urls="https://goo.gle/UnsafeProtectedBroadcastReceiver"
        errorLine1="    public void onReceive(Context context, Intent intent) {"
        errorLine2="                ~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\receivers\SimChangedReceiver.java"
            line="13"
            column="17"/>
    </issue>

    <issue
        id="UnsafeProtectedBroadcastReceiver"
        severity="Warning"
        message="This broadcast receiver declares an intent-filter for a protected broadcast action string, which can only be sent by the system, not third-party applications. However, the receiver&apos;s onReceive method does not appear to call getAction to ensure that the received Intent&apos;s action string matches the expected value, potentially making it possible for another actor to send a spoofed intent with no action string or a different action string and cause undesired behavior. In this case, it is possible that the onReceive method passed the received Intent to another method that checked the action string. If so, this finding can safely be ignored."
        category="Security"
        priority="6"
        summary="Unsafe Protected `BroadcastReceiver`"
        explanation="`BroadcastReceiver`s that declare an intent-filter for a protected-broadcast action string must check that the received intent&apos;s action string matches the expected value, otherwise it is possible for malicious actors to spoof intents."
        url="https://goo.gle/UnsafeProtectedBroadcastReceiver"
        urls="https://goo.gle/UnsafeProtectedBroadcastReceiver"
        errorLine1="    public void onReceive(Context context,  Intent intent) {"
        errorLine2="                ~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\receivers\SmsReceiver.java"
            line="31"
            column="17"/>
    </issue>

    <issue
        id="ExportedService"
        severity="Warning"
        message="Exported service does not require permission"
        category="Security"
        priority="5"
        summary="Exported service does not require permission"
        explanation="Exported services (services which either set `exported=true` or contain an intent-filter and do not specify `exported=false`) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service."
        url="https://goo.gle/ExportedService"
        urls="https://goo.gle/ExportedService"
        errorLine1="        &lt;service"
        errorLine2="         ~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml"
            line="140"
            column="10"/>
    </issue>

    <issue
        id="SystemPermissionTypo"
        severity="Warning"
        message="Did you mean `android.permission.WRITE_OBB`?"
        category="Security"
        priority="5"
        summary="Permission appears to be a standard permission with a typo"
        explanation="This check looks for required permissions that *look* like well-known system permissions or permissions from the Android SDK, but aren&apos;t, and may be typos.&#xA;&#xA;Please double check the permission value you have supplied."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.WRITE_SMS&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml"
            line="15"
            column="36"/>
    </issue>

    <issue
        id="Wakelock"
        severity="Warning"
        message="Should not set both `PARTIAL_WAKE_LOCK` and `ACQUIRE_CAUSES_WAKEUP`. If you do not want the screen to turn on, get rid of `ACQUIRE_CAUSES_WAKEUP`"
        category="Performance"
        priority="9"
        summary="Incorrect `WakeLock` usage"
        explanation="Failing to release a wakelock properly can keep the Android device in a high power mode, which reduces battery life. There are several causes of this, such as releasing the wake lock in `onDestroy()` instead of in `onPause()`, failing to call `release()` in all possible code paths after an `acquire()`, and so on.&#xA;&#xA;NOTE: If you are using the lock just to keep the screen on, you should strongly consider using `FLAG_KEEP_SCREEN_ON` instead. This window flag will be correctly managed by the platform as the user moves between applications and doesn&apos;t require a special permission. See https://developer.android.com/reference/android/view/WindowManager.LayoutParams.html#FLAG_KEEP_SCREEN_ON."
        errorLine1="                wakeLock = powerManager.newWakeLock("
        errorLine2="                                        ~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\MainActivity.java"
            line="235"
            column="41"/>
    </issue>

    <issue
        id="WakelockTimeout"
        severity="Warning"
        message="Provide a timeout when requesting a wakelock with `PowerManager.Wakelock.acquire(long timeout)`. This will ensure the OS will cleanup any wakelocks that last longer than you intend, and will save your user&apos;s battery."
        category="Performance"
        priority="9"
        summary="Using wakeLock without timeout"
        explanation="Wakelocks have two acquire methods: one with a timeout, and one without. You should generally always use the one with a timeout. A typical timeout is 10 minutes. If the task takes longer than it is critical that it happens (i.e. can&apos;t use `JobScheduler`) then maybe they should consider a foreground service instead (which is a stronger run guarantee and lets the user know something long/important is happening)."
        errorLine1="            wakeLock.acquire();"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\MainActivity.java"
            line="238"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="        android:layout_centerVertical=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="        android:layout_centerVertical=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml"
            line="23"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="        android:layout_centerVertical=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml"
            line="34"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="        android:layout_centerVertical=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="            android:layout_centerVertical=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="            android:layout_centerVertical=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="            android:layout_centerVertical=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteLayoutParam"
        severity="Warning"
        message="Invalid layout param in a `LinearLayout`: `layout_centerVertical`"
        category="Performance"
        priority="6"
        summary="Obsolete layout params"
        explanation="The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed."
        errorLine1="            android:layout_centerVertical=&quot;true&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            String methodName = Build.VERSION.SDK_INT >= 18 ? &quot;currentProcessName&quot; : &quot;currentPackageName&quot;;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\App.java"
            line="101"
            column="33"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\views\BlackUnderlineEditText.java"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\impl\DefaultWhiteListProvider.java"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 21"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="@TargetApi(Build.VERSION_CODES.LOLLIPOP)"
        errorLine2="~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\JobSchedulerService.java"
            line="16"
            column="1"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="134"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="146"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="175"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PermissionUtils.java"
            line="198"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= 22 ) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PhoneUtils.java"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\utils\PhoneUtils.java"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\singlepixel\SinglePixelActivity.java"
            line="85"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is never &lt; 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP_MR1) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\SmsSender.java"
            line="110"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\UssdProcessor.java"
            line="82"
            column="25"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\UssdProcessor.java"
            line="96"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is never &lt; 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP_MR1) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\UssdProcessor.java"
            line="134"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchDogService.java"
            line="78"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchDogService.java"
            line="83"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchDogService.java"
            line="187"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            String methodName = Build.VERSION.SDK_INT >= 18 ? &quot;currentProcessName&quot; : &quot;currentPackageName&quot;;"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\receivers\WatchDogStatusReceiver.java"
            line="44"
            column="33"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                String methodName = Build.VERSION.SDK_INT >= 18 ? &quot;currentProcessName&quot; : &quot;currentPackageName&quot;;"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\WatchedService.java"
            line="59"
            column="37"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteList.java"
            line="95"
            column="25"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteList.java"
            line="142"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\xuexiang\keeplive\whitelist\WhiteList.java"
            line="173"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="This folder configuration (`v24`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `drawable`."
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder.">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-v24"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 21"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        &lt;item name=&quot;android:statusBarColor&quot; tools:targetApi=&quot;l&quot;>#000000&lt;/item> &lt;!-- Black -->"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-night\themes.xml"
            line="6"
            column="45"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 21"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        &lt;item name=&quot;android:statusBarColor&quot; tools:targetApi=&quot;l&quot;>#000000&lt;/item> &lt;!-- Black -->"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\themes.xml"
            line="6"
            column="45"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="This folder configuration (`v23`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `values`."
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder.">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-v23"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields (static reference to `ScreenManager` which has field `mContext` pointing to `Context`); this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private static ScreenManager sInstance;"
        errorLine2="            ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\singlepixel\ScreenManager.java"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields (static reference to `SocketServiceConnection` which has field `host` pointing to `Context`); this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private static SocketServiceConnection instance;"
        errorLine2="            ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketServiceConnection.java"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields (static reference to `FloatingWindow` which has field `floatRootView` pointing to `View`); this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="    private static FloatingWindow floatingWindow = null;"
        errorLine2="            ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\Sys.java"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="HandlerLeak"
        severity="Warning"
        message="This `Handler` class should be static or leaks might occur (com.bm.atool.service.SocketService.IncomingHandler)"
        category="Performance"
        priority="4"
        summary="Handler reference leaks"
        explanation="Since this Handler is declared as an inner class, it may prevent the outer class from being garbage collected. If the Handler is using a `Looper` or `MessageQueue` for a thread other than the main thread, then there is no issue. If the `Handler` is using the `Looper` or `MessageQueue` of the main thread, you need to fix your `Handler` declaration, as follows: Declare the `Handler` as a static class; In the outer class, instantiate a `WeakReference` to the outer class and pass this object to your `Handler` when you instantiate the `Handler`; Make all references to members of the outer class using the `WeakReference` object."
        errorLine1="    private class IncomingHandler extends Handler {"
        errorLine2="                  ~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\SocketService.java"
            line="269"
            column="19"/>
    </issue>

    <issue
        id="InefficientWeight"
        severity="Warning"
        message="Use a `layout_height` of `0dp` instead of `1dp` for better performance"
        category="Performance"
        priority="3"
        summary="Inefficient layout weight"
        explanation="When only a single widget in a `LinearLayout` defines a weight, it is more efficient to assign a width/height of `0dp` to it since it will absorb all the remaining space anyway. With a declared width/height of `0dp` it does not have to measure its own size first."
        errorLine1="        android:layout_height = &quot;1dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_main.xml"
            line="16"
            column="9"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#ffffff` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#ffffff&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/app_background_color` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/app_background_color&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/app_background_color` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/app_background_color&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/app_background_color` with a theme that also paints a background (inferred theme is `@style/Theme.AndroidTool`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/app_background_color&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.array.reply_entries` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string-array name=&quot;reply_entries&quot;>"
        errorLine2="                  ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\arrays.xml"
            line="3"
            column="19"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.array.reply_values` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string-array name=&quot;reply_values&quot;>"
        errorLine2="                  ~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\arrays.xml"
            line="8"
            column="19"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.black` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.fab_margin` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;fab_margin&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\dimens.xml"
            line="2"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_action_home` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-anydpi\ic_action_home.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.action_settings` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;action_settings&quot;>Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="3"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.first_fragment_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;first_fragment_label&quot;>First Fragment&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="5"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.second_fragment_label` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;second_fragment_label&quot;>Second Fragment&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.next` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;next&quot;>Next&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.previous` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;previous&quot;>Previous&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.lorem_ipsum` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;lorem_ipsum&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.title_activity_settings` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;title_activity_settings&quot;>SettingsActivity&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.messages_header` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;messages_header&quot;>Messages&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.sync_header` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;sync_header&quot;>Sync&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.signature_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;signature_title&quot;>Your signature&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.reply_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;reply_title&quot;>Default reply action&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.sync_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;sync_title&quot;>Sync email periodically&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.attachment_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;attachment_title&quot;>Download incoming attachments&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.attachment_summary_on` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;attachment_summary_on&quot;>Automatically download attachments for incoming emails"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.attachment_summary_off` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;attachment_summary_off&quot;>Only download attachments when manually requested&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values\strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Base_Theme_Snettool` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Base.Theme.Snettool&quot; parent=&quot;Theme.Material3.DayNight.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-night\themes.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Theme_Snettool` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Theme.Snettool&quot; parent=&quot;Base.Theme.Snettool&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\values-v23\themes.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UselessParent"
        severity="Warning"
        message="This `LinearLayout` layout or its `FrameLayout` parent is unnecessary; transfer the `background` attribute to the other view"
        category="Performance"
        priority="2"
        summary="Unnecessary parent layout"
        explanation="A layout with children that has no siblings, is not a scrollview or a root layout, and does not have a background, can be removed and have its children moved directly into the parent for a flatter and more efficient layout hierarchy."
        errorLine1="    &lt;LinearLayout"
        errorLine2="     ~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="10"
            column="6"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        xmlns:tools=&quot;http://schemas.android.com/tools&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="52"
            column="9"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="RedundantNamespace"
        severity="Warning"
        message="This namespace declaration is redundant"
        category="Performance"
        priority="1"
        summary="Redundant namespace"
        explanation="In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation."
        errorLine1="        xmlns:tools=&quot;http://schemas.android.com/tools&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="54"
            column="9"/>
    </issue>

    <issue
        id="UnusedNamespace"
        severity="Warning"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        category="Performance"
        priority="1"
        summary="Unused namespace"
        explanation="Unused namespace declarations take up space and require processing that is not necessary"
        errorLine1="        xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="UnusedNamespace"
        severity="Warning"
        message="Unused namespace declaration xmlns:app; already declared on the root element"
        category="Performance"
        priority="1"
        summary="Unused namespace"
        explanation="Unused namespace declarations take up space and require processing that is not necessary"
        errorLine1="        xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="UnusedNamespace"
        severity="Warning"
        message="Unused namespace declaration xmlns:tools; already declared on the root element"
        category="Performance"
        priority="1"
        summary="Unused namespace"
        explanation="Unused namespace declarations take up space and require processing that is not necessary"
        errorLine1="        xmlns:tools=&quot;http://schemas.android.com/tools&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="UnusedNamespace"
        severity="Warning"
        message="Unused namespace declaration xmlns:android; already declared on the root element"
        category="Performance"
        priority="1"
        summary="Unused namespace"
        explanation="Unused namespace declarations take up space and require processing that is not necessary"
        errorLine1="        xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="52"
            column="9"/>
    </issue>

    <issue
        id="UnusedNamespace"
        severity="Warning"
        message="Unused namespace declaration xmlns:app; already declared on the root element"
        category="Performance"
        priority="1"
        summary="Unused namespace"
        explanation="Unused namespace declarations take up space and require processing that is not necessary"
        errorLine1="        xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="UnusedNamespace"
        severity="Warning"
        message="Unused namespace declaration xmlns:tools; already declared on the root element"
        category="Performance"
        priority="1"
        summary="Unused namespace"
        explanation="Unused namespace declarations take up space and require processing that is not necessary"
        errorLine1="        xmlns:tools=&quot;http://schemas.android.com/tools&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="54"
            column="9"/>
    </issue>

    <issue
        id="IconXmlAndPng"
        severity="Warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\drawable-anydpi\ic_action_home.xml, src\main\res\drawable-hdpi\ic_action_home.png"
        category="Usability:Icons"
        priority="7"
        summary="Icon is specified both as `.xml` file and as a bitmap"
        explanation="If a drawable resource appears as an `.xml` file in the `drawable/` folder, it&apos;s usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxhdpi\ic_action_home.png"/>
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xhdpi\ic_action_home.png"/>
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-mdpi\ic_action_home.png"/>
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-hdpi\ic_action_home.png"/>
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-anydpi\ic_action_home.xml"/>
    </issue>

    <issue
        id="IconColors"
        severity="Warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes"
        category="Usability:Icons"
        priority="6"
        summary="Icon colors do not follow the recommended visual style"
        explanation="Notification icons and Action Bar icons should only white and shades of gray. See the Android Design Guide for more details. Note that the way Lint decides whether an icon is an action bar icon or a notification icon is based on the filename prefix: `ic_menu_` for action bar icons, `ic_stat_` for notification icons etc. These correspond to the naming conventions documented in https://d.android.com/r/studio-ui/designer/material/iconography">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-hdpi\ic_action_ok.png"/>
    </issue>

    <issue
        id="IconColors"
        severity="Warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes"
        category="Usability:Icons"
        priority="6"
        summary="Icon colors do not follow the recommended visual style"
        explanation="Notification icons and Action Bar icons should only white and shades of gray. See the Android Design Guide for more details. Note that the way Lint decides whether an icon is an action bar icon or a notification icon is based on the filename prefix: `ic_menu_` for action bar icons, `ic_stat_` for notification icons etc. These correspond to the naming conventions documented in https://d.android.com/r/studio-ui/designer/material/iconography">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-mdpi\ic_action_ok.png"/>
    </issue>

    <issue
        id="IconColors"
        severity="Warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes"
        category="Usability:Icons"
        priority="6"
        summary="Icon colors do not follow the recommended visual style"
        explanation="Notification icons and Action Bar icons should only white and shades of gray. See the Android Design Guide for more details. Note that the way Lint decides whether an icon is an action bar icon or a notification icon is based on the filename prefix: `ic_menu_` for action bar icons, `ic_stat_` for notification icons etc. These correspond to the naming conventions documented in https://d.android.com/r/studio-ui/designer/material/iconography">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xhdpi\ic_action_ok.png"/>
    </issue>

    <issue
        id="IconColors"
        severity="Warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes"
        category="Usability:Icons"
        priority="6"
        summary="Icon colors do not follow the recommended visual style"
        explanation="Notification icons and Action Bar icons should only white and shades of gray. See the Android Design Guide for more details. Note that the way Lint decides whether an icon is an action bar icon or a notification icon is based on the filename prefix: `ic_menu_` for action bar icons, `ic_stat_` for notification icons etc. These correspond to the naming conventions documented in https://d.android.com/r/studio-ui/designer/material/iconography">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxhdpi\ic_action_ok.png"/>
    </issue>

    <issue
        id="IconColors"
        severity="Warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes"
        category="Usability:Icons"
        priority="6"
        summary="Icon colors do not follow the recommended visual style"
        explanation="Notification icons and Action Bar icons should only white and shades of gray. See the Android Design Guide for more details. Note that the way Lint decides whether an icon is an action bar icon or a notification icon is based on the filename prefix: `ic_menu_` for action bar icons, `ic_stat_` for notification icons etc. These correspond to the naming conventions documented in https://d.android.com/r/studio-ui/designer/material/iconography">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable-xxxhdpi\ic_action_ok.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/debug.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\debug.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/debug_select.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\debug_select.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/home.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\home.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/home_select.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\home_select.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/setting.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\setting.png"/>
    </issue>

    <issue
        id="IconLocation"
        severity="Warning"
        message="Found bitmap drawable `res/drawable/setting_select.png` in densityless folder"
        category="Usability:Icons"
        priority="5"
        summary="Image defined in density-independent drawable folder"
        explanation="The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to `drawable-mdpi` and consider providing higher and lower resolution versions in `drawable-ldpi`, `drawable-hdpi` and `drawable-xhdpi`. If the icon **really** is density independent (for example a solid color) you can place it in `drawable-nodpi`."
        url="https://developer.android.com/guide/practices/screens_support.html"
        urls="https://developer.android.com/guide/practices/screens_support.html">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\drawable\setting_select.png"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="57"
            column="14"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="67"
            column="14"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="95"
            column="14"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="106"
            column="14"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="118"
            column="14"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml"
            line="54"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml"
            line="65"
            column="18"/>
    </issue>

    <issue
        id="TextFields"
        severity="Warning"
        message="This text field does not specify an `inputType`"
        category="Usability"
        priority="5"
        summary="Missing `inputType`"
        explanation="Providing an `inputType` attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). &#xA;&#xA;The lint detector also looks at the `id` of the view, and if the id offers a hint of the purpose of the field (for example, the `id` contains the phrase `phone` or `email`), then lint will also ensure that the `inputType` contains the corresponding type attributes.&#xA;&#xA;If you really want to keep the text field generic, you can suppress this warning by setting `inputType=&quot;text&quot;`."
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="27"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="27"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="48"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="36"
            column="10"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="`ItemViewTouchListener#onTouch` should call `View#performClick` when a click is detected"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="    public boolean onTouch(View v, MotionEvent motionEvent) {"
        errorLine2="                   ~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\ItemViewTouchListener.java"
            line="30"
            column="20"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_float_item.xml"
            line="7"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="25"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="46"
            column="14"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="&lt;ImageView"
        errorLine2=" ~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml"
            line="17"
            column="2"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="19"
            column="10"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvJSResult.setText(&quot;No result yet...&quot;);"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\DebugFragment.java"
            line="157"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        holder.txtSlot.setText(&quot;PHONE&quot;);"
        errorLine2="                               ~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PhoneAdapter.java"
            line="56"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        holder.txtSubscriptionId.setText(&quot;ID:&quot; + currentItem.subscriptionId);"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PhoneAdapter.java"
            line="58"
            column="42"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        holder.txtSubscriptionId.setText(&quot;ID:&quot; + currentItem.subscriptionId);"
        errorLine2="                                         ~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\adapters\PhoneAdapter.java"
            line="58"
            column="42"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;User Name&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;User Name&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="24"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Login ID&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;Login ID&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Password&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Password&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="45"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Password&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;Password&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="57"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Login&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Login&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="68"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Debug &amp; JavaScript Test&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Debug &amp;amp; JavaScript Test&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;JavaScript Test&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;JavaScript Test&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Enter JavaScript code here...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:hint=&quot;Enter JavaScript code here...&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Execute&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Execute&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="64"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Clear&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Clear&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="74"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Quick Tests&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Quick Tests&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="83"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Device Info&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Device Info&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="102"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Phone Numbers&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Phone Numbers&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="113"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Battery&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Battery&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="125"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Execution Result&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Execution Result&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="135"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;No result yet...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;No result yet...&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="151"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;System Control&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;System Control&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="158"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Stop All Services&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Stop All Services&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_debug.xml"
            line="173"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;username&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;username&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="40"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;SMS:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;SMS:&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="88"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Permissions&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Permissions&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml"
            line="25"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Logout&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Logout&quot; />"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml"
            line="63"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Exit&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Exit&quot; />"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_settings.xml"
            line="74"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入手机号码&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:hint=&quot;请输入手机号码&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\phone_row.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Android 6.0&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="    android:text=&quot;Android 6.0&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml"
            line="13"
            column="5"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Grant&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Grant&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="RtlSymmetry"
        severity="Warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry"
        category="Internationalization:Bidirectional Text"
        priority="6"
        summary="Padding and margin symmetry"
        explanation="If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry."
        errorLine1="            android:paddingLeft=&quot;6dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.END`&quot; instead of &quot;`Gravity.RIGHT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        layoutParam.gravity = Gravity.RIGHT;"
        errorLine2="                                      ~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\ui\FloatingWindow.java"
            line="68"
            column="39"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`Gravity.START`&quot; instead of &quot;`Gravity.LEFT`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        mWindow.setGravity(Gravity.LEFT | Gravity.TOP);"
        errorLine2="                                   ~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\java\com\bm\atool\service\singlepixel\SinglePixelActivity.java"
            line="37"
            column="36"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_marginLeft=&quot;5dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;5dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_marginLeft=&quot;5dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\activity_login.xml"
            line="56"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            android:gravity=&quot;left|center_vertical&quot;"
        errorLine2="                             ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="20"
            column="30"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;0dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:layout_marginLeft=&quot;0dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\fragment_main.xml"
            line="30"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="    android:layout_alignParentRight=&quot;true&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml"
            line="21"
            column="5"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Redundant attribute `layout_alignParentRight`; already defining `layout_alignParentEnd` with `targetSdkVersion` 34"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_alignParentRight=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\setting_row.xml"
            line="33"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;0dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            android:layout_marginLeft=&quot;0dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;6dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            android:paddingLeft=&quot;6dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;48dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            android:layout_marginLeft=&quot;48dp&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\res\layout\sms_row.xml"
            line="65"
            column="13"/>
    </issue>

</issues>
