#!/bin/bash

echo "🔍 验证JavaScript引擎修复..."
echo "================================"

# 检查AndroidBridge.java是否包含接口定义
echo "1. 检查AndroidBridgeInterface接口定义..."

if grep -q "interface AndroidBridgeInterface" app/src/main/java/com/bm/atool/js/AndroidBridge.java; then
    echo "✅ AndroidBridgeInterface 接口已定义"
else
    echo "❌ AndroidBridgeInterface 接口未找到"
    exit 1
fi

# 检查AndroidBridge是否实现了接口
echo "2. 检查AndroidBridge接口实现..."

if grep -q "implements AndroidBridgeInterface" app/src/main/java/com/bm/atool/js/AndroidBridge.java; then
    echo "✅ AndroidBridge 已实现接口"
else
    echo "❌ AndroidBridge 未实现接口"
    exit 1
fi

# 检查JavaScriptEngine是否使用接口绑定
echo "3. 检查JavaScriptEngine接口绑定..."

if grep -q "AndroidBridgeInterface.class" app/src/main/java/com/bm/atool/js/JavaScriptEngine.java; then
    echo "✅ JavaScriptEngine 使用接口绑定"
else
    echo "❌ JavaScriptEngine 未使用接口绑定"
    exit 1
fi

# 检查console对象是否正确设置
echo "4. 检查console对象设置..."

if grep -q "Android.log('INFO'" app/src/main/java/com/bm/atool/js/JavaScriptEngine.java; then
    echo "✅ console对象正确设置"
else
    echo "❌ console对象设置不正确"
    exit 1
fi

# 检查编译是否成功
echo "5. 检查编译状态..."

if [ -f "app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/bm/atool/js/AndroidBridge.class" ]; then
    echo "✅ AndroidBridge 编译成功"
else
    echo "⚠️  AndroidBridge 编译文件未找到（可能需要重新编译）"
fi

if [ -f "app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/bm/atool/js/JavaScriptEngine.class" ]; then
    echo "✅ JavaScriptEngine 编译成功"
else
    echo "⚠️  JavaScriptEngine 编译文件未找到（可能需要重新编译）"
fi

echo ""
echo "🎉 JavaScript引擎修复验证完成！"
echo ""
echo "修复内容总结："
echo "- ✅ 创建了AndroidBridgeInterface接口"
echo "- ✅ AndroidBridge类实现了接口"
echo "- ✅ JavaScriptEngine使用接口绑定而不是类绑定"
echo "- ✅ 修复了console.log方法调用"
echo "- ✅ 所有方法都使用QuickJS支持的数据类型"
echo ""
echo "这应该解决了以下错误："
echo "- UnsupportedOperationException: Only interfaces can be bound"
echo "- JavaScript引擎初始化失败的问题"
echo ""
echo "现在可以重新运行应用程序测试JavaScript引擎功能。"
