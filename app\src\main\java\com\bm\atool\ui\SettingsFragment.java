package com.bm.atool.ui;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.provider.Settings;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bm.atool.LoginActivity;
import com.bm.atool.R;
import com.bm.atool.Sys;
import com.bm.atool.events.EventSource;
import com.bm.atool.model.PermissionModel;
import com.bm.atool.ui.adapters.PermissionsAdapter;
import com.bm.atool.utils.PermissionUtils;

import java.util.ArrayList;
import java.util.Objects;

public class SettingsFragment extends BaseFragment {
    private static final String TAG = SettingsFragment.class.getSimpleName();
    
    // Initialize variable
    private RecyclerView permissionsListView;
    private Button btnLogout;
    private Button btnExit;
    private EventSource.IEventListener<ArrayList<PermissionModel>> permissionChangeEventListener = new EventSource.IEventListener<ArrayList<PermissionModel>>() {
        @Override
        public void onEvent(ArrayList<PermissionModel> event) {
            if(Objects.nonNull(permissionsListView)){
                permissionsListView.setAdapter(new PermissionsAdapter(Sys.permissionModels,SettingsFragment.this));
            }
        }
    };

    public SettingsFragment(){
        super();
        this.setTitle(" SETTINGS ");

        Sys.permissionsEventSource.addEventListener(permissionChangeEventListener);
    }

    /**
     * 自动请求所需的权限
     * 此方法主要用于特殊权限的跳转，运行时权限由LoginActivity处理
     */
    public void requestPermissionsAutomatically() {
        Log.d(TAG, "requestPermissionsAutomatically: Automatically requesting permissions");
        if (Sys.permissionModels == null || getActivity() == null) {
            return;
        }
        
        // 查找第一个未授予的特殊权限并跳转
        for (PermissionModel permission : Sys.permissionModels) {
            if (!permission.granted && !permission.optional) {
                if (permission.name.equals("NotificationListener")) {
                    Log.d(TAG, "requestPermissionsAutomatically: Requesting Notification Listener permission.");
                    PermissionUtils.notificationListenerToSettingPage(getActivity());
                    return;
                } else if (permission.name.equals("Accessibility")) {
                    Log.d(TAG, "requestPermissionsAutomatically: Requesting Accessibility permission.");
                    PermissionUtils.accessibilityToSettingPage(getActivity());
                    return;
                } else if (permission.fullName != null && permission.fullName.equals(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)) {
                    Log.d(TAG, "requestPermissionsAutomatically: Requesting Manage Overlay permission.");
                    PermissionUtils.manageOverlayToSettingPage(getActivity());
                    return;
                } else if (permission.fullName != null && permission.fullName.equals(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)) {
                    Log.d(TAG, "requestPermissionsAutomatically: Requesting Battery Optimizations permission.");
                    PermissionUtils.batteryOptimizationToSettingPage(getActivity());
                    return;
                }
                // 对于其他运行时权限，不在这里自动跳转，由LoginActivity处理
            }
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Initialize view
        View view =inflater.inflate(R.layout.fragment_settings, container, false);

        // Assign variable
        permissionsListView=view.findViewById(R.id.permissionsListView);
        permissionsListView.setLayoutManager(new LinearLayoutManager(this.getContext())); ;
        permissionsListView.setAdapter(new PermissionsAdapter(Sys.permissionModels,SettingsFragment.this));
        // return view
        btnLogout = view.findViewById(R.id.btnLogout);
        btnLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Sys.updateLogin("","");
                Sys.stop();
                getActivity().startActivity(new Intent(getContext(), LoginActivity.class));
            }
        });

        btnExit = view.findViewById(R.id.btnExit);
        btnExit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Sys.stop();
                btnExit.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        System.exit(0);
                    }
                },1000L);

            }
        });
        
        // 仅在onCreate时刷新一次权限列表，不在此处自动请求权限，避免与LoginActivity冲突
        // permissionsListView.setAdapter(new PermissionsAdapter(Sys.permissionModels,SettingsFragment.this));
        
        return view;
    }

    @Override
    public void onDestroy() {
        Sys.permissionsEventSource.removeEventListener(this.permissionChangeEventListener);
        super.onDestroy();
    }

    @Override
    public void onResume() {
        super.onResume();

        Sys.permissionModels = PermissionUtils.getAllPermissions(getActivity());
        
        // 当设置页面显示时，刷新权限列表并自动请求特殊权限
        if (permissionsListView != null) {
            permissionsListView.setAdapter(new PermissionsAdapter(Sys.permissionModels, this));
        }
        
        // 延迟一会儿后自动请求特殊权限
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                requestPermissionsAutomatically();
            }
        }, 500);
    }

    @Override
    public int getIconResourceId() {
        return R.drawable.tab_icon_settings;
    }
}