package com.bm.atool.service;

import android.annotation.SuppressLint;
import android.app.Application;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.bm.atool.Sys;
import com.bm.atool.events.EventSource;
import com.bm.atool.receivers.AccessibilityReceiver;

import java.lang.reflect.Method;

public abstract class WatchedService extends Service {
    private final String TAG = WatchedService.class.getSimpleName();

    private EventSource.IEventListener<Boolean> watchDogEventListener = new EventSource.IEventListener<Boolean>() {
        @Override
        public void onEvent(Boolean enabled) {
            Log.e(TAG, "on watchDogEventListener.onEvent:" + enabled.toString());
            if(!enabled){
                stopService();
            }
        }
    };
    private BroadcastReceiver startWatchReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {

            Log.e(TAG, "StartWatchReceiver.onReceive: " + intent.getAction() + "process: " + getProcessName()
                    + "Thread: " + Thread.currentThread().getName() + "[" + Thread.currentThread().getId() + "]");
            if (Sys.ACTION_DAEMON_ENABLED.equals(intent.getAction())) {
                Boolean enableWatchDog = intent.getBooleanExtra(NotificationCompat.CATEGORY_STATUS, false);
                Sys.updateWatchDogEnabled(enableWatchDog);
            }
        }

        private String getProcessName() {
            if (Build.VERSION.SDK_INT >= 28)
                return Application.getProcessName();

            // Using the same technique as Application.getProcessName() for older devices
            // Using reflection since ActivityThread is an internal API

            try {
                @SuppressLint("PrivateApi")
                Class<?> activityThread = Class.forName("android.app.ActivityThread");

                // Before API 18, the method was incorrectly named "currentPackageName", but it still returned the process name
                // See https://github.com/aosp-mirror/platform_frameworks_base/commit/b57a50bd16ce25db441da5c1b63d48721bb90687
                String methodName = Build.VERSION.SDK_INT >= 18 ? "currentProcessName" : "currentPackageName";
                Method getProcessName = activityThread.getDeclaredMethod(methodName);
                return (String) getProcessName.invoke(null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return "";
        }
    };
    private AccessibilityReceiver accessibilityReceiver = new AccessibilityReceiver();
    protected abstract void stopService();

    @Override
    public void onCreate() {
        super.onCreate();
        Sys.registerReceiver(this, this.accessibilityReceiver,new IntentFilter(Sys.ACTION_ACCESSIBILITY_SERVICE_STATUS_CHANGED));
        Sys.registerReceiver(this,this.startWatchReceiver,new IntentFilter(Sys.ACTION_DAEMON_ENABLED));
        Sys.watchDogEventSource.addEventListener(watchDogEventListener);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Sys.watchDogEventSource.removeEventListener(watchDogEventListener);
        unregisterReceiver(this.accessibilityReceiver);
        unregisterReceiver(this.startWatchReceiver);
    }
}
